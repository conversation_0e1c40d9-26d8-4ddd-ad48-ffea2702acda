# Required
ANTHROPIC_API_KEY= # For most AI ops -- Format: sk-ant-api03-... (Required)
PERPLEXITY_API_KEY=      # For research -- Format: pplx-abcde (Optional, Highly Recommended)

# Optional - defaults shown
MODEL=claude-3-7-sonnet-20250219  # Recommended models: claude-3-7-sonnet-20250219, claude-3-opus-20240229 (Required)
PERPLEXITY_MODEL=sonar-pro        # Make sure you have access to sonar-pro otherwise you can use sonar regular (Optional)
MAX_TOKENS=64000                   # Maximum tokens for model responses (Required)
TEMPERATURE=0.2                   # Temperature for model responses (0.0-1.0) - lower = less creativity and follow your prompt closely (Required)
DEBUG=true                     # Enable debug logging (true/false)
LOG_LEVEL=info                    # Log level (debug, info, warn, error)
DEFAULT_SUBTASKS=5                # Default number of subtasks when expanding
DEFAULT_PRIORITY=medium           # Default priority for generated tasks (high, medium, low)
PROJECT_NAME=AI Navigator      # Project name for tasks.json metadata

# .env - Environment variables for AI Navigator
# IMPORTANT: Add this file to your .gitignore!

# --- General ---
NODE_ENV=production # Or 'production'

# --- Supabase ---
# Find these in your Supabase project settings (API section)
NEXT_PUBLIC_SUPABASE_URL=https://jeoozbxohegxbfgclvum.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY= #supabase anon key

SUPABASE_ANON_KEY=

# REQUIRED for backend/admin operations (NestJS, Scraper, Migrations) - KEEP SECRET
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# --- Database (PostgreSQL via Supabase) ---
# Direct connection string for backend services (NestJS/Prisma, Python Scraper)
# Format: postgresql://postgres:[YOUR-PASSWORD]@[HOST]:[PORT]/postgres
DATABASE_URL= #db url

SUPABASE_URL=https://qpkllizwchhppvqsnagl.supabase.co

FRONTEND_BASE_URL=
# OAuth Configuration
NEXT_PUBLIC_GOOGLE_CLIENT_ID= #client id
GOOGLE_CLIENT_SECRET= #Client secret
NEXT_PUBLIC_SITE_URL="http://localhost:3000"  # Development
# NEXT_PUBLIC_SITE_URL="https://your-production-domain.com"  # Production
# --- Backend API (NestJS) ---
PORT=3000 # Or your preferred port
JWT_SECRET= #jwt seceret
FRONTEND_URL=http://localhost:3000 # Adjust for production

# --- Frontend (Next.js) ---
# Needs NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY (defined above)
NEXT_PUBLIC_BACKEND_API_URL=http://localhost:3001 # URL of your NestJS backend

# --- LLM APIs ---
# Keys and configurations for various LLM providers.
# Your backend application will need access to these, but the ACTIVE selection
# should be managed dynamically (e.g., via a database setting controlled by the admin dashboard).
# Keep ALL API keys SECRET and access them ONLY from your secure backend.

# Groq
# https://console.groq.com/keys
GROQ_API_KEY=your_groq_api_key

# HuggingFace
# https://huggingface.co/settings/tokens
HUGGINGFACE_API_KEY=your_huggingface_api_key # Often referred to as HF_TOKEN

# OpenAI
# https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key

# Anthropic
# https://console.anthropic.com/settings/keys
ANTHROPIC_API_KEY=your_anthropic_api_key

# OpenRouter
# https://openrouter.ai/settings/keys
OPENROUTER_API_KEY=your_openrouter_api_key

# Google Generative AI (Gemini)
# https://aistudio.google.com/app/apikey or GCP credentials
GOOGLE_GENERATIVE_AI_API_KEY=your_google_gemini_api_key

# Ollama (Self-hosted/Local)
# Use 127.0.0.1 instead of localhost for IPV6 compatibility if needed
OLLAMA_API_BASE_URL=http://127.0.0.1:11434 # Or your Ollama server address

# OpenAI-Compatible APIs (e.g., VLLM, TGI, Anyscale, Fireworks)
# Base URL for the compatible endpoint
OPENAI_LIKE_API_BASE_URL=your_openai_compatible_base_url
# API Key if required by the compatible endpoint
OPENAI_LIKE_API_KEY=your_openai_compatible_api_key

# Together AI
# https://api.together.ai/settings/api-keys
TOGETHER_API_KEY=your_together_api_key
TOGETHER_API_BASE_URL=https://api.together.xyz/v1 # Default, may not be needed if using SDK

# DeepSeek
# Get from DeepSeek platform
DEEPSEEK_API_KEY=your_deepseek_api_key

# Hyperbolic
# https://app.hyperbolic.xyz/settings
HYPERBOLIC_API_KEY=your_hyperbolic_api_key
HYPERBOLIC_API_BASE_URL=https://api.hyperbolic.xyz/v1 # Example, confirm if needed

# Mistral AI
# https://console.mistral.ai/api-keys/
MISTRAL_API_KEY=your_mistral_api_key

# Cohere
# https://dashboard.cohere.com/api-keys
COHERE_API_KEY=your_cohere_api_key

# LM Studio (Local)
# Enable CORS and get from LM Studio Developer Console
# Use 127.0.0.1 instead of localhost for IPV6 compatibility if needed
LMSTUDIO_API_BASE_URL=http://127.0.0.1:1234 # Or your LM Studio server address

# xAI (Grok)
# https://x.ai/api
XAI_API_KEY=your_xai_api_key

# Perplexity
# https://www.perplexity.ai/settings/api
PERPLEXITY_API_KEY=your_perplexity_api_key

# AWS Bedrock
# https://console.aws.amazon.com/iam/home
# Provide configuration as a JSON string or configure via standard AWS SDK environment variables (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION, AWS_SESSION_TOKEN)
# Example JSON string: '{"region": "us-east-1", "accessKeyId": "...", "secretAccessKey": "...", "sessionToken": "..."}'
AWS_BEDROCK_CONFIG= # Optional: JSON config string
# Alternatively set standard AWS env vars:
# AWS_ACCESS_KEY_ID=your_aws_access_key_id
# AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
# AWS_REGION=your_aws_bedrock_region
# AWS_SESSION_TOKEN=your_aws_session_token # If using temporary credentials

# --- Background Jobs (BullMQ / Redis) ---
REDIS_HOST=localhost
REDIS_PORT=6379
# REDIS_PASSWORD=your_redis_password
# REDIS_URL=redis://:your_redis_password@localhost:6379

# --- Scraping Service (Python) ---
# Needs DATABASE_URL (defined above)
# Needs necessary LLM API Keys (defined above)
# SCRAPER_USER_AGENT="AINavigatorBot/1.0 (+https://yourdomain.com/bot)"
# SCRAPER_DELAY_SECONDS=5

# --- Other Optional Config ---
# Log level for frontend (if using Vite/similar) or backend
# VITE_LOG_LEVEL=debug # Example for Vite frontend
LOG_LEVEL=info # Example for backend

# Default context window size (example, usage depends on implementation)
DEFAULT_NUM_CTX= # e.g., 4096