import { EntitiesService } from '../../entities/entities.service';
import { AdminUpdateEntityStatusDto } from './dto/admin-update-entity-status.dto';
import { Entity as EntityModel } from '../../../generated/prisma';
export declare class AdminEntitiesController {
    private readonly entitiesService;
    constructor(entitiesService: EntitiesService);
    updateEntityStatus(id: string, adminUpdateEntityStatusDto: AdminUpdateEntityStatusDto): Promise<EntityModel>;
}
