"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminEntitiesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const admin_guard_1 = require("../../auth/guards/admin.guard");
const entities_service_1 = require("../../entities/entities.service");
const admin_update_entity_status_dto_1 = require("./dto/admin-update-entity-status.dto");
let AdminEntitiesController = class AdminEntitiesController {
    constructor(entitiesService) {
        this.entitiesService = entitiesService;
    }
    async updateEntityStatus(id, adminUpdateEntityStatusDto) {
        return this.entitiesService.adminSetStatus(id, adminUpdateEntityStatusDto.status);
    }
};
exports.AdminEntitiesController = AdminEntitiesController;
__decorate([
    (0, common_1.Patch)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Admin: Update entity status' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, admin_update_entity_status_dto_1.AdminUpdateEntityStatusDto]),
    __metadata("design:returntype", Promise)
], AdminEntitiesController.prototype, "updateEntityStatus", null);
exports.AdminEntitiesController = AdminEntitiesController = __decorate([
    (0, swagger_1.ApiTags)('Admin - Entities'),
    (0, common_1.Controller)('admin/entities'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [entities_service_1.EntitiesService])
], AdminEntitiesController);
//# sourceMappingURL=admin-entities.controller.js.map