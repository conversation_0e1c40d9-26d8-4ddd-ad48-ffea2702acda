import { AdminSettingsService } from './admin-settings.service';
import { UpdateLlmProviderDto } from './dto/update-llm-provider.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';
import { AppSettingResponseDto } from './dto/app-setting-response.dto';
export declare class AdminSettingsController {
    private readonly adminSettingsService;
    constructor(adminSettingsService: AdminSettingsService);
    getAllSettings(): Promise<AppSettingResponseDto[]>;
    getLlmProvider(): Promise<AppSettingResponseDto>;
    updateLlmProvider(updateLlmProviderDto: UpdateLlmProviderDto): Promise<AppSettingResponseDto>;
    getSetting(key: string): Promise<AppSettingResponseDto>;
    updateSetting(key: string, updateSettingDto: UpdateSettingDto): Promise<AppSettingResponseDto>;
    deleteSetting(key: string): Promise<void>;
}
