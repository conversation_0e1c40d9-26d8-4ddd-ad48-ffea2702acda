"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminSettingsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const throttler_1 = require("@nestjs/throttler");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const admin_guard_1 = require("../../auth/guards/admin.guard");
const admin_settings_service_1 = require("./admin-settings.service");
const update_llm_provider_dto_1 = require("./dto/update-llm-provider.dto");
const update_setting_dto_1 = require("./dto/update-setting.dto");
const app_setting_response_dto_1 = require("./dto/app-setting-response.dto");
let AdminSettingsController = class AdminSettingsController {
    constructor(adminSettingsService) {
        this.adminSettingsService = adminSettingsService;
    }
    async getAllSettings() {
        return this.adminSettingsService.getAllSettings();
    }
    async getLlmProvider() {
        return this.adminSettingsService.getLlmProvider();
    }
    async updateLlmProvider(updateLlmProviderDto) {
        return this.adminSettingsService.updateLlmProvider(updateLlmProviderDto);
    }
    async getSetting(key) {
        return this.adminSettingsService.getSetting(key);
    }
    async updateSetting(key, updateSettingDto) {
        return this.adminSettingsService.updateSetting(key, updateSettingDto.value, updateSettingDto.description);
    }
    async deleteSetting(key) {
        return this.adminSettingsService.deleteSetting(key);
    }
};
exports.AdminSettingsController = AdminSettingsController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all application settings (Admin only)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of all application settings',
        type: [app_setting_response_dto_1.AppSettingResponseDto],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminSettingsController.prototype, "getAllSettings", null);
__decorate([
    (0, common_1.Get)('llm'),
    (0, swagger_1.ApiOperation)({ summary: 'Get current LLM provider setting (Admin only)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Current LLM provider setting',
        type: app_setting_response_dto_1.AppSettingResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'LLM provider setting not found',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AdminSettingsController.prototype, "getLlmProvider", null);
__decorate([
    (0, common_1.Put)('llm'),
    (0, throttler_1.Throttle)({ default: { limit: 10, ttl: 60000 } }),
    (0, swagger_1.ApiOperation)({ summary: 'Update LLM provider setting (Admin only)' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'LLM provider setting updated successfully',
        type: app_setting_response_dto_1.AppSettingResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid LLM provider',
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [update_llm_provider_dto_1.UpdateLlmProviderDto]),
    __metadata("design:returntype", Promise)
], AdminSettingsController.prototype, "updateLlmProvider", null);
__decorate([
    (0, common_1.Get)(':key'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific setting by key (Admin only)' }),
    (0, swagger_1.ApiParam)({
        name: 'key',
        description: 'The setting key',
        example: 'CURRENT_LLM_PROVIDER',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Setting retrieved successfully',
        type: app_setting_response_dto_1.AppSettingResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Setting not found',
    }),
    __param(0, (0, common_1.Param)('key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminSettingsController.prototype, "getSetting", null);
__decorate([
    (0, common_1.Put)(':key'),
    (0, throttler_1.Throttle)({ default: { limit: 20, ttl: 60000 } }),
    (0, swagger_1.ApiOperation)({ summary: 'Update a specific setting by key (Admin only)' }),
    (0, swagger_1.ApiParam)({
        name: 'key',
        description: 'The setting key',
        example: 'CURRENT_LLM_PROVIDER',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Setting updated successfully',
        type: app_setting_response_dto_1.AppSettingResponseDto,
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Param)('key')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_setting_dto_1.UpdateSettingDto]),
    __metadata("design:returntype", Promise)
], AdminSettingsController.prototype, "updateSetting", null);
__decorate([
    (0, common_1.Delete)(':key'),
    (0, throttler_1.Throttle)({ default: { limit: 5, ttl: 60000 } }),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a setting by key (Admin only)' }),
    (0, swagger_1.ApiParam)({
        name: 'key',
        description: 'The setting key',
        example: 'SOME_CUSTOM_SETTING',
    }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Setting deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Setting not found',
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('key')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminSettingsController.prototype, "deleteSetting", null);
exports.AdminSettingsController = AdminSettingsController = __decorate([
    (0, swagger_1.ApiTags)('Admin Settings'),
    (0, common_1.Controller)('admin/settings'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, admin_guard_1.AdminGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [admin_settings_service_1.AdminSettingsService])
], AdminSettingsController);
//# sourceMappingURL=admin-settings.controller.js.map