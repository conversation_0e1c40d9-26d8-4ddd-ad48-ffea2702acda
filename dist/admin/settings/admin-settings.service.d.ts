import { PrismaService } from '../../prisma/prisma.service';
import { AppSetting } from '../../../generated/prisma';
import { UpdateLlmProviderDto } from './dto/update-llm-provider.dto';
export declare class AdminSettingsService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    getLlmProvider(): Promise<AppSetting>;
    updateLlmProvider(updateLlmProviderDto: UpdateLlmProviderDto): Promise<AppSetting>;
    getAllSettings(): Promise<AppSetting[]>;
    getSetting(key: string): Promise<AppSetting>;
    updateSetting(key: string, value: string, description?: string): Promise<AppSetting>;
    deleteSetting(key: string): Promise<void>;
}
