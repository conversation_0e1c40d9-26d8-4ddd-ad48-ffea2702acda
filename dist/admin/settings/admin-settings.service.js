"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminSettingsService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../../prisma/prisma.service");
let AdminSettingsService = class AdminSettingsService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getLlmProvider() {
        const setting = await this.prisma.appSetting.findUnique({
            where: { key: 'CURRENT_LLM_PROVIDER' },
        });
        if (!setting) {
            throw new common_1.NotFoundException('LLM provider setting not found');
        }
        return setting;
    }
    async updateLlmProvider(updateLlmProviderDto) {
        const { provider } = updateLlmProviderDto;
        const updatedSetting = await this.prisma.appSetting.upsert({
            where: { key: 'CURRENT_LLM_PROVIDER' },
            update: {
                value: provider,
                updatedAt: new Date(),
            },
            create: {
                key: 'CURRENT_LLM_PROVIDER',
                value: provider,
                description: 'The LLM provider to use for recommendations. Options: OPENAI, GOOGLE_GEMINI, ANTHROPIC',
            },
        });
        return updatedSetting;
    }
    async getAllSettings() {
        return this.prisma.appSetting.findMany({
            orderBy: { key: 'asc' },
        });
    }
    async getSetting(key) {
        const setting = await this.prisma.appSetting.findUnique({
            where: { key },
        });
        if (!setting) {
            throw new common_1.NotFoundException(`Setting with key '${key}' not found`);
        }
        return setting;
    }
    async updateSetting(key, value, description) {
        const updatedSetting = await this.prisma.appSetting.upsert({
            where: { key },
            update: {
                value,
                description: description || undefined,
                updatedAt: new Date(),
            },
            create: {
                key,
                value,
                description: description || null,
            },
        });
        return updatedSetting;
    }
    async deleteSetting(key) {
        try {
            await this.prisma.appSetting.delete({
                where: { key },
            });
        }
        catch (error) {
            throw new common_1.NotFoundException(`Setting with key '${key}' not found`);
        }
    }
};
exports.AdminSettingsService = AdminSettingsService;
exports.AdminSettingsService = AdminSettingsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], AdminSettingsService);
//# sourceMappingURL=admin-settings.service.js.map