"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppSettingResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class AppSettingResponseDto {
}
exports.AppSettingResponseDto = AppSettingResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The setting key',
        example: 'CURRENT_LLM_PROVIDER',
    }),
    __metadata("design:type", String)
], AppSettingResponseDto.prototype, "key", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The setting value',
        example: 'OPENAI',
    }),
    __metadata("design:type", String)
], AppSettingResponseDto.prototype, "value", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Description of the setting',
        example: 'The LLM provider to use for recommendations. Options: OPENAI, GOOGLE_GEMINI, ANTHROPIC',
        required: false,
    }),
    __metadata("design:type", Object)
], AppSettingResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'When the setting was created',
        example: '2024-06-20T01:00:00.000Z',
    }),
    __metadata("design:type", Date)
], AppSettingResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'When the setting was last updated',
        example: '2024-06-20T01:00:00.000Z',
    }),
    __metadata("design:type", Date)
], AppSettingResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=app-setting-response.dto.js.map