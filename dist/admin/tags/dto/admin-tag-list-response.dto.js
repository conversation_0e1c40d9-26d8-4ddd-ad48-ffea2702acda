"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminTagListResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const tag_response_dto_1 = require("../../../tags/dto/tag-response.dto");
class AdminTagListResponseDto {
}
exports.AdminTagListResponseDto = AdminTagListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of tag objects.',
        type: [tag_response_dto_1.TagResponseDto],
    }),
    __metadata("design:type", Array)
], AdminTagListResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of tags matching the query.', example: 100 }),
    __metadata("design:type", Number)
], AdminTagListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Current page number.', example: 1 }),
    __metadata("design:type", Number)
], AdminTagListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of items per page.', example: 10 }),
    __metadata("design:type", Number)
], AdminTagListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of pages.', example: 10 }),
    __metadata("design:type", Number)
], AdminTagListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=admin-tag-list-response.dto.js.map