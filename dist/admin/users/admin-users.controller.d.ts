import { UserService } from '../../users/users.service';
import { User as PublicUserModel, UserStatus } from '../../../generated/prisma';
import { UpdateUserStatusDto } from './dto/update-user-status.dto';
import { UpdateUserRoleDto } from './dto/update-user-role.dto';
import { UserProfileResponseDto } from '../../users/dto/user-profile-response.dto';
export declare class AdminUsersController {
    private readonly userService;
    constructor(userService: UserService);
    getAllUsers(page?: string, limit?: string, filterByStatus?: UserStatus, sortBy?: string, sortOrder?: 'asc' | 'desc'): Promise<{
        users: import("../../users/users.service").AdminUserListItem[];
        total: number;
        page: number;
        limit: number;
    }>;
    getUserById(userId: string): Promise<UserProfileResponseDto>;
    updateUserStatus(adminUser: PublicUserModel, targetUserId: string, updateUserStatusDto: UpdateUserStatusDto): Promise<UserProfileResponseDto>;
    updateUserRole(adminUser: PublicUserModel, targetUserId: string, updateUserRoleDto: UpdateUserRoleDto): Promise<UserProfileResponseDto>;
}
