"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminUsersController = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("../../users/users.service");
const admin_guard_1 = require("../../auth/guards/admin.guard");
const prisma_1 = require("../../../generated/prisma");
const get_user_decorator_1 = require("../../auth/decorators/get-user.decorator");
const update_user_status_dto_1 = require("./dto/update-user-status.dto");
const update_user_role_dto_1 = require("./dto/update-user-role.dto");
const swagger_1 = require("@nestjs/swagger");
const user_profile_response_dto_1 = require("../../users/dto/user-profile-response.dto");
const admin_user_list_response_dto_1 = require("./dto/admin-user-list-response.dto");
let AdminUsersController = class AdminUsersController {
    constructor(userService) {
        this.userService = userService;
    }
    async getAllUsers(page, limit, filterByStatus, sortBy, sortOrder) {
        const pageNumber = page ? parseInt(page, 10) : undefined;
        const limitNumber = limit ? parseInt(limit, 10) : undefined;
        const allowedSortByFields = ['createdAt', 'username', 'email', 'status', 'role', 'lastLogin', 'updatedAt', 'displayName'];
        const safeSortBy = sortBy && allowedSortByFields.includes(sortBy) ? sortBy : 'createdAt';
        return this.userService.findAllUsers({
            page: pageNumber,
            limit: limitNumber,
            filterByStatus,
            sortBy: safeSortBy,
            sortOrder,
        });
    }
    async getUserById(userId) {
        const user = await this.userService.findProfileById(userId);
        if (!user) {
            throw new common_1.NotFoundException(`User with ID ${userId} not found.`);
        }
        return {
            id: user.id,
            authUserId: user.authUserId,
            email: user.email,
            username: user.username,
            displayName: user.displayName,
            profilePictureUrl: user.profilePictureUrl,
            bio: user.bio,
            status: user.status,
            role: user.role,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            lastLoginAt: user.lastLogin,
        };
    }
    async updateUserStatus(adminUser, targetUserId, updateUserStatusDto) {
        const updatedUser = await this.userService.updateUserStatus(adminUser, targetUserId, updateUserStatusDto.status);
        return {
            id: updatedUser.id,
            authUserId: updatedUser.authUserId,
            email: updatedUser.email,
            username: updatedUser.username,
            displayName: updatedUser.displayName,
            profilePictureUrl: updatedUser.profilePictureUrl,
            bio: updatedUser.bio,
            status: updatedUser.status,
            role: updatedUser.role,
            createdAt: updatedUser.createdAt,
            updatedAt: updatedUser.updatedAt,
            lastLoginAt: updatedUser.lastLogin,
        };
    }
    async updateUserRole(adminUser, targetUserId, updateUserRoleDto) {
        const updatedUser = await this.userService.updateUserRole(adminUser, targetUserId, updateUserRoleDto.role);
        return {
            id: updatedUser.id,
            authUserId: updatedUser.authUserId,
            email: updatedUser.email,
            username: updatedUser.username,
            displayName: updatedUser.displayName,
            profilePictureUrl: updatedUser.profilePictureUrl,
            bio: updatedUser.bio,
            status: updatedUser.status,
            role: updatedUser.role,
            createdAt: updatedUser.createdAt,
            updatedAt: updatedUser.updatedAt,
            lastLoginAt: updatedUser.lastLogin,
        };
    }
};
exports.AdminUsersController = AdminUsersController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get a paginated list of all users (Admin)' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Page number for pagination.', example: 1 }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Number of users per page.', example: 10 }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: prisma_1.UserStatus, description: 'Filter users by status.' }),
    (0, swagger_1.ApiQuery)({ name: 'sortBy', required: false, type: String, description: 'Field to sort by.', example: 'createdAt', enum: ['createdAt', 'username', 'email', 'status', 'role', 'lastLogin', 'updatedAt', 'displayName'] }),
    (0, swagger_1.ApiQuery)({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: "Sort order ('asc' or 'desc').", example: 'desc' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of users retrieved successfully.', type: admin_user_list_response_dto_1.AdminUserListResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden resource. User is not an admin.' }),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('status')),
    __param(3, (0, common_1.Query)('sortBy')),
    __param(4, (0, common_1.Query)('sortOrder')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "getAllUsers", null);
__decorate([
    (0, common_1.Get)(':userId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a specific user by ID (Admin)' }),
    (0, swagger_1.ApiParam)({ name: 'userId', type: String, format: 'uuid', description: "The UUID of the user to retrieve." }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User details retrieved successfully.', type: user_profile_response_dto_1.UserProfileResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden resource.' }),
    __param(0, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "getUserById", null);
__decorate([
    (0, common_1.Put)(':userId/status'),
    (0, swagger_1.ApiOperation)({ summary: "Update a user\'s status (Admin)" }),
    (0, swagger_1.ApiParam)({ name: 'userId', type: String, format: 'uuid', description: "The UUID of the user whose status is to be updated." }),
    (0, swagger_1.ApiBody)({ type: update_user_status_dto_1.UpdateUserStatusDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "User status updated successfully.", type: user_profile_response_dto_1.UserProfileResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data or invalid status transition.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden resource (e.g., admin trying to change their own status).' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_user_status_dto_1.UpdateUserStatusDto]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "updateUserStatus", null);
__decorate([
    (0, common_1.Put)(':userId/role'),
    (0, swagger_1.ApiOperation)({ summary: "Update a user\'s role (Admin)" }),
    (0, swagger_1.ApiParam)({ name: 'userId', type: String, format: 'uuid', description: "The UUID of the user whose role is to be updated." }),
    (0, swagger_1.ApiBody)({ type: update_user_role_dto_1.UpdateUserRoleDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "User role updated successfully.", type: user_profile_response_dto_1.UserProfileResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid input data.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden resource (e.g., admin trying to change their own role or remove last admin).' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Param)('userId', common_1.ParseUUIDPipe)),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_user_role_dto_1.UpdateUserRoleDto]),
    __metadata("design:returntype", Promise)
], AdminUsersController.prototype, "updateUserRole", null);
exports.AdminUsersController = AdminUsersController = __decorate([
    (0, swagger_1.ApiTags)('Admin - Users'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.Controller)('admin/users'),
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    __metadata("design:paramtypes", [users_service_1.UserService])
], AdminUsersController);
//# sourceMappingURL=admin-users.controller.js.map