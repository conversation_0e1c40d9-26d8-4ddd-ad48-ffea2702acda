"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminUserListResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const user_profile_response_dto_1 = require("../../../users/dto/user-profile-response.dto");
class AdminUserListResponseDto {
}
exports.AdminUserListResponseDto = AdminUserListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of user profiles.',
        type: [user_profile_response_dto_1.UserProfileResponseDto],
    }),
    __metadata("design:type", Array)
], AdminUserListResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of users matching the query.', example: 100 }),
    __metadata("design:type", Number)
], AdminUserListResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Current page number.', example: 1 }),
    __metadata("design:type", Number)
], AdminUserListResponseDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of items per page.', example: 10 }),
    __metadata("design:type", Number)
], AdminUserListResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Total number of pages.', example: 10 }),
    __metadata("design:type", Number)
], AdminUserListResponseDto.prototype, "totalPages", void 0);
//# sourceMappingURL=admin-user-list-response.dto.js.map