import { Response } from 'express';
import { AppService } from './app.service';
import { SupabaseService } from './supabase/supabase.service';
export declare class AppController {
    private readonly appService;
    private readonly supabaseService;
    constructor(appService: AppService, supabaseService: SupabaseService);
    getHello(): string;
    healthCheck(res: Response): void;
    testConnection(): Promise<{
        status: string;
        message: string;
        userCount: number | null;
        details?: undefined;
    } | {
        status: string;
        message: string;
        details: any;
        userCount?: undefined;
    }>;
}
