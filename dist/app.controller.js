"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppController = void 0;
const common_1 = require("@nestjs/common");
const app_service_1 = require("./app.service");
const supabase_service_1 = require("./supabase/supabase.service");
let AppController = class AppController {
    constructor(appService, supabaseService) {
        this.appService = appService;
        this.supabaseService = supabaseService;
    }
    getHello() {
        return this.appService.getHello();
    }
    healthCheck(res) {
        res.status(200).send('OK');
    }
    async testConnection() {
        try {
            const { data, error, count } = await this.supabaseService.getClient()
                .from('users')
                .select('*', { count: 'exact', head: true });
            if (error) {
                console.error('Supabase connection test error:', error);
                return {
                    status: 'error',
                    message: 'Failed to connect to Supabase or query users table',
                    details: error.message
                };
            }
            return {
                status: 'success',
                message: 'Successfully connected to Supabase and queried users table count.',
                userCount: count
            };
        }
        catch (error) {
            console.error('Supabase connection test exception:', error);
            return {
                status: 'error',
                message: 'Exception during Supabase connection test',
                details: error.message
            };
        }
    }
};
exports.AppController = AppController;
__decorate([
    (0, common_1.Get)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", String)
], AppController.prototype, "getHello", null);
__decorate([
    (0, common_1.Get)('/healthz'),
    __param(0, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], AppController.prototype, "healthCheck", null);
__decorate([
    (0, common_1.Get)('test-connection'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AppController.prototype, "testConnection", null);
exports.AppController = AppController = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [app_service_1.AppService,
        supabase_service_1.SupabaseService])
], AppController);
//# sourceMappingURL=app.controller.js.map