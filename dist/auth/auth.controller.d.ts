import { AuthService } from './auth.service';
import { RegisterAuthDto } from './dto/register-auth.dto';
import { LoginAuthDto } from './dto/login-auth.dto';
import { Request } from 'express';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ResendConfirmationDto } from './dto/resend-confirmation.dto';
import { SignUpResponseDto } from './dto/signup-response.dto';
import { LoginResponseDto } from './dto/login-response.dto';
import { User as PrismaUserProfile } from '../../generated/prisma';
import { ReqUserObject } from './strategies/jwt.strategy';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    signUp(registerAuthDto: RegisterAuthDto): Promise<SignUpResponseDto>;
    signIn(loginAuthDto: LoginAuthDto): Promise<LoginResponseDto>;
    logout(req: Request): Promise<{
        message: string;
    }>;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{
        message: string;
    }>;
    resetPassword(resetPasswordDto: ResetPasswordDto, authHeader?: string): Promise<{
        message: string;
    }>;
    resendConfirmation(resendConfirmationDto: ResendConfirmationDto): Promise<{
        message: string;
    }>;
    syncProfile(reqUser: ReqUserObject): Promise<PrismaUserProfile>;
}
