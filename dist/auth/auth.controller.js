"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const throttler_1 = require("@nestjs/throttler");
const auth_service_1 = require("./auth.service");
const register_auth_dto_1 = require("./dto/register-auth.dto");
const login_auth_dto_1 = require("./dto/login-auth.dto");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const forgot_password_dto_1 = require("./dto/forgot-password.dto");
const reset_password_dto_1 = require("./dto/reset-password.dto");
const resend_confirmation_dto_1 = require("./dto/resend-confirmation.dto");
const swagger_1 = require("@nestjs/swagger");
const signup_response_dto_1 = require("./dto/signup-response.dto");
const login_response_dto_1 = require("./dto/login-response.dto");
const user_profile_minimal_dto_1 = require("./dto/user-profile-minimal.dto");
const get_req_user_decorator_1 = require("./decorators/get-req-user.decorator");
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    async signUp(registerAuthDto) {
        const { user: supabaseUser } = await this.authService.signUp(registerAuthDto);
        if (!supabaseUser) {
            throw new common_1.InternalServerErrorException('User registration completed but user data is unexpectedly missing.');
        }
        const safeUser = {
            id: supabaseUser.id,
            email: supabaseUser.email || '',
            created_at: supabaseUser.created_at,
            last_sign_in_at: supabaseUser.last_sign_in_at || null,
            app_metadata: supabaseUser.app_metadata || undefined,
            user_metadata: supabaseUser.user_metadata || undefined,
        };
        return {
            message: 'User registered successfully. Please check your email to confirm your registration.',
            user: safeUser,
        };
    }
    async signIn(loginAuthDto) {
        const { user, session } = await this.authService.signIn(loginAuthDto);
        const safeUser = {
            id: user.id,
            email: user.email || '',
            created_at: user.created_at,
            last_sign_in_at: user.last_sign_in_at || null,
            app_metadata: user.app_metadata || undefined,
            user_metadata: user.user_metadata || undefined,
        };
        let sessionDto = null;
        if (session) {
            const sessionUser = {
                id: session.user.id,
                email: session.user.email || '',
                created_at: session.user.created_at,
                last_sign_in_at: session.user.last_sign_in_at || null,
                app_metadata: session.user.app_metadata || undefined,
                user_metadata: session.user.user_metadata || undefined,
            };
            sessionDto = {
                access_token: session.access_token,
                refresh_token: session.refresh_token,
                token_type: session.token_type,
                expires_in: session.expires_in,
                expires_at: session.expires_at || undefined,
                user: sessionUser,
            };
        }
        return {
            message: 'User logged in successfully.',
            user: safeUser,
            session: sessionDto,
        };
    }
    async logout(req) {
        await this.authService.signOut();
        return { message: 'User logged out successfully.' };
    }
    async forgotPassword(forgotPasswordDto) {
        await this.authService.forgotPassword(forgotPasswordDto);
        return { message: 'If an account with this email exists, a password reset link has been sent.' };
    }
    async resetPassword(resetPasswordDto, authHeader) {
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new common_1.UnauthorizedException('Authorization header with Bearer token is required for password reset.');
        }
        const accessToken = authHeader.split(' ')[1];
        if (!accessToken) {
            throw new common_1.UnauthorizedException('Bearer token is missing or malformed.');
        }
        await this.authService.resetPassword(resetPasswordDto, accessToken);
        return { message: 'Password has been reset successfully.' };
    }
    async resendConfirmation(resendConfirmationDto) {
        await this.authService.resendConfirmation(resendConfirmationDto);
        return { message: 'If your account exists and requires confirmation, a new confirmation email has been sent.' };
    }
    async syncProfile(reqUser) {
        const servicePayload = {
            id: reqUser.authData.sub,
            email: reqUser.authData.email,
            user_metadata: reqUser.authData.user_metadata,
        };
        return this.authService.syncUserProfile(servicePayload);
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('signup'),
    (0, throttler_1.Throttle)({ default: { limit: 5, ttl: 60000 } }),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({
        summary: 'Register a new user',
        description: 'Creates a new user account. An email confirmation will be sent to the provided email address. The username and email must be unique.'
    }),
    (0, swagger_1.ApiBody)({ type: register_auth_dto_1.RegisterAuthDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: 'User registered successfully. Please check your email to confirm your registration.', type: signup_response_dto_1.SignUpResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid input data (e.g., email format, password strength, missing fields).' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CONFLICT, description: 'Email or username already exists.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.TOO_MANY_REQUESTS, description: 'Too many signup attempts. Please try again later.' }),
    __param(0, (0, common_1.Body)(new common_1.ValidationPipe({ whitelist: true, forbidNonWhitelisted: true, transform: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [register_auth_dto_1.RegisterAuthDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "signUp", null);
__decorate([
    (0, common_1.Post)('login'),
    (0, throttler_1.Throttle)({ default: { limit: 10, ttl: 60000 } }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Log in an existing user',
        description: 'Authenticates a user and returns a session object including JWT access and refresh tokens.'
    }),
    (0, swagger_1.ApiBody)({ type: login_auth_dto_1.LoginAuthDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'User logged in successfully.', type: login_response_dto_1.LoginResponseDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'Invalid credentials or email not confirmed.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid input data.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.TOO_MANY_REQUESTS, description: 'Too many login attempts. Please try again later.' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [login_auth_dto_1.LoginAuthDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "signIn", null);
__decorate([
    (0, common_1.Post)('logout'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Log out the current user',
        description: 'Invalidates the current user\'s session. Requires a valid JWT Bearer token.'
    }),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'User logged out successfully.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'User not authenticated or token invalid.' }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "logout", null);
__decorate([
    (0, common_1.Post)('forgot-password'),
    (0, throttler_1.Throttle)({ default: { limit: 3, ttl: 300000 } }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Request a password reset link',
        description: 'Sends a password reset link to the user\'s registered email address if the account exists.'
    }),
    (0, swagger_1.ApiBody)({ type: forgot_password_dto_1.ForgotPasswordDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'If an account with this email exists, a password reset link has been sent.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid input data (e.g., email format).' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.NOT_FOUND, description: 'Email not found (though the response is generic for security).' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.TOO_MANY_REQUESTS, description: 'Too many password reset attempts. Please try again later.' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [forgot_password_dto_1.ForgotPasswordDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "forgotPassword", null);
__decorate([
    (0, common_1.Post)('reset-password'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Reset user password using a token from email',
        description: 'Allows a user to set a new password after following a password reset link sent to their email. The client should include the temporary access token (obtained from the password recovery URL fragment or specific auth event) as a Bearer token in the Authorization header for this request.'
    }),
    (0, swagger_1.ApiBody)({ type: reset_password_dto_1.ResetPasswordDto }),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Password has been reset successfully.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid input data (e.g., passwords don\'t match, password too weak).' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.UNAUTHORIZED, description: 'Invalid or expired password reset token.' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Headers)('authorization')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [reset_password_dto_1.ResetPasswordDto, String]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "resetPassword", null);
__decorate([
    (0, common_1.Post)('resend-confirmation'),
    (0, throttler_1.Throttle)({ default: { limit: 3, ttl: 300000 } }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Resend email confirmation link',
        description: 'Sends a new email confirmation link to the user\'s email address if their account exists and is pending confirmation.'
    }),
    (0, swagger_1.ApiBody)({ type: resend_confirmation_dto_1.ResendConfirmationDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'If your account exists and requires confirmation, a new confirmation email has been sent.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.BAD_REQUEST, description: 'Invalid input data (e.g., email format).' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CONFLICT, description: 'Email already confirmed or recent resend attempt.' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.TOO_MANY_REQUESTS, description: 'Too many resend attempts. Please try again later.' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [resend_confirmation_dto_1.ResendConfirmationDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "resendConfirmation", null);
__decorate([
    (0, common_1.Post)('sync-profile'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiOperation)({ summary: 'Synchronize authenticated user profile with local database' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Profile synced successfully', type: user_profile_minimal_dto_1.UserProfileMinimalDto }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized' }),
    (0, swagger_1.ApiBearerAuth)(),
    __param(0, (0, get_req_user_decorator_1.GetReqUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "syncProfile", null);
exports.AuthController = AuthController = __decorate([
    (0, swagger_1.ApiTags)('Authentication'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map