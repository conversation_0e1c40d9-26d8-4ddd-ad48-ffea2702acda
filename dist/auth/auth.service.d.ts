import { SupabaseService } from '../supabase/supabase.service';
import { RegisterAuthDto } from './dto/register-auth.dto';
import { Session, User } from '@supabase/supabase-js';
import { LoginAuthDto } from './dto/login-auth.dto';
import { ConfigService } from '@nestjs/config';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ResendConfirmationDto } from './dto/resend-confirmation.dto';
import { PrismaService } from '../prisma/prisma.service';
import { User as PrismaUser } from '../../generated/prisma';
interface SyncProfilePayload {
    id: string;
    email?: string;
    user_metadata?: Record<string, any>;
}
export declare class AuthService {
    private readonly supabaseService;
    private readonly configService;
    private readonly prisma;
    constructor(supabaseService: SupabaseService, configService: ConfigService, prisma: PrismaService);
    signUp(registerAuthDto: RegisterAuthDto): Promise<{
        message: string;
        user: User | null;
    }>;
    signIn(loginAuthDto: LoginAuthDto): Promise<{
        user: User;
        session: Session | null;
    }>;
    signOut(): Promise<{
        message: string;
    }>;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<{
        message: string;
    }>;
    resetPassword(resetPasswordDto: ResetPasswordDto, accessToken: string | undefined): Promise<{
        message: string;
    }>;
    resendConfirmation(resendConfirmationDto: ResendConfirmationDto): Promise<{
        message: string;
    }>;
    private handleAuthError;
    syncUserProfile(authUserPayload: SyncProfilePayload): Promise<PrismaUser>;
}
export {};
