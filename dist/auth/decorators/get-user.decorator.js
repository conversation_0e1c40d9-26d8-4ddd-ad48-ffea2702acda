"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GetUser = void 0;
const common_1 = require("@nestjs/common");
exports.GetUser = (0, common_1.createParamDecorator)((data, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const reqUser = request.user;
    if (!reqUser || !reqUser.dbProfile) {
        throw new common_1.UnauthorizedException('User profile not found. Please ensure your account is properly set up.');
    }
    return reqUser.dbProfile;
});
//# sourceMappingURL=get-user.decorator.js.map