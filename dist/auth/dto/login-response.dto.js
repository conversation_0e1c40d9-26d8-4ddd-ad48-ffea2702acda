"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LoginResponseDto = exports.SessionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const user_profile_minimal_dto_1 = require("./user-profile-minimal.dto");
class SessionDto {
}
exports.SessionDto = SessionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The access token (JWT).', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' }),
    __metadata("design:type", String)
], SessionDto.prototype, "access_token", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The refresh token.', example: 'long_refresh_token_string' }),
    __metadata("design:type", String)
], SessionDto.prototype, "refresh_token", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The type of token.', example: 'bearer' }),
    __metadata("design:type", String)
], SessionDto.prototype, "token_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The number of seconds until the access token expires.', example: 3600 }),
    __metadata("design:type", Number)
], SessionDto.prototype, "expires_in", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'The Unix timestamp of when the access token expires.', example: 1678886400, required: false }),
    __metadata("design:type", Number)
], SessionDto.prototype, "expires_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Minimal user profile associated with this session.',
        type: user_profile_minimal_dto_1.UserProfileMinimalDto,
    }),
    __metadata("design:type", user_profile_minimal_dto_1.UserProfileMinimalDto)
], SessionDto.prototype, "user", void 0);
class LoginResponseDto {
}
exports.LoginResponseDto = LoginResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'A message confirming the outcome of the login attempt.',
        example: 'User logged in successfully.',
    }),
    __metadata("design:type", String)
], LoginResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Full user profile information upon successful login.',
        type: user_profile_minimal_dto_1.UserProfileMinimalDto,
    }),
    __metadata("design:type", user_profile_minimal_dto_1.UserProfileMinimalDto)
], LoginResponseDto.prototype, "user", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The Supabase session object containing tokens and session details.',
        type: SessionDto,
        nullable: true,
    }),
    __metadata("design:type", Object)
], LoginResponseDto.prototype, "session", void 0);
//# sourceMappingURL=login-response.dto.js.map