"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResetPasswordDto = exports.MatchPasswordConstraint = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
let MatchPasswordConstraint = class MatchPasswordConstraint {
    validate(value, args) {
        const [relatedPropertyName] = args.constraints;
        const relatedValue = args.object[relatedPropertyName];
        return value === relatedValue;
    }
    defaultMessage(args) {
        const [relatedPropertyName] = args.constraints;
        return `${args.property} must match ${relatedPropertyName}`;
    }
};
exports.MatchPasswordConstraint = MatchPasswordConstraint;
exports.MatchPasswordConstraint = MatchPasswordConstraint = __decorate([
    (0, class_validator_1.ValidatorConstraint)({ name: 'matchPassword', async: false })
], MatchPasswordConstraint);
class ResetPasswordDto {
}
exports.ResetPasswordDto = ResetPasswordDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'New password for the user account.',
        example: 'NewStr0ngP@ss!',
        minLength: 8,
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Password should not be empty.' }),
    (0, class_validator_1.MinLength)(8, { message: 'Password must be at least 8 characters long.' }),
    __metadata("design:type", String)
], ResetPasswordDto.prototype, "password", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Confirmation of the new password.',
        example: 'NewStr0ngP@ss!',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Password confirmation should not be empty.' }),
    (0, class_validator_1.Validate)(MatchPasswordConstraint, ['password'], {
        message: 'Passwords do not match.'
    }),
    __metadata("design:type", String)
], ResetPasswordDto.prototype, "confirmPassword", void 0);
//# sourceMappingURL=reset-password.dto.js.map