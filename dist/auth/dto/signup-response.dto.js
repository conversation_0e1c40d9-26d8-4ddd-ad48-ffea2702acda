"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SignUpResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const user_profile_minimal_dto_1 = require("./user-profile-minimal.dto");
class SignUpResponseDto {
}
exports.SignUpResponseDto = SignUpResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'A message confirming the outcome of the registration attempt.',
        example: 'User registered successfully. Please check your email to confirm your registration.',
    }),
    __metadata("design:type", String)
], SignUpResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Basic profile information of the registered user.',
        type: user_profile_minimal_dto_1.UserProfileMinimalDto,
    }),
    __metadata("design:type", user_profile_minimal_dto_1.UserProfileMinimalDto)
], SignUpResponseDto.prototype, "user", void 0);
//# sourceMappingURL=signup-response.dto.js.map