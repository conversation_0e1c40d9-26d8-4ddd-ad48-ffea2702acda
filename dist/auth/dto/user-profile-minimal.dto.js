"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserProfileMinimalDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class UserProfileMinimalDto {
}
exports.UserProfileMinimalDto = UserProfileMinimalDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID (UUID)',
        example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    }),
    __metadata("design:type", String)
], UserProfileMinimalDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User email address',
        example: '<EMAIL>',
    }),
    __metadata("design:type", String)
], UserProfileMinimalDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of user creation',
        example: '2023-01-15T10:30:00.000Z',
    }),
    __metadata("design:type", String)
], UserProfileMinimalDto.prototype, "created_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of last user sign-in',
        example: '2023-01-16T12:00:00.000Z',
        required: false,
    }),
    __metadata("design:type", Object)
], UserProfileMinimalDto.prototype, "last_sign_in_at", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Application-specific metadata. Structure may vary.',
        example: { role: 'user', plan: 'free' },
        required: false,
    }),
    __metadata("design:type", Object)
], UserProfileMinimalDto.prototype, "app_metadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User-specific metadata. Structure may vary.',
        example: { username: 'john_doe', display_name: 'John Doe' },
        required: false,
    }),
    __metadata("design:type", Object)
], UserProfileMinimalDto.prototype, "user_metadata", void 0);
//# sourceMappingURL=user-profile-minimal.dto.js.map