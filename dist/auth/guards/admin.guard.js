"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminGuard = void 0;
const common_1 = require("@nestjs/common");
const jwt_auth_guard_1 = require("./jwt-auth.guard");
const prisma_1 = require("../../../generated/prisma");
let AdminGuard = class AdminGuard extends jwt_auth_guard_1.JwtAuthGuard {
    async canActivate(context) {
        const isAuthenticated = await super.canActivate(context);
        if (!isAuthenticated) {
            return false;
        }
        const request = context.switchToHttp().getRequest();
        const reqUser = request.user;
        if (reqUser && reqUser.dbProfile && reqUser.dbProfile.role === prisma_1.UserRole.ADMIN) {
            return true;
        }
        if (reqUser && reqUser.dbProfile) {
            console.warn(`[AdminGuard] Access denied. User ${reqUser.dbProfile.id} (AuthID: ${reqUser.authData.sub}) has role: ${reqUser.dbProfile.role}, expected ADMIN.`);
        }
        else if (reqUser) {
            console.warn(`[AdminGuard] Access denied. User (AuthID: ${reqUser.authData.sub}) has no DB profile. Admin access requires a valid admin profile.`);
        }
        else {
            console.error('[AdminGuard] Access denied. No user object found on request after JWT authentication step.');
            throw new common_1.UnauthorizedException('Authentication failed or user object not found.');
        }
        throw new common_1.ForbiddenException('You do not have permission to access this resource. Admin role required.');
    }
};
exports.AdminGuard = AdminGuard;
exports.AdminGuard = AdminGuard = __decorate([
    (0, common_1.Injectable)()
], AdminGuard);
//# sourceMappingURL=admin.guard.js.map