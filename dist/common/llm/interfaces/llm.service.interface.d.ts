export interface LlmRecommendation {
    recommendedEntityIds: string[];
    explanation: string;
}
export interface CandidateEntity {
    id: string;
    name: string;
    shortDescription: string | null;
    description: string | null;
    entityType: {
        name: string;
        slug: string;
    };
    categories: Array<{
        category: {
            name: string;
            slug: string;
        };
    }>;
    tags: Array<{
        tag: {
            name: string;
            slug: string;
        };
    }>;
    features: Array<{
        feature: {
            name: string;
            slug: string;
        };
    }>;
    websiteUrl?: string | null;
    logoUrl?: string | null;
    avgRating?: number;
    reviewCount?: number;
}
export interface ILlmService {
    getRecommendation(problemDescription: string, candidateEntities: CandidateEntity[]): Promise<LlmRecommendation>;
}
