import { ConfigService } from '@nestjs/config';
import { ILlmService, LlmRecommendation, CandidateEntity } from '../interfaces/llm.service.interface';
export declare class AnthropicLlmService implements ILlmService {
    private readonly configService;
    private readonly logger;
    private readonly apiKey;
    private readonly apiUrl;
    constructor(configService: ConfigService);
    getRecommendation(problemDescription: string, candidateEntities: CandidateEntity[]): Promise<LlmRecommendation>;
    private callAnthropicAPI;
    private buildRecommendationPrompt;
    private parseAnthropicResponse;
    private getFallbackRecommendation;
}
