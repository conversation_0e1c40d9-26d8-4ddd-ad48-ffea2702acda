"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GoogleGeminiLlmService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GoogleGeminiLlmService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let GoogleGeminiLlmService = GoogleGeminiLlmService_1 = class GoogleGeminiLlmService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(GoogleGeminiLlmService_1.name);
        this.apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
        this.apiKey = this.configService.get('GOOGLE_GEMINI_API_KEY');
        if (!this.apiKey) {
            this.logger.warn('GOOGLE_GEMINI_API_KEY is not set in environment variables.');
        }
    }
    async getRecommendation(problemDescription, candidateEntities) {
        this.logger.log(`Getting Google Gemini recommendation for problem: "${problemDescription}" with ${candidateEntities.length} candidates`);
        if (!this.apiKey) {
            this.logger.warn('Google Gemini API key not available, using fallback');
            return this.getFallbackRecommendation(candidateEntities);
        }
        try {
            const prompt = this.buildRecommendationPrompt(problemDescription, candidateEntities);
            const response = await this.callGeminiAPI(prompt);
            const recommendation = this.parseGeminiResponse(response, candidateEntities);
            this.logger.log(`Google Gemini recommendation generated: ${recommendation.recommendedEntityIds.length} entities recommended`);
            return recommendation;
        }
        catch (error) {
            this.logger.error('Error generating Google Gemini recommendation', error.stack);
            return this.getFallbackRecommendation(candidateEntities);
        }
    }
    async callGeminiAPI(prompt) {
        const requestBody = {
            contents: [
                {
                    parts: [
                        {
                            text: prompt,
                        },
                    ],
                },
            ],
            generationConfig: {
                temperature: 0.7,
                topK: 40,
                topP: 0.95,
                maxOutputTokens: 1000,
            },
        };
        const response = await fetch(`${this.apiUrl}?key=${this.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
        });
        if (!response.ok) {
            throw new Error(`Google Gemini API error: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        if (data.candidates && data.candidates[0]?.content?.parts?.[0]?.text) {
            return data.candidates[0].content.parts[0].text;
        }
        throw new Error('Invalid response format from Google Gemini API');
    }
    buildRecommendationPrompt(problemDescription, candidateEntities) {
        const entitiesContext = candidateEntities
            .map((entity, index) => {
            const categories = entity.categories
                .map((c) => c.category.name)
                .join(', ');
            const tags = entity.tags.map((t) => t.tag.name).join(', ');
            const features = entity.features.map((f) => f.feature.name).join(', ');
            return `${index + 1}. **${entity.name}** (ID: ${entity.id})
   - Type: ${entity.entityType.name}
   - Description: ${entity.shortDescription || entity.description || 'No description available'}
   - Categories: ${categories || 'None'}
   - Tags: ${tags || 'None'}
   - Features: ${features || 'None'}
   - Rating: ${entity.avgRating ? `${entity.avgRating}/5 (${entity.reviewCount} reviews)` : 'No ratings'}`;
        })
            .join('\n\n');
        return `You are an AI assistant helping users find the best AI tools and resources for their specific needs.

**User's Problem:**
"${problemDescription}"

**Available Options:**
${entitiesContext}

**Instructions:**
1. Analyze the user's problem and requirements
2. Recommend the TOP 3-5 most relevant options from the list above
3. Provide a clear explanation of why each recommendation fits the user's needs
4. Consider factors like: relevance to the problem, tool capabilities, user ratings, ease of use, and cost

**Response Format:**
Please respond in the following JSON format:
{
  "recommendedEntityIds": ["entity-id-1", "entity-id-2", "entity-id-3"],
  "explanation": "Based on your need for [problem summary], I recommend: 1) [Tool Name] because [specific reason]... 2) [Tool Name] because [specific reason]... 3) [Tool Name] because [specific reason]..."
}

**Important:** Only include entity IDs that exist in the provided list above. Limit to maximum 5 recommendations.`;
    }
    parseGeminiResponse(response, candidateEntities) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No JSON found in response');
            }
            const parsed = JSON.parse(jsonMatch[0]);
            if (!parsed.recommendedEntityIds || !Array.isArray(parsed.recommendedEntityIds)) {
                throw new Error('Invalid response structure');
            }
            const validEntityIds = candidateEntities.map((e) => e.id);
            const filteredIds = parsed.recommendedEntityIds.filter((id) => validEntityIds.includes(id));
            return {
                recommendedEntityIds: filteredIds.slice(0, 5),
                explanation: parsed.explanation || 'AI recommendation generated successfully.',
            };
        }
        catch (error) {
            this.logger.warn('Failed to parse Google Gemini response, using fallback', error.message);
            return this.getFallbackRecommendation(candidateEntities);
        }
    }
    getFallbackRecommendation(candidateEntities) {
        const sortedEntities = candidateEntities
            .sort((a, b) => (b.avgRating || 0) - (a.avgRating || 0))
            .slice(0, 3);
        return {
            recommendedEntityIds: sortedEntities.map((e) => e.id),
            explanation: 'Based on the available options, here are the top-rated tools that might help with your needs. Please review each option to see which best fits your specific requirements.',
        };
    }
};
exports.GoogleGeminiLlmService = GoogleGeminiLlmService;
exports.GoogleGeminiLlmService = GoogleGeminiLlmService = GoogleGeminiLlmService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], GoogleGeminiLlmService);
//# sourceMappingURL=google-gemini-llm.service.js.map