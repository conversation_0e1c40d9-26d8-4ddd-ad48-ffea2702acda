import { OpenaiService } from '../../../openai/openai.service';
import { ILlmService, LlmRecommendation, CandidateEntity } from '../interfaces/llm.service.interface';
export declare class OpenaiLlmService implements ILlmService {
    private readonly openaiService;
    private readonly logger;
    constructor(openaiService: OpenaiService);
    getRecommendation(problemDescription: string, candidateEntities: CandidateEntity[]): Promise<LlmRecommendation>;
    private buildRecommendationPrompt;
    private parseOpenAIResponse;
    private getFallbackRecommendation;
}
