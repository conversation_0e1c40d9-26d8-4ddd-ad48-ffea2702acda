"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BountyDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class BountyDetailsResponseDto {
}
exports.BountyDetailsResponseDto = BountyDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    }),
    __metadata("design:type", String)
], BountyDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The reward amount for the bounty as a string.',
        example: '1000',
    }),
    __metadata("design:type", Object)
], BountyDetailsResponseDto.prototype, "rewardAmount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The currency of the reward.',
        example: 'USD',
    }),
    __metadata("design:type", Object)
], BountyDetailsResponseDto.prototype, "rewardCurrency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The current status of the bounty (e.g., open, in-progress, closed).',
        example: 'open',
    }),
    __metadata("design:type", Object)
], BountyDetailsResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The submission deadline for the bounty.',
        example: '2024-12-31T23:59:59.999Z',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", Object)
], BountyDetailsResponseDto.prototype, "deadline", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The requirements for completing the bounty.',
        type: 'array',
        items: { type: 'string' },
        example: ['Fix a critical security vulnerability.', 'Provide a proof of concept.'],
    }),
    __metadata("design:type", Object)
], BountyDetailsResponseDto.prototype, "requirements", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The platform where the bounty is hosted (e.g., HackerOne, Bugcrowd).',
        example: 'HackerOne',
    }),
    __metadata("design:type", Object)
], BountyDetailsResponseDto.prototype, "platform", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the bounty details were created',
        example: '2024-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], BountyDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the bounty details',
        example: '2024-01-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], BountyDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=bounty-details-response.dto.js.map