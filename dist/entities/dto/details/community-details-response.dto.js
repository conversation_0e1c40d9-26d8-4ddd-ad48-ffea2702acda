"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommunityDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class CommunityDetailsResponseDto {
}
exports.CommunityDetailsResponseDto = CommunityDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'b2c3d4e5-f6g7-8901-2345-678901abcdef',
    }),
    __metadata("design:type", String)
], CommunityDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The platform where the community is hosted.',
        example: 'Discord',
    }),
    __metadata("design:type", Object)
], CommunityDetailsResponseDto.prototype, "platform", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The number of members in the community.',
        example: 15000,
        type: 'integer',
    }),
    __metadata("design:type", Object)
], CommunityDetailsResponseDto.prototype, "memberCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The main topics of focus for the community.',
        type: [String],
        example: ['AI Development', 'Machine Learning', 'Data Science'],
    }),
    __metadata("design:type", Object)
], CommunityDetailsResponseDto.prototype, "focusTopics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the community rules.',
        example: 'https://example.com/community/rules',
    }),
    __metadata("design:type", Object)
], CommunityDetailsResponseDto.prototype, "rulesUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to invite new members to the community.',
        example: 'https://discord.gg/example',
    }),
    __metadata("design:type", Object)
], CommunityDetailsResponseDto.prototype, "inviteUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the main channel or forum of the community.',
        example: 'https://example.com/community/general',
    }),
    __metadata("design:type", Object)
], CommunityDetailsResponseDto.prototype, "mainChannelUrl", void 0);
//# sourceMappingURL=community-details-response.dto.js.map