"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ContentCreatorDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ContentCreatorDetailsResponseDto {
}
exports.ContentCreatorDetailsResponseDto = ContentCreatorDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'c3d4e5f6-g7h8-9012-3456-789012abcdef',
    }),
    __metadata("design:type", String)
], ContentCreatorDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "The name of the content creator, if it's a person.",
        example: 'John Doe',
    }),
    __metadata("design:type", Object)
], ContentCreatorDetailsResponseDto.prototype, "creatorName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The primary platform where the creator publishes content.',
        example: 'YouTube',
    }),
    __metadata("design:type", Object)
], ContentCreatorDetailsResponseDto.prototype, "primaryPlatform", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The main focus areas or topics of the content creator.',
        type: [String],
        example: ['AI Tutorials', 'Machine Learning Explained', 'Tech Reviews'],
    }),
    __metadata("design:type", Object)
], ContentCreatorDetailsResponseDto.prototype, "focusAreas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The number of followers the creator has.',
        example: 100000,
        type: 'integer',
    }),
    __metadata("design:type", Object)
], ContentCreatorDetailsResponseDto.prototype, "followerCount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to an example of the creator-s content.',
        example: 'https://youtube.com/watch?v=example',
    }),
    __metadata("design:type", Object)
], ContentCreatorDetailsResponseDto.prototype, "exampleContentUrl", void 0);
//# sourceMappingURL=content-creator-details-response.dto.js.map