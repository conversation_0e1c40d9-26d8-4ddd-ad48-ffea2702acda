"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAgencyDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateAgencyDetailsDto {
}
exports.CreateAgencyDetailsDto = CreateAgencyDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Services offered by the agency', type: [String], example: ['AI Strategy', 'ML Model Development'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateAgencyDetailsDto.prototype, "services_offered", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Industries the agency focuses on', type: [String], example: ['Healthcare', 'Finance'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateAgencyDetailsDto.prototype, "industry_focus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Target client sizes (e.g., Startup, SME, Enterprise)', type: [String], example: ['Startup', 'Enterprise'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateAgencyDetailsDto.prototype, "target_client_size", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Target audience for the agency services', type: [String], example: ['CTOs', 'Product Managers'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateAgencyDetailsDto.prototype, "target_audience", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Summary of agency location(s)', example: 'New York, Remote' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAgencyDetailsDto.prototype, "location_summary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the agency portfolio', example: 'https://agency.com/portfolio' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateAgencyDetailsDto.prototype, "portfolio_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'General information about agency pricing or engagement models', example: 'Project-based, Retainer options available' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateAgencyDetailsDto.prototype, "pricing_info", void 0);
//# sourceMappingURL=create-agency-details.dto.js.map