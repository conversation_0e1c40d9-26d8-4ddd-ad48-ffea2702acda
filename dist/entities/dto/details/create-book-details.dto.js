"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateBookDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateBookDetailsDto {
}
exports.CreateBookDetailsDto = CreateBookDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'List of author names',
        example: ['Author A', 'Author B'],
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateBookDetailsDto.prototype, "author_names", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ISBN of the book',
        example: '978-3-16-148410-0',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateBookDetailsDto.prototype, "isbn", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Publisher of the book',
        example: 'Tech Publishing House',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateBookDetailsDto.prototype, "publisher", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Year the book was published',
        example: 2023,
        type: Number,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1000),
    (0, class_validator_1.Max)(new Date().getFullYear() + 5),
    __metadata("design:type", Number)
], CreateBookDetailsDto.prototype, "publication_year", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of pages in the book',
        example: 350,
        type: Number,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateBookDetailsDto.prototype, "page_count", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Summary or abstract of the book',
        example: 'An in-depth look at the future of artificial intelligence...',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateBookDetailsDto.prototype, "summary", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL where the book can be purchased',
        example: 'https://amazon.com/book-title',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateBookDetailsDto.prototype, "purchase_url", void 0);
//# sourceMappingURL=create-book-details.dto.js.map