"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateBountyDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateBountyDetailsDto {
}
exports.CreateBountyDetailsDto = CreateBountyDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Issuer of the bounty',
        example: 'OpenAI',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateBountyDetailsDto.prototype, "bounty_issuer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Reward amount for the bounty (e.g., "1000 USD", "0.5 ETH")',
        example: '1000 USD',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateBountyDetailsDto.prototype, "reward_amount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Requirements for completing the bounty',
        example: 'Develop a plugin for X, must meet performance Y.',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateBountyDetailsDto.prototype, "requirements", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Submission deadline for the bounty (YYYY-MM-DD)',
        example: '2024-11-30',
        type: String,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], CreateBountyDetailsDto.prototype, "submission_deadline", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the platform hosting the bounty (e.g., Gitcoin, HackerOne)',
        example: 'https://gitcoin.co/bounty/123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateBountyDetailsDto.prototype, "platform_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Difficulty level of the bounty (e.g., Easy, Medium, Hard)',
        example: 'Medium',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateBountyDetailsDto.prototype, "difficulty_level", void 0);
//# sourceMappingURL=create-bounty-details.dto.js.map