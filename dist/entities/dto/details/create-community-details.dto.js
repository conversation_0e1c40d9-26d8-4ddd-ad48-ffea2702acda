"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCommunityDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateCommunityDetailsDto {
}
exports.CreateCommunityDetailsDto = CreateCommunityDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Platform where the community is hosted (e.g., Discord, Slack, Forum)', example: 'Discord' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCommunityDetailsDto.prototype, "platform", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of members in the community', type: Number, minimum: 0, example: 1500 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateCommunityDetailsDto.prototype, "member_count", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Main focus topics of the community', type: [String], example: ['AI Safety', 'Large Language Models'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateCommunityDetailsDto.prototype, "focus_topics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the community rules or guidelines', example: 'https://community.com/rules' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateCommunityDetailsDto.prototype, "rules_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to join the community (invite link)', example: 'https://discord.gg/invitecode' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateCommunityDetailsDto.prototype, "invite_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the main channel or landing page of the community', example: 'https://community.com/general' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateCommunityDetailsDto.prototype, "main_channel_url", void 0);
//# sourceMappingURL=create-community-details.dto.js.map