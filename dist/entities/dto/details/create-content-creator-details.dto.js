"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateContentCreatorDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateContentCreatorDetailsDto {
}
exports.CreateContentCreatorDetailsDto = CreateContentCreatorDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name of the content creator', example: 'John Doe' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateContentCreatorDetailsDto.prototype, "creator_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Primary platform where the creator publishes (e.g., YouTube, Twitch, Blog)', example: 'YouTube' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateContentCreatorDetailsDto.prototype, "primary_platform", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Main focus areas or types of content created', type: [String], example: ['AI Tutorials', 'Tech Reviews'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateContentCreatorDetailsDto.prototype, "focus_areas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of followers or subscribers', type: Number, minimum: 0, example: 100000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateContentCreatorDetailsDto.prototype, "follower_count", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to an example piece of content (e.g., a popular video or article)', example: 'https://youtube.com/watch?v=example' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateContentCreatorDetailsDto.prototype, "example_content_url", void 0);
//# sourceMappingURL=create-content-creator-details.dto.js.map