"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCourseDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const prisma_1 = require("../../../../generated/prisma/index.js");
class CreateCourseDetailsDto {
}
exports.CreateCourseDetailsDto = CreateCourseDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Instructor name' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCourseDetailsDto.prototype, "instructor_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Duration of the course as text (e.g., "10 hours", "3 weeks")' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCourseDetailsDto.prototype, "duration_text", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Skill level targeted by the course', enum: prisma_1.SkillLevel }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.SkillLevel),
    __metadata("design:type", String)
], CreateCourseDetailsDto.prototype, "skill_level", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Prerequisites for the course as a text description' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateCourseDetailsDto.prototype, "prerequisites", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the course syllabus' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateCourseDetailsDto.prototype, "syllabus_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of students enrolled', type: Number, minimum: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateCourseDetailsDto.prototype, "enrollment_count", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Indicates if a certificate is available upon completion', type: Boolean }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateCourseDetailsDto.prototype, "certificate_available", void 0);
//# sourceMappingURL=create-course-details.dto.js.map