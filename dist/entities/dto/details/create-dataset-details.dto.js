"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateDatasetDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateDatasetDetailsDto {
}
exports.CreateDatasetDetailsDto = CreateDatasetDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Format of the dataset (e.g., CSV, JSON, Parquet)',
        example: 'CSV',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDatasetDetailsDto.prototype, "format", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the source of the dataset',
        example: 'https://example.com/dataset.zip',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateDatasetDetailsDto.prototype, "source_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'License of the dataset (e.g., MIT, CC BY 4.0)',
        example: 'CC BY 4.0',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDatasetDetailsDto.prototype, "license", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Size of the dataset in bytes',
        example: 104857600,
        type: Number,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateDatasetDetailsDto.prototype, "size_in_bytes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Detailed description of the dataset',
        example: 'A comprehensive dataset of...',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDatasetDetailsDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Notes on how to access or use the dataset',
        example: 'Requires registration. Download link will be emailed.',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateDatasetDetailsDto.prototype, "access_notes", void 0);
//# sourceMappingURL=create-dataset-details.dto.js.map