"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateGrantDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateGrantDetailsDto {
}
exports.CreateGrantDetailsDto = CreateGrantDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Name of the institution providing the grant',
        example: 'AI Research Foundation',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateGrantDetailsDto.prototype, "granting_institution", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Criteria for eligibility for the grant',
        example: 'Must be a non-profit organization focused on AI safety.',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateGrantDetailsDto.prototype, "eligibility_criteria", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Application deadline for the grant (YYYY-MM-DD)',
        example: '2024-12-31',
        type: String,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], CreateGrantDetailsDto.prototype, "application_deadline", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Funding amount or range (e.g., "$10,000 - $50,000")',
        example: '$10,000 - $50,000',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateGrantDetailsDto.prototype, "funding_amount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the grant application page',
        example: 'https://foundation.example/apply-grant',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateGrantDetailsDto.prototype, "application_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Main focus area of the grant (e.g., "AI Ethics Research")',
        example: 'AI Ethics Research',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateGrantDetailsDto.prototype, "grant_focus_area", void 0);
//# sourceMappingURL=create-grant-details.dto.js.map