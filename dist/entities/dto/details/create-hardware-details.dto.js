"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateHardwareDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateHardwareDetailsDto {
}
exports.CreateHardwareDetailsDto = CreateHardwareDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Type of hardware (e.g., GPU, FPGA, ASIC, TPU)',
        example: 'GPU',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateHardwareDetailsDto.prototype, "hardware_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Technical specifications of the hardware',
        example: { memory: '24GB GDDR6X', cuda_cores: 10496, tflops: 35.6 },
        type: 'object',
        additionalProperties: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateHardwareDetailsDto.prototype, "specifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Manufacturer of the hardware',
        example: 'NVIDIA',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateHardwareDetailsDto.prototype, "manufacturer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Release date of the hardware (YYYY-MM-DD)',
        example: '2023-09-20',
        type: String,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], CreateHardwareDetailsDto.prototype, "release_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Price range of the hardware (e.g., "$500 - $1000")',
        example: '$699 - $799',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateHardwareDetailsDto.prototype, "price_range", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the hardware datasheet',
        example: 'https://nvidia.com/datasheet/rtx4070.pdf',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateHardwareDetailsDto.prototype, "datasheet_url", void 0);
//# sourceMappingURL=create-hardware-details.dto.js.map