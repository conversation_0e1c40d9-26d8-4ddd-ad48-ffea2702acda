"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateInvestorDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateInvestorDetailsDto {
}
exports.CreateInvestorDetailsDto = CreateInvestorDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Areas of investment focus',
        example: ['Seed Stage AI', 'Healthcare Tech', 'SaaS'],
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateInvestorDetailsDto.prototype, "investment_focus_areas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the investor portfolio',
        example: 'https://investor.com/portfolio',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateInvestorDetailsDto.prototype, "portfolio_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Typical investment size (e.g., "$100k - $1M")',
        example: '$100k - $1M',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateInvestorDetailsDto.prototype, "typical_investment_size", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Investment stages (e.g., Pre-seed, Seed, Series A)',
        example: ['Pre-seed', 'Seed', 'Series A'],
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateInvestorDetailsDto.prototype, "investment_stages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Contact email for the investor',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], CreateInvestorDetailsDto.prototype, "contact_email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Preferred method of communication',
        example: 'Email, LinkedIn',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateInvestorDetailsDto.prototype, "preferred_communication", void 0);
//# sourceMappingURL=create-investor-details.dto.js.map