"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateJobDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateJobDetailsDto {
}
exports.CreateJobDetailsDto = CreateJobDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Title of the job', example: 'AI Engineer' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "job_title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name of the hiring company', example: 'Tech Solutions Inc.' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "company_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Location type of the job (e.g., Remote, On-site, Hybrid)',
        example: 'Remote',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "location_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Salary range for the job (e.g., "$80k - $120k", "Competitive")',
        example: '$80k - $120k',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "salary_range", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the job application page',
        example: 'https://jobs.example.com/apply/123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "application_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Full description of the job responsibilities and requirements',
        example: 'Seeking an experienced AI engineer to develop cutting-edge solutions...',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "job_description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Required experience level (e.g., Entry, Mid, Senior)',
        example: 'Mid-Senior level',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "experience_level", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Type of employment (e.g., Full-time, Part-time, Contract)',
        example: 'Full-time',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateJobDetailsDto.prototype, "employment_type", void 0);
//# sourceMappingURL=create-job-details.dto.js.map