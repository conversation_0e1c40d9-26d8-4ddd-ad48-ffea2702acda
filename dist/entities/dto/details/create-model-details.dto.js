"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateModelDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateModelDetailsDto {
}
exports.CreateModelDetailsDto = CreateModelDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Architecture of the model (e.g., Transformer, CNN, RNN)',
        example: 'Transformer',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateModelDetailsDto.prototype, "model_architecture", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of parameters in the model',
        example: 175000000000,
        type: Number,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateModelDetailsDto.prototype, "parameters_count", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Information about the training dataset used for the model',
        example: 'Trained on a large corpus of text and code.',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateModelDetailsDto.prototype, "training_dataset", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Performance metrics of the model',
        example: { accuracy: 0.95, f1_score: 0.92 },
        type: 'object',
        additionalProperties: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], CreateModelDetailsDto.prototype, "performance_metrics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to download or access the model (e.g., Hugging Face model hub)',
        example: 'https://huggingface.co/openai-gpt',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateModelDetailsDto.prototype, "model_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'License of the model (e.g., MIT, Apache 2.0)',
        example: 'MIT',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateModelDetailsDto.prototype, "license", void 0);
//# sourceMappingURL=create-model-details.dto.js.map