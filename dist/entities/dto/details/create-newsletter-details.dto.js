"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateNewsletterDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateNewsletterDetailsDto {
}
exports.CreateNewsletterDetailsDto = CreateNewsletterDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Publishing frequency (e.g., Weekly, Monthly, Bi-weekly)', example: 'Weekly' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateNewsletterDetailsDto.prototype, "frequency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Main topics covered in the newsletter', type: [String], example: ['AI Research', 'Tech Industry News'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreateNewsletterDetailsDto.prototype, "main_topics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the newsletter archive', example: 'https://newsletter.com/archive' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateNewsletterDetailsDto.prototype, "archive_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to subscribe to the newsletter', example: 'https://newsletter.com/subscribe' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateNewsletterDetailsDto.prototype, "subscribe_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Name of the author or publisher', example: 'AI Insights Team' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateNewsletterDetailsDto.prototype, "author_name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of subscribers', type: Number, minimum: 0, example: 5000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateNewsletterDetailsDto.prototype, "subscriber_count", void 0);
//# sourceMappingURL=create-newsletter-details.dto.js.map