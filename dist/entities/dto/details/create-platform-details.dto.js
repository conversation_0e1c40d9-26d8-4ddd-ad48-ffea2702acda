"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePlatformDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const prisma_1 = require("../../../../generated/prisma/index.js");
class CreatePlatformDetailsDto {
}
exports.CreatePlatformDetailsDto = CreatePlatformDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Type of the platform (e.g., PaaS, SaaS, IaaS)', example: 'SaaS' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePlatformDetailsDto.prototype, "platform_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Key services offered by the platform', type: [String], example: ['Model Training', 'Data Annotation'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreatePlatformDetailsDto.prototype, "key_services", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the platform documentation', example: 'https://platform.com/docs' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreatePlatformDetailsDto.prototype, "documentation_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Pricing model of the platform', enum: prisma_1.PricingModel, example: prisma_1.PricingModel.SUBSCRIPTION }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.PricingModel),
    __metadata("design:type", String)
], CreatePlatformDetailsDto.prototype, "pricing_model", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the Service Level Agreement (SLA)', example: 'https://platform.com/sla' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreatePlatformDetailsDto.prototype, "sla_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Supported regions or availability zones', type: [String], example: ['us-east-1', 'eu-west-2'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreatePlatformDetailsDto.prototype, "supported_regions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Does the platform offer a free tier?', example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreatePlatformDetailsDto.prototype, "has_free_tier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Potential use cases for the platform', type: [String], example: ['Enterprise AI Solutions', 'Startup AI Development'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreatePlatformDetailsDto.prototype, "use_cases", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'General price range', enum: prisma_1.PriceRange, example: prisma_1.PriceRange.MEDIUM }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.PriceRange),
    __metadata("design:type", String)
], CreatePlatformDetailsDto.prototype, "price_range", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Specific details about pricing tiers or structure', example: 'Basic: $99/month, Pro: $299/month' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePlatformDetailsDto.prototype, "pricing_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the pricing page', example: 'https://platform.com/pricing' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreatePlatformDetailsDto.prototype, "pricing_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'List of integrations with other tools or platforms', type: [String], example: ['Slack', 'GitHub'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    (0, class_validator_1.ArrayMinSize)(1),
    __metadata("design:type", Array)
], CreatePlatformDetailsDto.prototype, "integrations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Email address for support inquiries', example: '<EMAIL>' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], CreatePlatformDetailsDto.prototype, "support_email", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Does the platform offer live chat support?', example: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreatePlatformDetailsDto.prototype, "has_live_chat", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL to the community forum or support page', example: 'https://community.platform.com' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreatePlatformDetailsDto.prototype, "community_url", void 0);
//# sourceMappingURL=create-platform-details.dto.js.map