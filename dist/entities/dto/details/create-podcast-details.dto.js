"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePodcastDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreatePodcastDetailsDto {
}
exports.CreatePodcastDetailsDto = CreatePodcastDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'List of host names for the podcast',
        example: ['Host One', 'Host Two'],
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreatePodcastDetailsDto.prototype, "host_names", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Average length of an episode (e.g., "45 minutes")',
        example: '45 minutes',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePodcastDetailsDto.prototype, "average_episode_length", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Main topics covered by the podcast',
        example: ['AI Ethics', 'ML Research', 'Tech News'],
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreatePodcastDetailsDto.prototype, "main_topics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL where the podcast can be listened to (e.g., Spotify, Apple Podcasts)',
        example: 'https://open.spotify.com/show/podcastid',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreatePodcastDetailsDto.prototype, "listen_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Frequency of new episodes (e.g., "Weekly", "Bi-weekly")',
        example: 'Weekly',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePodcastDetailsDto.prototype, "frequency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Primary language of the podcast',
        example: 'English',
        default: 'English',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePodcastDetailsDto.prototype, "primary_language", void 0);
//# sourceMappingURL=create-podcast-details.dto.js.map