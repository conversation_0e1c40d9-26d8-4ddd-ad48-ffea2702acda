"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateProjectReferenceDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateProjectReferenceDetailsDto {
}
exports.CreateProjectReferenceDetailsDto = CreateProjectReferenceDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Status of the project (e.g., active, completed, archived)',
        example: 'active',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProjectReferenceDetailsDto.prototype, "project_status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the source code repository',
        example: 'https://github.com/user/project',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateProjectReferenceDetailsDto.prototype, "source_code_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the live demo of the project',
        example: 'https://project-demo.com',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateProjectReferenceDetailsDto.prototype, "live_demo_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'List of technologies used in the project',
        example: ['React', 'Node.js', 'PostgreSQL'],
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateProjectReferenceDetailsDto.prototype, "technologies", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Goals of the project',
        example: 'To demonstrate advanced AI capabilities in X domain.',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateProjectReferenceDetailsDto.prototype, "project_goals", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'List of contributors (names or objects)',
        example: ['Jane Doe', { name: 'John Smith', role: 'Lead Developer' }],
        type: Array,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    __metadata("design:type", Array)
], CreateProjectReferenceDetailsDto.prototype, "contributors", void 0);
//# sourceMappingURL=create-project-reference-details.dto.js.map