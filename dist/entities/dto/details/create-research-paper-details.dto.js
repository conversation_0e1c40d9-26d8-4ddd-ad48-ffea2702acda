"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateResearchPaperDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateResearchPaperDetailsDto {
}
exports.CreateResearchPaperDetailsDto = CreateResearchPaperDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Publication date of the research paper (YYYY-MM-DD)',
        example: '2023-03-15',
        type: String,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", Date)
], CreateResearchPaperDetailsDto.prototype, "publication_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Digital Object Identifier (DOI) of the paper',
        example: '10.1000/xyz123',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateResearchPaperDetailsDto.prototype, "doi", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'List of authors',
        example: ['Author One', 'Author Two'],
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateResearchPaperDetailsDto.prototype, "authors", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Abstract of the research paper',
        example: 'This paper explores...',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateResearchPaperDetailsDto.prototype, "abstract", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Name of the journal or conference',
        example: 'Journal of AI Research',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateResearchPaperDetailsDto.prototype, "journal_or_conference", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the publication',
        example: 'https://arxiv.org/abs/1234.56789',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateResearchPaperDetailsDto.prototype, "publication_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of citations',
        example: 150,
        type: Number,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateResearchPaperDetailsDto.prototype, "citation_count", void 0);
//# sourceMappingURL=create-research-paper-details.dto.js.map