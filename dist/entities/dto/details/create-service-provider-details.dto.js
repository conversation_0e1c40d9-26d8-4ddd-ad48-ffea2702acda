"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateServiceProviderDetailsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateServiceProviderDetailsDto {
}
exports.CreateServiceProviderDetailsDto = CreateServiceProviderDetailsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Areas of service provided',
        example: ['AI Development', 'Data Science Consulting', 'MLOps'],
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateServiceProviderDetailsDto.prototype, "service_areas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to case studies',
        example: 'https://serviceprovider.com/case-studies',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateServiceProviderDetailsDto.prototype, "case_studies_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL for booking a consultation',
        example: 'https://serviceprovider.com/book-consultation',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateServiceProviderDetailsDto.prototype, "consultation_booking_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Industry specializations',
        example: ['Healthcare', 'Finance'],
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateServiceProviderDetailsDto.prototype, "industry_specializations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Target company size (e.g., Startups, SMEs, Enterprise)',
        example: 'SMEs',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateServiceProviderDetailsDto.prototype, "company_size_focus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Typical hourly rate range (e.g., "$100-$200")',
        example: '$100-$200',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateServiceProviderDetailsDto.prototype, "hourly_rate_range", void 0);
//# sourceMappingURL=create-service-provider-details.dto.js.map