"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatasetDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class DatasetDetailsResponseDto {
}
exports.DatasetDetailsResponseDto = DatasetDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0',
    }),
    __metadata("design:type", String)
], DatasetDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "The format of the dataset (e.g., 'CSV', 'JSON', 'Parquet').",
        example: 'CSV',
    }),
    __metadata("design:type", Object)
], DatasetDetailsResponseDto.prototype, "format", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "The size of the dataset in a human-readable format (e.g., '1.2 GB', '1M rows').",
        example: '1.2 GB',
    }),
    __metadata("design:type", Object)
], DatasetDetailsResponseDto.prototype, "size", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The number of records or rows in the dataset.',
        example: 1000000,
        type: 'integer',
    }),
    __metadata("design:type", Object)
], DatasetDetailsResponseDto.prototype, "numRecords", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to access the dataset, perhaps an API endpoint or landing page.',
        example: 'https://example.com/dataset/access',
    }),
    __metadata("design:type", Object)
], DatasetDetailsResponseDto.prototype, "accessUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Direct URL to download the dataset file.',
        example: 'https://example.com/dataset.csv',
    }),
    __metadata("design:type", Object)
], DatasetDetailsResponseDto.prototype, "downloadUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "The license of the dataset (e.g., 'MIT', 'CC BY 4.0').",
        example: 'MIT',
    }),
    __metadata("design:type", Object)
], DatasetDetailsResponseDto.prototype, "license", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "How frequently the dataset is updated (e.g., 'daily', 'monthly', 'not updated').",
        example: 'monthly',
    }),
    __metadata("design:type", Object)
], DatasetDetailsResponseDto.prototype, "updateFrequency", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the dataset details were created',
        example: '2024-03-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], DatasetDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the dataset details',
        example: '2024-03-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], DatasetDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=dataset-details-response.dto.js.map