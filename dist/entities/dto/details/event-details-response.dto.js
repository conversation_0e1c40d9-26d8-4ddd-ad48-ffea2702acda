"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class EventDetailsResponseDto {
}
exports.EventDetailsResponseDto = EventDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'f6g7h8i9-j0k1-l2m3-n4o5-p6q7r8s9t0u1',
    }),
    __metadata("design:type", String)
], EventDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "The format of the event (e.g., 'virtual', 'in-person', 'hybrid').",
        example: 'virtual',
    }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "format", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The start date of the event.',
        example: '2024-09-01T09:00:00.000Z',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The end date of the event.',
        example: '2024-09-03T17:00:00.000Z',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The location of the event (can be a physical address or "Online").',
        example: 'Online',
    }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the event registration page.',
        example: 'https://example.com/event/register',
    }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "registrationUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the event schedule or agenda.',
        example: 'https://example.com/event/schedule',
    }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "scheduleUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Information about the speakers at the event.',
        example: [{ name: 'John Doe', topic: 'AI in 2025' }, 'Jane Smith'],
    }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "speakerInfo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The price of the event.',
        example: '$99',
    }),
    __metadata("design:type", Object)
], EventDetailsResponseDto.prototype, "price", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the event details were created',
        example: '2024-04-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], EventDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the event details',
        example: '2024-04-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], EventDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=event-details-response.dto.js.map