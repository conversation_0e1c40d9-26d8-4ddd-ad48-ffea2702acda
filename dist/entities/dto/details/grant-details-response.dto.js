"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GrantDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class GrantDetailsResponseDto {
}
exports.GrantDetailsResponseDto = GrantDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'g7h8i9j0-k1l2-m3n4-o5p6-q7r8s9t0u1v2',
    }),
    __metadata("design:type", String)
], GrantDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The funding amount of the grant.',
        example: '$10,000 - $50,000',
    }),
    __metadata("design:type", Object)
], GrantDetailsResponseDto.prototype, "fundingAmount", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "The type of funding (e.g., 'equity-free', 'research').",
        example: 'research',
    }),
    __metadata("design:type", Object)
], GrantDetailsResponseDto.prototype, "fundingType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the grant application page.',
        example: 'https://example.com/grant/apply',
    }),
    __metadata("design:type", Object)
], GrantDetailsResponseDto.prototype, "applicationUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The application deadline for the grant.',
        example: '2025-01-15T23:59:59.999Z',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", Object)
], GrantDetailsResponseDto.prototype, "deadline", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The eligibility criteria for the grant.',
        type: 'array',
        items: { type: 'string' },
        example: ['Must be a non-profit organization.', 'Project must be open-source.'],
    }),
    __metadata("design:type", Object)
], GrantDetailsResponseDto.prototype, "eligibilityCriteria", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The geographic regions the grant focuses on.',
        type: 'array',
        items: { type: 'string' },
        example: ['Global', 'North America'],
    }),
    __metadata("design:type", Object)
], GrantDetailsResponseDto.prototype, "regionFocus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the grant details were created',
        example: '2024-05-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], GrantDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the grant details',
        example: '2024-05-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], GrantDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=grant-details-response.dto.js.map