"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HardwareDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class HardwareDetailsResponseDto {
}
exports.HardwareDetailsResponseDto = HardwareDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'h8i9j0k1-l2m3-n4o5-p6q7-r8s9t0u1v2w3',
    }),
    __metadata("design:type", String)
], HardwareDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The main processor or CPU of the hardware.',
        example: 'Intel Core i9-13900K',
    }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "processor", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The amount and type of memory (RAM).',
        example: '32GB DDR5',
    }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "memory", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The storage capacity and type.',
        example: '2TB NVMe SSD',
    }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "storage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The graphics processing unit (GPU).',
        example: 'NVIDIA GeForce RTX 4090',
    }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "gpu", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Available ports on the hardware.',
        type: 'array',
        items: { type: 'string' },
        example: ['USB-C', 'HDMI 2.1', 'DisplayPort 1.4'],
    }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "ports", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The power consumption of the hardware.',
        example: '450W',
    }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "powerConsumption", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The physical dimensions of the hardware.',
        example: '304mm x 137mm x 61mm',
    }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "dimensions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The weight of the hardware.',
        example: '2.5kg',
    }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "weight", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The release date of the hardware.',
        example: '2022-09-20T00:00:00.000Z',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", Object)
], HardwareDetailsResponseDto.prototype, "releaseDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the hardware details were created',
        example: '2024-06-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], HardwareDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the hardware details',
        example: '2024-06-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], HardwareDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=hardware-details-response.dto.js.map