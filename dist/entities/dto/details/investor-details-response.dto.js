"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvestorDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class InvestorDetailsResponseDto {
}
exports.InvestorDetailsResponseDto = InvestorDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'i9j0k1l2-m3n4-o5p6-q7r8-s9t0u1v2w3x4',
    }),
    __metadata("design:type", String)
], InvestorDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "The investment stages the investor focuses on (e.g., 'seed', 'series-a').",
        type: 'array',
        items: { type: 'string' },
        example: ['Seed', 'Series A'],
    }),
    __metadata("design:type", Object)
], InvestorDetailsResponseDto.prototype, "investmentStage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The number of companies in the investor portfolio.',
        example: 50,
        type: 'integer',
    }),
    __metadata("design:type", Object)
], InvestorDetailsResponseDto.prototype, "portfolioSize", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The typical check size for investments.',
        example: '$100k - $1M',
    }),
    __metadata("design:type", Object)
], InvestorDetailsResponseDto.prototype, "checkSize", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The focus areas of the investor.',
        type: 'array',
        items: { type: 'string' },
        example: ['AI/ML', 'SaaS', 'Fintech'],
    }),
    __metadata("design:type", Object)
], InvestorDetailsResponseDto.prototype, "focusAreas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Notable investments made by the investor.',
        type: 'array',
        items: { type: 'string' },
        example: ['OpenAI', 'Acme Corp'],
    }),
    __metadata("design:type", Object)
], InvestorDetailsResponseDto.prototype, "notableInvestments", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The primary contact person at the investment firm.',
        example: 'Jane Doe',
    }),
    __metadata("design:type", Object)
], InvestorDetailsResponseDto.prototype, "contactPerson", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The contact email of the investor.',
        example: '<EMAIL>',
    }),
    __metadata("design:type", Object)
], InvestorDetailsResponseDto.prototype, "contactEmail", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the investor details were created',
        example: '2024-07-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], InvestorDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the investor details',
        example: '2024-07-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], InvestorDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=investor-details-response.dto.js.map