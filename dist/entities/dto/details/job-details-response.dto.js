"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JobDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class JobDetailsResponseDto {
}
exports.JobDetailsResponseDto = JobDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'j1k2l3m4-n5o6-p7q8-r9s0-t1u2v3w4x5y6',
    }),
    __metadata("design:type", String)
], JobDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The name of the company.',
        example: 'Awesome AI Inc.',
    }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "companyName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The location of the job (e.g., "Remote", "New York, NY").',
        example: 'Remote',
    }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The salary range for the job.',
        example: '$80k - $120k',
    }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "salaryRange", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: "The type of job (e.g., 'full-time', 'part-time', 'contract').",
        example: 'Full-time',
    }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "jobType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The experience level required for the job.',
        example: 'Mid-Senior',
    }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "experienceLevel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The responsibilities of the job.',
        type: 'array',
        items: { type: 'string' },
        example: ['Developing and deploying AI models.', 'Collaborating with product teams.'],
    }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "responsibilities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The qualifications for the job.',
        type: 'array',
        items: { type: 'string' },
        example: ['3+ years of experience with Python.', 'Experience with TensorFlow or PyTorch.'],
    }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "qualifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the job application page.',
        example: 'https://example.com/job/apply',
    }),
    __metadata("design:type", Object)
], JobDetailsResponseDto.prototype, "applyUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the job details were created',
        example: '2024-08-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], JobDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the job details',
        example: '2024-08-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], JobDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=job-details-response.dto.js.map