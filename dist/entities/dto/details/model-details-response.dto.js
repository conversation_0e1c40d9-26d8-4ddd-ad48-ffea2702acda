"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ModelDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ModelDetailsResponseDto {
}
exports.ModelDetailsResponseDto = ModelDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'k2l3m4n5-o6p7-q8r9-s0t1-u2v3w4x5y6z7',
    }),
    __metadata("design:type", String)
], ModelDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The architecture of the model.',
        example: 'Transformer',
    }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "architecture", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The context length of the model.',
        example: '8192 tokens',
    }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "contextLength", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The developer or organization behind the model.',
        example: 'OpenAI',
    }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "developer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The dataset used to train the model.',
        example: 'Common Crawl, WebText2',
    }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "trainingData", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The number of parameters in the model.',
        example: '175B',
    }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "parameters", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Common use cases for the model.',
        type: 'array',
        items: { type: 'string' },
        example: ['Text generation', 'Translation', 'Summarization'],
    }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "useCases", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to download or access the model.',
        example: 'https://huggingface.co/openai/gpt-3',
    }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "accessUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The license of the model.',
        example: 'MIT',
    }),
    __metadata("design:type", Object)
], ModelDetailsResponseDto.prototype, "license", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the model details were created',
        example: '2024-09-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], ModelDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the model details',
        example: '2024-09-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], ModelDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=model-details-response.dto.js.map