"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class NewsDetailsResponseDto {
}
exports.NewsDetailsResponseDto = NewsDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'l3m4n5o6-p7q8-r9s0-t1u2-v3w4x5y6z7a8',
    }),
    __metadata("design:type", String)
], NewsDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The author of the news article.',
        example: 'Jane Doe',
    }),
    __metadata("design:type", Object)
], NewsDetailsResponseDto.prototype, "author", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The name of the source of the news.',
        example: 'TechCrunch',
    }),
    __metadata("design:type", Object)
], NewsDetailsResponseDto.prototype, "source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the news article.',
        example: 'https://techcrunch.com/2024/10/01/ai-breakthrough/',
    }),
    __metadata("design:type", Object)
], NewsDetailsResponseDto.prototype, "articleUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The publication date of the news.',
        example: '2024-10-01T00:00:00.000Z',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", Object)
], NewsDetailsResponseDto.prototype, "publishedDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the news details were created',
        example: '2024-10-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], NewsDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the news details',
        example: '2024-10-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], NewsDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=news-details-response.dto.js.map