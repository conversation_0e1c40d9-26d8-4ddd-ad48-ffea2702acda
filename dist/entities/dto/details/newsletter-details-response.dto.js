"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewsletterDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class NewsletterDetailsResponseDto {
}
exports.NewsletterDetailsResponseDto = NewsletterDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'n4o5p6q7-r8s9-t0u1-v2w3-x4y5z6a7b8c9',
    }),
    __metadata("design:type", String)
], NewsletterDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The frequency of the newsletter.',
        example: 'Weekly',
    }),
    __metadata("design:type", Object)
], NewsletterDetailsResponseDto.prototype, "frequency", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The main topics of the newsletter.',
        type: [String],
        example: ['AI News', 'Machine Learning', 'Data Science'],
    }),
    __metadata("design:type", Object)
], NewsletterDetailsResponseDto.prototype, "mainTopics", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the archive of the newsletter.',
        example: 'https://example.com/newsletter/archive',
    }),
    __metadata("design:type", Object)
], NewsletterDetailsResponseDto.prototype, "archiveUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to subscribe to the newsletter.',
        example: 'https://example.com/newsletter/subscribe',
    }),
    __metadata("design:type", Object)
], NewsletterDetailsResponseDto.prototype, "subscribeUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The name of the author of the newsletter.',
        example: 'John Doe',
    }),
    __metadata("design:type", Object)
], NewsletterDetailsResponseDto.prototype, "authorName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The number of subscribers to the newsletter.',
        example: 10000,
        type: 'integer',
    }),
    __metadata("design:type", Object)
], NewsletterDetailsResponseDto.prototype, "subscriberCount", void 0);
//# sourceMappingURL=newsletter-details-response.dto.js.map