"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectReferenceDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ProjectReferenceDetailsResponseDto {
}
exports.ProjectReferenceDetailsResponseDto = ProjectReferenceDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'q7r8s9t0-u1v2-w3x4-y5z6-a7b8c9d0e1f2',
    }),
    __metadata("design:type", String)
], ProjectReferenceDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The problem statement the project addresses.',
        example: 'Existing solutions for X are too slow.',
    }),
    __metadata("design:type", Object)
], ProjectReferenceDetailsResponseDto.prototype, "problemStatement", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'An overview of the solution provided by the project.',
        example: 'We built a new pipeline using Y that improves performance by 50%.',
    }),
    __metadata("design:type", Object)
], ProjectReferenceDetailsResponseDto.prototype, "solutionOverview", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The goals of the project.',
        type: 'array',
        items: { type: 'string' },
        example: ['Demonstrate new AI model capabilities', 'Achieve 99% accuracy'],
    }),
    __metadata("design:type", Object)
], ProjectReferenceDetailsResponseDto.prototype, "projectGoals", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Key features of the project.',
        type: 'array',
        items: { type: 'string' },
        example: ['Real-time processing', 'Scalable architecture'],
    }),
    __metadata("design:type", Object)
], ProjectReferenceDetailsResponseDto.prototype, "keyFeatures", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The technologies used in the project.',
        type: 'array',
        items: { type: 'string' },
        example: ['React', 'Node.js', 'PostgreSQL'],
    }),
    __metadata("design:type", Object)
], ProjectReferenceDetailsResponseDto.prototype, "technologiesUsed", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to a live version of the project.',
        example: 'https://example.com/project/live',
    }),
    __metadata("design:type", Object)
], ProjectReferenceDetailsResponseDto.prototype, "liveUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the source code repository of the project.',
        example: 'https://github.com/example/project',
    }),
    __metadata("design:type", Object)
], ProjectReferenceDetailsResponseDto.prototype, "repoUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the project reference details were created',
        example: '2025-01-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], ProjectReferenceDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the project reference details',
        example: '2025-01-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], ProjectReferenceDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=project-reference-details-response.dto.js.map