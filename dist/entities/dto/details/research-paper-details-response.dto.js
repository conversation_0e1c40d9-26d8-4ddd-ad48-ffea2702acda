"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResearchPaperDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ResearchPaperDetailsResponseDto {
}
exports.ResearchPaperDetailsResponseDto = ResearchPaperDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 'r8s9t0u1-v2w3-x4y5-z6a7-b8c9d0e1f2g3',
    }),
    __metadata("design:type", String)
], ResearchPaperDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The authors of the research paper.',
        type: 'array',
        items: { type: 'string' },
        example: ['Yann LeCun', 'Yoshua Bengio', 'Geoffrey Hinton'],
    }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "authors", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The journal or conference where the research paper was published.',
        example: 'Nature',
    }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "journal", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The publication date of the research paper.',
        example: '2023-11-15T00:00:00.000Z',
        type: String,
        format: 'date-time',
    }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "publicationDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The DOI of the research paper.',
        example: '10.1109/5.771073',
    }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "doi", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the PDF of the research paper.',
        example: 'https://www.nature.com/articles/nature14539.pdf',
    }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "pdfUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the code repository for the research paper.',
        example: 'https://github.com/example/paper-code',
    }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "codeRepoUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The abstract of the research paper.',
        example: 'Deep learning is a class of machine learning algorithms that...',
    }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "abstract", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The citation count of the research paper.',
        example: 45000,
        type: 'integer',
    }),
    __metadata("design:type", Object)
], ResearchPaperDetailsResponseDto.prototype, "citations", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the research paper details were created',
        example: '2025-02-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], ResearchPaperDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the research paper details',
        example: '2025-02-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], ResearchPaperDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=research-paper-details-response.dto.js.map