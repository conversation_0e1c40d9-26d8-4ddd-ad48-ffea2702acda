"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceProviderDetailsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ServiceProviderDetailsResponseDto {
}
exports.ServiceProviderDetailsResponseDto = ServiceProviderDetailsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Entity ID (UUID) this detail record is associated with.',
        example: 's9t0u1v2-w3x4-y5z6-a7b8-c9d0e1f2g3h4',
    }),
    __metadata("design:type", String)
], ServiceProviderDetailsResponseDto.prototype, "entityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The services offered by the provider.',
        type: 'array',
        items: { type: 'string' },
        example: ['AI Development', 'Data Science Consulting', 'MLOps'],
    }),
    __metadata("design:type", Object)
], ServiceProviderDetailsResponseDto.prototype, "servicesOffered", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Information about pricing.',
        example: 'Project-based, hourly rates available upon request.',
    }),
    __metadata("design:type", Object)
], ServiceProviderDetailsResponseDto.prototype, "pricingInfo", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The primary contact person.',
        example: 'Jane Doe',
    }),
    __metadata("design:type", Object)
], ServiceProviderDetailsResponseDto.prototype, "contactPerson", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The contact email for inquiries.',
        example: '<EMAIL>',
    }),
    __metadata("design:type", Object)
], ServiceProviderDetailsResponseDto.prototype, "contactEmail", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URL to the provider portfolio or case studies.',
        example: 'https://example.com/portfolio',
    }),
    __metadata("design:type", Object)
], ServiceProviderDetailsResponseDto.prototype, "portfolioUrl", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'The physical location or service area of the provider.',
        example: 'San Francisco, CA',
    }),
    __metadata("design:type", Object)
], ServiceProviderDetailsResponseDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Languages spoken by the service provider.',
        type: 'array',
        items: { type: 'string' },
        example: ['English', 'Spanish'],
    }),
    __metadata("design:type", Object)
], ServiceProviderDetailsResponseDto.prototype, "languagesSpoken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the service provider details were created',
        example: '2025-03-01T00:00:00.000Z',
    }),
    __metadata("design:type", Date)
], ServiceProviderDetailsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the last update to the service provider details',
        example: '2025-03-10T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], ServiceProviderDetailsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=service-provider-details-response.dto.js.map