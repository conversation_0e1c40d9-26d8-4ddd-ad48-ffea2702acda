import { PricingModel, PriceRange } from '@generated-prisma';
export declare class SoftwareDetailsResponseDto {
    entityId: string;
    keyFeatures?: any | null;
    useCases?: any | null;
    integrations?: any | null;
    targetAudience?: any | null;
    deploymentOptions?: any | null;
    supportedOs?: any | null;
    mobileSupport?: boolean | null;
    apiAccess?: boolean | null;
    hasFreeTier?: boolean | null;
    pricingModel?: PricingModel | null;
    priceRange?: PriceRange | null;
    pricingDetails?: string | null;
    pricingUrl?: string | null;
    supportChannels?: any | null;
    supportEmail?: string | null;
    hasLiveChat?: boolean | null;
    communityUrl?: string | null;
    createdAt: Date;
    updatedAt: Date;
}
