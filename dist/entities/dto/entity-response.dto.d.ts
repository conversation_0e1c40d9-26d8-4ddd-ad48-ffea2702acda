import { EntityTypeResponseDto } from '../../entity-types/dto/entity-type-response.dto';
import { UserProfileMinimalDto } from '../../auth/dto/user-profile-minimal.dto';
import { CategoryResponseDto } from '../../categories/dto/category-response.dto';
import { TagResponseDto } from '../../tags/dto/tag-response.dto';
import { EntityStatus, AffiliateStatus, EmployeeCountRange, FundingStage } from '@generated-prisma';
import { ReviewResponseDto } from '../../reviews/dto/review-response.dto';
import { FeatureResponseDto } from '../../features/dto/feature-response.dto';
export declare class EntityResponseDto {
    id: string;
    name: string;
    slug: string;
    websiteUrl?: string | null;
    entityType?: EntityTypeResponseDto | null;
    shortDescription?: string | null;
    description?: string | null;
    logoUrl?: string | null;
    documentationUrl?: string | null;
    contactUrl?: string | null;
    privacyPolicyUrl?: string | null;
    foundedYear?: number | null;
    status: EntityStatus;
    socialLinks?: any | null;
    submitter?: UserProfileMinimalDto | null;
    legacyId?: string | null;
    reviewCount: number;
    avgRating: number;
    createdAt: Date;
    updatedAt: Date;
    metaTitle?: string | null;
    metaDescription?: string | null;
    scrapedReviewSentimentLabel?: string | null;
    scrapedReviewSentimentScore?: number | null;
    scrapedReviewCount?: number | null;
    employeeCountRange?: EmployeeCountRange | null;
    fundingStage?: FundingStage | null;
    locationSummary?: string | null;
    refLink?: string | null;
    affiliateStatus?: AffiliateStatus | null;
    saveCount: number;
    details?: any;
    categories: CategoryResponseDto[];
    tags: TagResponseDto[];
    features?: FeatureResponseDto[];
    reviews?: ReviewResponseDto[];
}
