import { EntityStatus, Prisma, EmployeeCountRange, FundingStage, PricingModel, PriceRange } from '@generated-prisma';
export declare class ListEntitiesDto {
    page?: number;
    limit?: number;
    status?: EntityStatus;
    entityTypeIds?: string[];
    categoryIds?: string[];
    tagIds?: string[];
    featureIds?: string[];
    searchTerm?: string;
    sortBy?: string;
    sortOrder?: Prisma.SortOrder;
    submitterId?: string;
    createdAtFrom?: Date;
    createdAtTo?: Date;
    hasFreeTier?: boolean;
    employeeCountRanges?: EmployeeCountRange[];
    fundingStages?: FundingStage[];
    locationSearch?: string;
    apiAccess?: boolean;
    pricingModels?: PricingModel[];
    priceRanges?: PriceRange[];
    integrations?: string[];
    platforms?: string[];
    targetAudience?: string[];
}
