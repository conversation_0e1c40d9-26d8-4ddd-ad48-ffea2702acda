"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaginatedEntityResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const entity_list_item_response_dto_1 = require("./entity-list-item-response.dto");
const paginated_response_dto_1 = require("../../common/dto/paginated-response.dto");
class PaginatedEntityResponseDto extends paginated_response_dto_1.PaginatedResponseDto {
}
exports.PaginatedEntityResponseDto = PaginatedEntityResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        type: () => [entity_list_item_response_dto_1.EntityListItemResponseDto],
        description: 'List of entities for the current page.',
    }),
    __metadata("design:type", Array)
], PaginatedEntityResponseDto.prototype, "data", void 0);
//# sourceMappingURL=paginated-entity-response.dto.js.map