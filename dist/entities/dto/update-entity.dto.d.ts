import { CreateEntityDto } from './create-entity.dto';
import { UpdateToolDetailsDto } from './details/update-tool-details.dto';
import { UpdateCourseDetailsDto } from './details/update-course-details.dto';
import { UpdateAgencyDetailsDto } from './details/update-agency-details.dto';
import { UpdateContentCreatorDetailsDto } from './details/update-content-creator-details.dto';
import { UpdateCommunityDetailsDto } from './details/update-community-details.dto';
import { UpdateNewsletterDetailsDto } from './details/update-newsletter-details.dto';
import { UpdateDatasetDetailsDto } from './details/update-dataset-details.dto';
import { UpdateResearchPaperDetailsDto } from './details/update-research-paper-details.dto';
import { UpdateSoftwareDetailsDto } from './details/update-software-details.dto';
import { UpdateModelDetailsDto } from './details/update-model-details.dto';
import { UpdateProjectReferenceDetailsDto } from './details/update-project-reference-details.dto';
import { UpdateServiceProviderDetailsDto } from './details/update-service-provider-details.dto';
import { UpdateInvestorDetailsDto } from './details/update-investor-details.dto';
import { UpdateEventDetailsDto } from './details/update-event-details.dto';
import { UpdateJobDetailsDto } from './details/update-job-details.dto';
import { UpdateGrantDetailsDto } from './details/update-grant-details.dto';
import { UpdateBountyDetailsDto } from './details/update-bounty-details.dto';
import { UpdateHardwareDetailsDto } from './details/update-hardware-details.dto';
import { UpdateNewsDetailsDto } from './details/update-news-details.dto';
import { UpdateBookDetailsDto } from './details/update-book-details.dto';
import { UpdatePodcastDetailsDto } from './details/update-podcast-details.dto';
import { UpdatePlatformDetailsDto } from './details/update-platform-details.dto';
declare const UpdateEntityDto_base: import("@nestjs/common").Type<Partial<CreateEntityDto>>;
export declare class UpdateEntityDto extends UpdateEntityDto_base {
    tool_details?: UpdateToolDetailsDto;
    course_details?: UpdateCourseDetailsDto;
    agency_details?: UpdateAgencyDetailsDto;
    content_creator_details?: UpdateContentCreatorDetailsDto;
    community_details?: UpdateCommunityDetailsDto;
    newsletter_details?: UpdateNewsletterDetailsDto;
    dataset_details?: UpdateDatasetDetailsDto;
    research_paper_details?: UpdateResearchPaperDetailsDto;
    software_details?: UpdateSoftwareDetailsDto;
    model_details?: UpdateModelDetailsDto;
    project_reference_details?: UpdateProjectReferenceDetailsDto;
    service_provider_details?: UpdateServiceProviderDetailsDto;
    investor_details?: UpdateInvestorDetailsDto;
    event_details?: UpdateEventDetailsDto;
    job_details?: UpdateJobDetailsDto;
    grant_details?: UpdateGrantDetailsDto;
    bounty_details?: UpdateBountyDetailsDto;
    hardware_details?: UpdateHardwareDetailsDto;
    news_details?: UpdateNewsDetailsDto;
    book_details?: UpdateBookDetailsDto;
    podcast_details?: UpdatePodcastDetailsDto;
    platform_details?: UpdatePlatformDetailsDto;
}
export {};
