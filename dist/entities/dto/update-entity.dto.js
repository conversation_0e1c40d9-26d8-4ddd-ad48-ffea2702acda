"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateEntityDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const create_entity_dto_1 = require("./create-entity.dto");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const update_tool_details_dto_1 = require("./details/update-tool-details.dto");
const update_course_details_dto_1 = require("./details/update-course-details.dto");
const update_agency_details_dto_1 = require("./details/update-agency-details.dto");
const update_content_creator_details_dto_1 = require("./details/update-content-creator-details.dto");
const update_community_details_dto_1 = require("./details/update-community-details.dto");
const update_newsletter_details_dto_1 = require("./details/update-newsletter-details.dto");
const update_dataset_details_dto_1 = require("./details/update-dataset-details.dto");
const update_research_paper_details_dto_1 = require("./details/update-research-paper-details.dto");
const update_software_details_dto_1 = require("./details/update-software-details.dto");
const update_model_details_dto_1 = require("./details/update-model-details.dto");
const update_project_reference_details_dto_1 = require("./details/update-project-reference-details.dto");
const update_service_provider_details_dto_1 = require("./details/update-service-provider-details.dto");
const update_investor_details_dto_1 = require("./details/update-investor-details.dto");
const update_event_details_dto_1 = require("./details/update-event-details.dto");
const update_job_details_dto_1 = require("./details/update-job-details.dto");
const update_grant_details_dto_1 = require("./details/update-grant-details.dto");
const update_bounty_details_dto_1 = require("./details/update-bounty-details.dto");
const update_hardware_details_dto_1 = require("./details/update-hardware-details.dto");
const update_news_details_dto_1 = require("./details/update-news-details.dto");
const update_book_details_dto_1 = require("./details/update-book-details.dto");
const update_podcast_details_dto_1 = require("./details/update-podcast-details.dto");
const update_platform_details_dto_1 = require("./details/update-platform-details.dto");
class UpdateEntityDto extends (0, swagger_1.PartialType)(create_entity_dto_1.CreateEntityDto) {
}
exports.UpdateEntityDto = UpdateEntityDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for an AI Tool' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_tool_details_dto_1.UpdateToolDetailsDto),
    __metadata("design:type", update_tool_details_dto_1.UpdateToolDetailsDto)
], UpdateEntityDto.prototype, "tool_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Course' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_course_details_dto_1.UpdateCourseDetailsDto),
    __metadata("design:type", update_course_details_dto_1.UpdateCourseDetailsDto)
], UpdateEntityDto.prototype, "course_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for an Agency' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_agency_details_dto_1.UpdateAgencyDetailsDto),
    __metadata("design:type", update_agency_details_dto_1.UpdateAgencyDetailsDto)
], UpdateEntityDto.prototype, "agency_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Content Creator' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_content_creator_details_dto_1.UpdateContentCreatorDetailsDto),
    __metadata("design:type", update_content_creator_details_dto_1.UpdateContentCreatorDetailsDto)
], UpdateEntityDto.prototype, "content_creator_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Community' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_community_details_dto_1.UpdateCommunityDetailsDto),
    __metadata("design:type", update_community_details_dto_1.UpdateCommunityDetailsDto)
], UpdateEntityDto.prototype, "community_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Newsletter' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_newsletter_details_dto_1.UpdateNewsletterDetailsDto),
    __metadata("design:type", update_newsletter_details_dto_1.UpdateNewsletterDetailsDto)
], UpdateEntityDto.prototype, "newsletter_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Dataset' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_dataset_details_dto_1.UpdateDatasetDetailsDto),
    __metadata("design:type", update_dataset_details_dto_1.UpdateDatasetDetailsDto)
], UpdateEntityDto.prototype, "dataset_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Research Paper' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_research_paper_details_dto_1.UpdateResearchPaperDetailsDto),
    __metadata("design:type", update_research_paper_details_dto_1.UpdateResearchPaperDetailsDto)
], UpdateEntityDto.prototype, "research_paper_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for Software' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_software_details_dto_1.UpdateSoftwareDetailsDto),
    __metadata("design:type", update_software_details_dto_1.UpdateSoftwareDetailsDto)
], UpdateEntityDto.prototype, "software_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Model' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_model_details_dto_1.UpdateModelDetailsDto),
    __metadata("design:type", update_model_details_dto_1.UpdateModelDetailsDto)
], UpdateEntityDto.prototype, "model_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Project Reference' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_project_reference_details_dto_1.UpdateProjectReferenceDetailsDto),
    __metadata("design:type", update_project_reference_details_dto_1.UpdateProjectReferenceDetailsDto)
], UpdateEntityDto.prototype, "project_reference_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Service Provider' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_service_provider_details_dto_1.UpdateServiceProviderDetailsDto),
    __metadata("design:type", update_service_provider_details_dto_1.UpdateServiceProviderDetailsDto)
], UpdateEntityDto.prototype, "service_provider_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for an Investor' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_investor_details_dto_1.UpdateInvestorDetailsDto),
    __metadata("design:type", update_investor_details_dto_1.UpdateInvestorDetailsDto)
], UpdateEntityDto.prototype, "investor_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for an Event' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_event_details_dto_1.UpdateEventDetailsDto),
    __metadata("design:type", update_event_details_dto_1.UpdateEventDetailsDto)
], UpdateEntityDto.prototype, "event_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Job' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_job_details_dto_1.UpdateJobDetailsDto),
    __metadata("design:type", update_job_details_dto_1.UpdateJobDetailsDto)
], UpdateEntityDto.prototype, "job_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Grant' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_grant_details_dto_1.UpdateGrantDetailsDto),
    __metadata("design:type", update_grant_details_dto_1.UpdateGrantDetailsDto)
], UpdateEntityDto.prototype, "grant_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Bounty' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_bounty_details_dto_1.UpdateBountyDetailsDto),
    __metadata("design:type", update_bounty_details_dto_1.UpdateBountyDetailsDto)
], UpdateEntityDto.prototype, "bounty_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for Hardware' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_hardware_details_dto_1.UpdateHardwareDetailsDto),
    __metadata("design:type", update_hardware_details_dto_1.UpdateHardwareDetailsDto)
], UpdateEntityDto.prototype, "hardware_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for News' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_news_details_dto_1.UpdateNewsDetailsDto),
    __metadata("design:type", update_news_details_dto_1.UpdateNewsDetailsDto)
], UpdateEntityDto.prototype, "news_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Book' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_book_details_dto_1.UpdateBookDetailsDto),
    __metadata("design:type", update_book_details_dto_1.UpdateBookDetailsDto)
], UpdateEntityDto.prototype, "book_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Podcast' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_podcast_details_dto_1.UpdatePodcastDetailsDto),
    __metadata("design:type", update_podcast_details_dto_1.UpdatePodcastDetailsDto)
], UpdateEntityDto.prototype, "podcast_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Details for a Platform' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => update_platform_details_dto_1.UpdatePlatformDetailsDto),
    __metadata("design:type", update_platform_details_dto_1.UpdatePlatformDetailsDto)
], UpdateEntityDto.prototype, "platform_details", void 0);
//# sourceMappingURL=update-entity.dto.js.map