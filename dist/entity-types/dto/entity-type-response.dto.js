"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntityTypeResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class EntityTypeResponseDto {
}
exports.EntityTypeResponseDto = EntityTypeResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Entity Type ID (UUID)', example: 'c3d4e5f6-g7h8-9012-3456-789012abcdef' }),
    __metadata("design:type", String)
], EntityTypeResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Name of the entity type', example: 'Software Tool' }),
    __metadata("design:type", String)
], EntityTypeResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'URL-friendly slug for the entity type', example: 'software-tool' }),
    __metadata("design:type", String)
], EntityTypeResponseDto.prototype, "slug", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Detailed description of the entity type', example: 'Represents software applications and tools.' }),
    __metadata("design:type", Object)
], EntityTypeResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'URL of an icon representing the entity type', example: 'https://example.com/icons/software.png' }),
    __metadata("design:type", Object)
], EntityTypeResponseDto.prototype, "iconUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the entity type was created', example: '2023-03-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], EntityTypeResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the entity type', example: '2023-03-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], EntityTypeResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=entity-type-response.dto.js.map