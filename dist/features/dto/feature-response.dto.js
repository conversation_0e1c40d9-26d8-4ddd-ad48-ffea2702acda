"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FeatureResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class FeatureResponseDto {
}
exports.FeatureResponseDto = FeatureResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unique identifier for the feature.',
        example: 'clxrb20vf0000tpol0b1g2c3d',
    }),
    __metadata("design:type", String)
], FeatureResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The name of the feature.',
        example: 'Dark Mode',
    }),
    __metadata("design:type", String)
], FeatureResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL-friendly slug for the feature.',
        example: 'dark-mode',
    }),
    __metadata("design:type", String)
], FeatureResponseDto.prototype, "slug", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'A brief description of the feature.',
        example: 'Enables a dark theme for the user interface.',
        nullable: true,
    }),
    __metadata("design:type", Object)
], FeatureResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'URL of an icon representing the feature.',
        example: 'https://example.com/icons/dark-mode.png',
        nullable: true,
    }),
    __metadata("design:type", Object)
], FeatureResponseDto.prototype, "iconUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the feature was created.',
        example: '2023-01-01T12:00:00.000Z',
    }),
    __metadata("design:type", Date)
], FeatureResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of when the feature was last updated.',
        example: '2023-01-02T12:00:00.000Z',
    }),
    __metadata("design:type", Date)
], FeatureResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=feature-response.dto.js.map