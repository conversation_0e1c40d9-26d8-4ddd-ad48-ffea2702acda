"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var OpenaiService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenaiService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const openai_1 = __importDefault(require("openai"));
let OpenaiService = OpenaiService_1 = class OpenaiService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(OpenaiService_1.name);
        const apiKey = this.configService.get('OPENAI_API_KEY');
        if (!apiKey) {
            throw new Error('OPENAI_API_KEY is not set in environment variables.');
        }
        this.openai = new openai_1.default({ apiKey });
    }
    async generateEmbedding(text) {
        if (!text) {
            this.logger.warn('generateEmbedding called with empty text.');
            return null;
        }
        try {
            const response = await this.openai.embeddings.create({
                model: 'text-embedding-3-small',
                input: text.trim(),
            });
            if (response.data && response.data.length > 0 && response.data[0].embedding) {
                return response.data[0].embedding;
            }
            else {
                this.logger.warn('OpenAI API returned no embedding for the provided text.');
                return null;
            }
        }
        catch (error) {
            this.logger.error('Error generating embedding from OpenAI', error.stack);
            throw error;
        }
    }
};
exports.OpenaiService = OpenaiService;
exports.OpenaiService = OpenaiService = OpenaiService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], OpenaiService);
//# sourceMappingURL=openai.service.js.map