"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateRecommendationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const recommendation_filters_dto_1 = require("./recommendation-filters.dto");
class CreateRecommendationDto {
}
exports.CreateRecommendationDto = CreateRecommendationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Description of the problem or need for which recommendations are requested',
        example: 'I need an AI tool to help me generate code documentation automatically',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateRecommendationDto.prototype, "problem_description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional filters to apply to the search',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => recommendation_filters_dto_1.RecommendationFiltersDto),
    __metadata("design:type", recommendation_filters_dto_1.RecommendationFiltersDto)
], CreateRecommendationDto.prototype, "filters", void 0);
//# sourceMappingURL=create-recommendation.dto.js.map