import { EntityStatus, EmployeeCountRange, FundingStage, PricingModel, PriceRange } from '../../../generated/prisma';
export declare class RecommendationFiltersDto {
    entity_type_ids?: string[];
    category_ids?: string[];
    tag_ids?: string[];
    feature_ids?: string[];
    status?: EntityStatus;
    has_free_tier?: boolean;
    employee_count_ranges?: EmployeeCountRange[];
    funding_stages?: FundingStage[];
    pricing_models?: PricingModel[];
    price_ranges?: PriceRange[];
    max_candidates?: number;
}
