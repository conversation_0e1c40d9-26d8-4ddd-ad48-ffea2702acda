"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecommendationFiltersDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const prisma_1 = require("../../../generated/prisma");
class RecommendationFiltersDto {
    constructor() {
        this.max_candidates = 20;
    }
}
exports.RecommendationFiltersDto = RecommendationFiltersDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter by entity type IDs',
        example: ['10000000-0000-0000-0000-000000000001'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)(4, { each: true }),
    __metadata("design:type", Array)
], RecommendationFiltersDto.prototype, "entity_type_ids", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter by category IDs',
        example: ['*************-0000-0000-000000000001'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)(4, { each: true }),
    __metadata("design:type", Array)
], RecommendationFiltersDto.prototype, "category_ids", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter by tag IDs',
        example: ['*************-0000-0000-000000000001'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)(4, { each: true }),
    __metadata("design:type", Array)
], RecommendationFiltersDto.prototype, "tag_ids", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter by feature IDs',
        example: ['*************-0000-0000-000000000001'],
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)(4, { each: true }),
    __metadata("design:type", Array)
], RecommendationFiltersDto.prototype, "feature_ids", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter by entity status',
        enum: prisma_1.EntityStatus,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(prisma_1.EntityStatus),
    __metadata("design:type", String)
], RecommendationFiltersDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter by whether the entity has a free tier',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], RecommendationFiltersDto.prototype, "has_free_tier", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter by employee count ranges',
        enum: prisma_1.EmployeeCountRange,
        isArray: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(prisma_1.EmployeeCountRange, { each: true }),
    __metadata("design:type", Array)
], RecommendationFiltersDto.prototype, "employee_count_ranges", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter by funding stages',
        enum: prisma_1.FundingStage,
        isArray: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(prisma_1.FundingStage, { each: true }),
    __metadata("design:type", Array)
], RecommendationFiltersDto.prototype, "funding_stages", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter by pricing models',
        enum: prisma_1.PricingModel,
        isArray: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(prisma_1.PricingModel, { each: true }),
    __metadata("design:type", Array)
], RecommendationFiltersDto.prototype, "pricing_models", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Filter by price ranges',
        enum: prisma_1.PriceRange,
        isArray: true,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(prisma_1.PriceRange, { each: true }),
    __metadata("design:type", Array)
], RecommendationFiltersDto.prototype, "price_ranges", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Maximum number of candidate entities to consider for LLM analysis',
        example: 20,
        minimum: 5,
        maximum: 50,
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsInt)(),
    (0, class_validator_1.Min)(5),
    (0, class_validator_1.Max)(50),
    __metadata("design:type", Number)
], RecommendationFiltersDto.prototype, "max_candidates", void 0);
//# sourceMappingURL=recommendation-filters.dto.js.map