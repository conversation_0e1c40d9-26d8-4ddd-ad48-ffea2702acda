"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecommendationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const throttler_1 = require("@nestjs/throttler");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const recommendations_service_1 = require("./recommendations.service");
const create_recommendation_dto_1 = require("./dto/create-recommendation.dto");
const recommendation_response_dto_1 = require("./dto/recommendation-response.dto");
let RecommendationsController = class RecommendationsController {
    constructor(recommendationsService) {
        this.recommendationsService = recommendationsService;
    }
    async getRecommendations(createRecommendationDto) {
        return this.recommendationsService.getRecommendations(createRecommendationDto);
    }
};
exports.RecommendationsController = RecommendationsController;
__decorate([
    (0, common_1.Post)(),
    (0, throttler_1.Throttle)({ default: { limit: 10, ttl: 60000 } }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Get AI-powered recommendations',
        description: `
    Get personalized AI recommendations based on a problem description.
    
    This endpoint:
    1. Uses vector search to find semantically relevant entities
    2. Applies optional filters to narrow down candidates
    3. Uses the configured LLM provider to analyze and recommend the best options
    4. Returns detailed recommendations with explanations
    
    The LLM provider can be configured by admins via the admin settings API.
    `,
    }),
    (0, swagger_1.ApiBody)({
        type: create_recommendation_dto_1.CreateRecommendationDto,
        description: 'Problem description and optional filters for recommendations',
        examples: {
            'Code Documentation Tool': {
                summary: 'Looking for code documentation tools',
                value: {
                    problem_description: 'I need an AI tool to help me generate code documentation automatically for my Python projects',
                    filters: {
                        entity_type_ids: ['10000000-0000-0000-0000-000000000001'],
                        category_ids: ['20000000-0000-0000-0000-000000000001'],
                        has_free_tier: true,
                        max_candidates: 15,
                    },
                },
            },
            'Machine Learning Platform': {
                summary: 'Looking for ML platforms',
                value: {
                    problem_description: 'I want to build and deploy machine learning models for my startup',
                    filters: {
                        category_ids: ['20000000-0000-0000-0000-000000000004'],
                        pricing_models: ['FREEMIUM', 'SUBSCRIPTION'],
                        employee_count_ranges: ['C1_10', 'C11_50'],
                        max_candidates: 20,
                    },
                },
            },
            'AI Learning Resources': {
                summary: 'Looking for AI education',
                value: {
                    problem_description: 'I am a beginner and want to learn about artificial intelligence and machine learning',
                    filters: {
                        entity_type_ids: ['10000000-0000-0000-0000-000000000002'],
                        category_ids: ['20000000-0000-0000-0000-000000000008'],
                        max_candidates: 10,
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'AI recommendations generated successfully',
        type: recommendation_response_dto_1.RecommendationResponseDto,
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Invalid request data',
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Authentication required',
    }),
    (0, swagger_1.ApiResponse)({
        status: 429,
        description: 'Rate limit exceeded',
    }),
    (0, swagger_1.ApiResponse)({
        status: 500,
        description: 'Internal server error during recommendation generation',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_recommendation_dto_1.CreateRecommendationDto]),
    __metadata("design:returntype", Promise)
], RecommendationsController.prototype, "getRecommendations", null);
exports.RecommendationsController = RecommendationsController = __decorate([
    (0, swagger_1.ApiTags)('Recommendations'),
    (0, common_1.Controller)('recommendations'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [recommendations_service_1.RecommendationsService])
], RecommendationsController);
//# sourceMappingURL=recommendations.controller.js.map