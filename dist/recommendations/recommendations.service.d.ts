import { EntitiesService } from '../entities/entities.service';
import { ILlmService } from '../common/llm/interfaces/llm.service.interface';
import { LlmFactoryService } from '../common/llm/services/llm-factory.service';
import { CreateRecommendationDto } from './dto/create-recommendation.dto';
import { RecommendationResponseDto } from './dto/recommendation-response.dto';
export declare class RecommendationsService {
    private readonly entitiesService;
    private readonly llmService;
    private readonly llmFactoryService;
    private readonly logger;
    constructor(entitiesService: EntitiesService, llmService: ILlmService, llmFactoryService: LlmFactoryService);
    getRecommendations(createRecommendationDto: CreateRecommendationDto): Promise<RecommendationResponseDto>;
    private findCandidateEntities;
    private convertToLlmCandidates;
    private getRecommendedEntityDetails;
    private mapToEntityListItemResponseDto;
    private getCurrentLlmProvider;
}
