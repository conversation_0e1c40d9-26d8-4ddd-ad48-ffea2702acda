"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var RecommendationsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RecommendationsService = void 0;
const common_1 = require("@nestjs/common");
const entities_service_1 = require("../entities/entities.service");
const llm_factory_service_1 = require("../common/llm/services/llm-factory.service");
let RecommendationsService = RecommendationsService_1 = class RecommendationsService {
    constructor(entitiesService, llmService, llmFactoryService) {
        this.entitiesService = entitiesService;
        this.llmService = llmService;
        this.llmFactoryService = llmFactoryService;
        this.logger = new common_1.Logger(RecommendationsService_1.name);
    }
    async getRecommendations(createRecommendationDto) {
        const { problem_description, filters } = createRecommendationDto;
        const maxCandidates = filters?.max_candidates || 20;
        this.logger.log(`Getting recommendations for problem: "${problem_description}" with max ${maxCandidates} candidates`);
        try {
            const candidateEntities = await this.findCandidateEntities(problem_description, filters, maxCandidates);
            this.logger.log(`Found ${candidateEntities.length} candidate entities`);
            if (candidateEntities.length === 0) {
                return {
                    recommended_entities: [],
                    explanation: 'No relevant entities found for your query. Please try a different description or adjust your filters.',
                    problem_description,
                    candidates_analyzed: 0,
                    llm_provider: 'N/A',
                    generated_at: new Date(),
                };
            }
            const llmCandidates = this.convertToLlmCandidates(candidateEntities);
            const llmRecommendation = await this.llmService.getRecommendation(problem_description, llmCandidates);
            const recommendedEntities = await this.getRecommendedEntityDetails(llmRecommendation.recommendedEntityIds, candidateEntities);
            const currentProvider = await this.getCurrentLlmProvider();
            return {
                recommended_entities: recommendedEntities,
                explanation: llmRecommendation.explanation,
                problem_description,
                candidates_analyzed: candidateEntities.length,
                llm_provider: currentProvider,
                generated_at: new Date(),
            };
        }
        catch (error) {
            this.logger.error('Error generating recommendations', error.stack);
            throw error;
        }
    }
    async findCandidateEntities(problemDescription, filters, maxCandidates) {
        const listEntitiesDto = {
            searchTerm: problemDescription,
            limit: maxCandidates,
            page: 1,
            status: filters?.status,
            entityTypeIds: filters?.entity_type_ids,
            categoryIds: filters?.category_ids,
            tagIds: filters?.tag_ids,
            featureIds: filters?.feature_ids,
            hasFreeTier: filters?.has_free_tier,
            employeeCountRanges: filters?.employee_count_ranges,
            fundingStages: filters?.funding_stages,
            pricingModels: filters?.pricing_models,
            priceRanges: filters?.price_ranges,
        };
        const result = await this.entitiesService.findAll(listEntitiesDto);
        return result.data;
    }
    convertToLlmCandidates(entities) {
        return entities.map((entity) => ({
            id: entity.id,
            name: entity.name,
            shortDescription: entity.shortDescription,
            description: entity.description,
            entityType: {
                name: entity.entityType.name,
                slug: entity.entityType.slug,
            },
            categories: entity.entityCategories || [],
            tags: entity.entityTags || [],
            features: entity.entityFeatures || [],
            websiteUrl: entity.websiteUrl,
            logoUrl: entity.logoUrl,
            avgRating: entity.avgRating,
            reviewCount: entity.reviewCount,
        }));
    }
    async getRecommendedEntityDetails(recommendedIds, candidateEntities) {
        const recommendedEntities = recommendedIds
            .map((id) => candidateEntities.find((entity) => entity.id === id))
            .filter((entity) => entity !== undefined);
        return recommendedEntities.map((entity) => this.mapToEntityListItemResponseDto(entity));
    }
    mapToEntityListItemResponseDto(entity) {
        return {
            id: entity.id,
            name: entity.name,
            slug: entity.slug,
            shortDescription: entity.shortDescription,
            logoUrl: entity.logoUrl,
            websiteUrl: entity.websiteUrl,
            entityType: entity.entityType,
            avgRating: entity.avgRating,
            reviewCount: entity.reviewCount,
            categories: entity.entityCategories?.map((ec) => ec.category) || [],
            tags: entity.entityTags?.map((et) => et.tag) || [],
            features: entity.entityFeatures?.map((ef) => ef.feature) || [],
            createdAt: entity.createdAt,
            updatedAt: entity.updatedAt,
        };
    }
    async getCurrentLlmProvider() {
        try {
            const providers = this.llmFactoryService.getAvailableProviders();
            return 'OPENAI';
        }
        catch (error) {
            this.logger.warn('Could not determine current LLM provider', error.message);
            return 'UNKNOWN';
        }
    }
};
exports.RecommendationsService = RecommendationsService;
exports.RecommendationsService = RecommendationsService = RecommendationsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_1.Inject)('ILlmService')),
    __metadata("design:paramtypes", [entities_service_1.EntitiesService, Object, llm_factory_service_1.LlmFactoryService])
], RecommendationsService);
//# sourceMappingURL=recommendations.service.js.map