import { ReviewsService } from './reviews.service';
import { UpdateReviewStatusDto } from './dto/update-review-status.dto';
import { ListAdminReviewsDto } from '../admin/reviews/dto/list-admin-reviews.dto';
export declare class AdminReviewsController {
    private readonly reviewsService;
    constructor(reviewsService: ReviewsService);
    findAll(listAdminReviewsDto: ListAdminReviewsDto): Promise<import("../common/interfaces/paginated-response.interface").PaginatedResponse<{
        id: string;
        entityId: string;
        userId: string;
        rating: number;
        title: string | null;
        content: string | null;
        status: import("../../generated/prisma").$Enums.ReviewStatus;
        moderatorId: string | null;
        moderationNotes: string | null;
        upvotes: number;
        downvotes: number;
        createdAt: Date;
        updatedAt: Date;
    } & {
        user: {
            id: string;
            username: string | null;
            email: string;
        };
        entity: {
            id: string;
            name: string;
        };
    }>>;
    updateReviewStatus(id: string, updateReviewStatusDto: UpdateReviewStatusDto, req: any): Promise<{
        id: string;
        entityId: string;
        userId: string;
        rating: number;
        title: string | null;
        content: string | null;
        status: import("../../generated/prisma").$Enums.ReviewStatus;
        moderatorId: string | null;
        moderationNotes: string | null;
        upvotes: number;
        downvotes: number;
        createdAt: Date;
        updatedAt: Date;
    }>;
    adminDeleteReview(id: string, req: any): Promise<void>;
}
