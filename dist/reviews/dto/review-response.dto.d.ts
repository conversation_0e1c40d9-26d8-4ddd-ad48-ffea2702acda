import { ReviewStatus } from '../../../generated/prisma';
declare class ReviewUserResponseDto {
    id: string;
    username?: string;
    displayName?: string;
    profilePictureUrl?: string;
}
export declare class ReviewResponseDto {
    id: string;
    rating: number;
    title?: string;
    comment?: string;
    pros?: string;
    cons?: string;
    status: ReviewStatus;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    user?: ReviewUserResponseDto;
    entityId: string;
    upvotes?: number;
    downvotes?: number;
}
export {};
