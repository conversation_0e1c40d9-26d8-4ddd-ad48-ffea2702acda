"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class TagResponseDto {
}
exports.TagResponseDto = TagResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'c1b2a3d4-e5f6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], TagResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Artificial Intelligence' }),
    __metadata("design:type", String)
], TagResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'artificial-intelligence' }),
    __metadata("design:type", String)
], TagResponseDto.prototype, "slug", void 0);
//# sourceMappingURL=tag-response.dto.js.map