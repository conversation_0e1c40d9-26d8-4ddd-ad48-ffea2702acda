"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublicTagsController = void 0;
const common_1 = require("@nestjs/common");
const tags_service_1 = require("./tags.service");
const swagger_1 = require("@nestjs/swagger");
const tag_response_dto_1 = require("./dto/tag-response.dto");
let PublicTagsController = class PublicTagsController {
    constructor(tagsService) {
        this.tagsService = tagsService;
    }
    mapToResponseDto(tag) {
        return {
            id: tag.id,
            name: tag.name,
            slug: tag.slug,
        };
    }
    async findAll() {
        const tags = await this.tagsService.findAllPublic();
        return tags.map(tag => this.mapToResponseDto(tag));
    }
};
exports.PublicTagsController = PublicTagsController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all public tags' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of all public tags.', type: [tag_response_dto_1.TagResponseDto] }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PublicTagsController.prototype, "findAll", null);
exports.PublicTagsController = PublicTagsController = __decorate([
    (0, swagger_1.ApiTags)('Public - Tags'),
    (0, common_1.Controller)('tags'),
    __metadata("design:paramtypes", [tags_service_1.TagsService])
], PublicTagsController);
//# sourceMappingURL=public-tags.controller.js.map