import { TagsService } from './tags.service';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { TagResponseDto } from './dto/tag-response.dto';
import { AdminTagListResponseDto } from '../admin/tags/dto/admin-tag-list-response.dto';
export declare class TagsController {
    private readonly tagsService;
    constructor(tagsService: TagsService);
    create(createTagDto: CreateTagDto): Promise<TagResponseDto>;
    findAll(page?: number, limit?: number): Promise<AdminTagListResponseDto>;
    findOne(id: string): Promise<TagResponseDto>;
    update(id: string, updateTagDto: UpdateTagDto): Promise<TagResponseDto>;
    remove(id: string): Promise<TagResponseDto>;
    private mapToTagResponseDto;
}
