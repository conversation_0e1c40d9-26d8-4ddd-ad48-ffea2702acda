"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagsController = void 0;
const common_1 = require("@nestjs/common");
const tags_service_1 = require("./tags.service");
const create_tag_dto_1 = require("./dto/create-tag.dto");
const update_tag_dto_1 = require("./dto/update-tag.dto");
const admin_guard_1 = require("../auth/guards/admin.guard");
const swagger_1 = require("@nestjs/swagger");
const tag_response_dto_1 = require("./dto/tag-response.dto");
const admin_tag_list_response_dto_1 = require("../admin/tags/dto/admin-tag-list-response.dto");
let TagsController = class TagsController {
    constructor(tagsService) {
        this.tagsService = tagsService;
    }
    async create(createTagDto) {
        const tag = await this.tagsService.create(createTagDto);
        return this.mapToTagResponseDto(tag);
    }
    async findAll(page, limit) {
        const { data, count, totalPages, currentPage } = await this.tagsService.findAll(page, limit);
        return {
            data: data.map(this.mapToTagResponseDto),
            total: count,
            page: currentPage,
            limit: limit || 10,
            totalPages,
        };
    }
    async findOne(id) {
        const tag = await this.tagsService.findOne(id);
        if (!tag)
            throw new common_1.NotFoundException(`Tag with ID ${id} not found.`);
        return this.mapToTagResponseDto(tag);
    }
    async update(id, updateTagDto) {
        const tag = await this.tagsService.update(id, updateTagDto);
        if (!tag)
            throw new common_1.NotFoundException(`Tag with ID ${id} not found or update failed.`);
        return this.mapToTagResponseDto(tag);
    }
    async remove(id) {
        const tag = await this.tagsService.remove(id);
        if (!tag)
            throw new common_1.NotFoundException(`Tag with ID ${id} not found or delete failed.`);
        return this.mapToTagResponseDto(tag);
    }
    mapToTagResponseDto(tag) {
        return {
            id: tag.id,
            name: tag.name,
            slug: tag.slug,
        };
    }
};
exports.TagsController = TagsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new tag' }),
    (0, swagger_1.ApiBody)({ type: create_tag_dto_1.CreateTagDto }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'The tag has been successfully created.', type: tag_response_dto_1.TagResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Conflict. Tag with this name or slug already exists.' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_tag_dto_1.CreateTagDto]),
    __metadata("design:returntype", Promise)
], TagsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all tags with pagination' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Page number for pagination', example: 1 }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'List of all tags.', type: admin_tag_list_response_dto_1.AdminTagListResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    __param(0, (0, common_1.Query)('page', new common_1.ParseIntPipe({ optional: true }))),
    __param(1, (0, common_1.Query)('limit', new common_1.ParseIntPipe({ optional: true }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], TagsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a tag by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'Tag ID (UUID)', format: 'uuid' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'The found tag.', type: tag_response_dto_1.TagResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Tag not found.' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TagsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a tag by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'Tag ID (UUID)', format: 'uuid' }),
    (0, swagger_1.ApiBody)({ type: update_tag_dto_1.UpdateTagDto }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'The tag has been successfully updated.', type: tag_response_dto_1.TagResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad Request.' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Tag not found.' }),
    (0, swagger_1.ApiResponse)({ status: 409, description: 'Conflict. Tag with this name or slug already exists.' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_tag_dto_1.UpdateTagDto]),
    __metadata("design:returntype", Promise)
], TagsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a tag by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', type: String, description: 'Tag ID (UUID)', format: 'uuid' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'The tag has been successfully deleted.', type: tag_response_dto_1.TagResponseDto }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized.' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden.' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Tag not found.' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TagsController.prototype, "remove", null);
exports.TagsController = TagsController = __decorate([
    (0, swagger_1.ApiTags)('Admin - Tags'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(admin_guard_1.AdminGuard),
    (0, common_1.Controller)('admin/tags'),
    __metadata("design:paramtypes", [tags_service_1.TagsService])
], TagsController);
//# sourceMappingURL=tags.controller.js.map