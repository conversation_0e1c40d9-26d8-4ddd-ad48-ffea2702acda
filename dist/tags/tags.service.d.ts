import { PrismaService } from '../prisma/prisma.service';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { Tag } from 'generated/prisma';
export declare class TagsService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    create(createTagDto: CreateTagDto): Promise<Tag>;
    findAll(page?: number, limit?: number): Promise<{
        data: Tag[];
        count: number;
        totalPages: number;
        currentPage: number;
    }>;
    findAllPublic(): Promise<Tag[]>;
    findOne(id: string): Promise<Tag>;
    update(id: string, updateTagDto: UpdateTagDto): Promise<Tag>;
    remove(id: string): Promise<Tag>;
}
