"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestController = void 0;
const common_1 = require("@nestjs/common");
const test_service_1 = require("./test.service");
let TestController = class TestController {
    constructor(testService) {
        this.testService = testService;
    }
    async testEntityTypeCrud() {
        console.log('--- Running EntityType CRUD Test ---');
        const result = await this.testService.testCreateReadEntityType();
        console.log('--- CRUD Test Step 1 (Create/Read) Complete --- ');
        const createdSlug = result.createdType.slug;
        console.log(`--- CRUD Test Step 2 (Update ${createdSlug}) ---`);
        const updated = await this.testService.testUpdateEntityType(createdSlug);
        console.log('--- CRUD Test Step 2 (Update) Complete --- ');
        console.log(`--- CRUD Test Step 3 (Delete ${createdSlug}) ---`);
        const deletionResult = await this.testService.testDeleteEntityType(createdSlug);
        console.log('--- CRUD Test Step 3 (Delete) Complete --- ');
        console.log('--- EntityType CRUD Test Finished ---');
        return { createReadResult: result, updateResult: updated, deleteResult: deletionResult };
    }
    async testEntityRelation() {
        console.log('--- Running Entity Relation Test ---');
        const result = await this.testService.testCreateEntityWithRelation();
        console.log('--- Entity Relation Test Finished --- Please clean up DB manually --- ');
        return result;
    }
    async testEntityTagM2MRelation() {
        console.log('--- Running Entity-Tag M2M Relation Test ---');
        const result = await this.testService.testEntityTagRelation();
        console.log('--- Entity-Tag M2M Relation Test Finished (DB cleanup attempted) --- ');
        return result;
    }
    async testEnumConstraints() {
        console.log('--- Running ENUM Constraint Test ---');
        const result = await this.testService.testEnums();
        console.log('--- ENUM Constraint Test Finished --- ');
        return result;
    }
    async testUniqueConstraints() {
        console.log('--- Running Unique Constraint Test ---');
        const result = await this.testService.testUniqueConstraints();
        console.log('--- Unique Constraint Test Finished --- ');
        return result;
    }
    async testOptionalDefaultsAndTimestamps() {
        console.log('--- Running Optional/Default/Timestamp Test ---');
        const result = await this.testService.testOptionalDefaultsUpdatedAt();
        console.log('--- Optional/Default/Timestamp Test Finished --- ');
        return result;
    }
};
exports.TestController = TestController;
__decorate([
    (0, common_1.Post)('entity-type/crud'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TestController.prototype, "testEntityTypeCrud", null);
__decorate([
    (0, common_1.Post)('entity/relation'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TestController.prototype, "testEntityRelation", null);
__decorate([
    (0, common_1.Post)('entity/m2m-relation'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TestController.prototype, "testEntityTagM2MRelation", null);
__decorate([
    (0, common_1.Post)('constraints/enum'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TestController.prototype, "testEnumConstraints", null);
__decorate([
    (0, common_1.Post)('constraints/unique'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TestController.prototype, "testUniqueConstraints", null);
__decorate([
    (0, common_1.Post)('constraints/optional-default-timestamp'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TestController.prototype, "testOptionalDefaultsAndTimestamps", null);
exports.TestController = TestController = __decorate([
    (0, common_1.Controller)('test'),
    __metadata("design:paramtypes", [test_service_1.TestService])
], TestController);
//# sourceMappingURL=test.controller.js.map