"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserNotificationSettingsResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class UserNotificationSettingsResponseDto {
}
exports.UserNotificationSettingsResponseDto = UserNotificationSettingsResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique identifier for the notification settings record.', example: 'clq2q5q7p0001c8e4b9g7h1i3' }),
    __metadata("design:type", String)
], UserNotificationSettingsResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User ID associated with these settings.', example: 'clq2q5q7p0000c8e4a9g7h1i2' }),
    __metadata("design:type", String)
], UserNotificationSettingsResponseDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Enable email notifications for new comments on user\'s submitted entities.', example: true, default: true }),
    __metadata("design:type", Boolean)
], UserNotificationSettingsResponseDto.prototype, "newCommentOnEntity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Enable email notifications for replies to user\'s comments.', example: true, default: true }),
    __metadata("design:type", Boolean)
], UserNotificationSettingsResponseDto.prototype, "replyToComment", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Enable email notifications when a submitted entity status changes (e.g., approved, rejected).', example: true, default: true }),
    __metadata("design:type", Boolean)
], UserNotificationSettingsResponseDto.prototype, "entityStatusChange", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Enable email notifications for new reviews on user\'s submitted entities.', example: true, default: true }),
    __metadata("design:type", Boolean)
], UserNotificationSettingsResponseDto.prototype, "newReviewOnEntity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Enable email notifications for general platform announcements.', example: false, default: false }),
    __metadata("design:type", Boolean)
], UserNotificationSettingsResponseDto.prototype, "platformAnnouncements", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Enable email notifications for a weekly/monthly digest.', example: false, default: false }),
    __metadata("design:type", Boolean)
], UserNotificationSettingsResponseDto.prototype, "digestSubscription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when these settings were created.', example: '2023-01-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], UserNotificationSettingsResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to these settings.', example: '2023-01-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], UserNotificationSettingsResponseDto.prototype, "updatedAt", void 0);
//# sourceMappingURL=user-notification-settings-response.dto.js.map