import { UserRole, UserStatus } from '../../../generated/prisma';
export declare class UserProfileResponseDto {
    id: string;
    authUserId: string;
    email: string;
    username?: string | null;
    displayName?: string | null;
    profilePictureUrl?: string | null;
    bio?: string | null;
    status: UserStatus;
    role: UserRole;
    createdAt: Date;
    updatedAt: Date;
    lastLoginAt?: Date | null;
}
