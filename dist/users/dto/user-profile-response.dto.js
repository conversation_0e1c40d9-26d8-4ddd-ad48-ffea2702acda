"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserProfileResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const prisma_1 = require("../../../generated/prisma");
class UserProfileResponseDto {
}
exports.UserProfileResponseDto = UserProfileResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User ID (from public.users table)', example: 'clq2q5q7p0000c8e4a9g7h1i2' }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Auth User ID (from auth.users table)', example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "authUserId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User\'s email address', example: '<EMAIL>' }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unique username', example: 'john_doe_123', required: false, nullable: true }),
    __metadata("design:type", Object)
], UserProfileResponseDto.prototype, "username", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User\'s display name', example: 'John Doe', required: false, nullable: true }),
    __metadata("design:type", Object)
], UserProfileResponseDto.prototype, "displayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'URL of the user\'s profile picture', example: 'https://example.com/profile.jpg', required: false, nullable: true }),
    __metadata("design:type", Object)
], UserProfileResponseDto.prototype, "profilePictureUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User\'s biography or short description', example: 'Loves coding and coffee.', required: false, nullable: true }),
    __metadata("design:type", Object)
], UserProfileResponseDto.prototype, "bio", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: prisma_1.UserStatus, description: 'Current status of the user account', example: prisma_1.UserStatus.ACTIVE }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ enum: prisma_1.UserRole, description: 'Role of the user within the system', example: prisma_1.UserRole.USER }),
    __metadata("design:type", String)
], UserProfileResponseDto.prototype, "role", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of when the user profile was created', example: '2023-01-01T00:00:00.000Z' }),
    __metadata("design:type", Date)
], UserProfileResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last update to the user profile', example: '2023-01-10T10:00:00.000Z' }),
    __metadata("design:type", Date)
], UserProfileResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Timestamp of the last login from auth.users', example: '2023-01-10T09:00:00.000Z', required: false, nullable: true }),
    __metadata("design:type", Object)
], UserProfileResponseDto.prototype, "lastLoginAt", void 0);
//# sourceMappingURL=user-profile-response.dto.js.map