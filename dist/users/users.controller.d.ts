import { UserService } from './users.service';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { UpdateNotificationSettingsDto } from './dto/update-notification-settings.dto';
import { UserProfileResponseDto } from './dto/user-profile-response.dto';
import { UserNotificationSettingsResponseDto } from './dto/user-notification-settings-response.dto';
import { User as PrismaUser } from '../../generated/prisma';
export declare class UserController {
    private readonly userService;
    constructor(userService: UserService);
    getMyProfile(user: PrismaUser): Promise<UserProfileResponseDto>;
    updateMyProfile(user: PrismaUser, updateProfileDto: UpdateProfileDto): Promise<UserProfileResponseDto>;
    getMyNotificationSettings(user: PrismaUser): Promise<UserNotificationSettingsResponseDto | null>;
    updateMyNotificationSettings(user: PrismaUser, updateDto: UpdateNotificationSettingsDto): Promise<UserNotificationSettingsResponseDto>;
    softDeleteMyAccount(user: PrismaUser): Promise<{
        message: string;
    }>;
}
