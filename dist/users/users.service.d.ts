import { PrismaService } from '../prisma/prisma.service';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { User, UserNotificationSettings, UserStatus, UserRole } from 'generated/prisma';
import { UpdateNotificationSettingsDto } from './dto/update-notification-settings.dto';
import { SupabaseService } from '../supabase/supabase.service';
export interface AdminUserListItem {
    id: string;
    username: string | null;
    displayName: string | null;
    email: string;
    role: UserRole;
    status: UserStatus;
    createdAt: Date;
    updatedAt: Date;
    lastLogin: Date | null;
}
type UserSortableFields = 'createdAt' | 'username' | 'email' | 'status' | 'role' | 'lastLogin' | 'updatedAt' | 'displayName';
export declare class UserService {
    private readonly prismaService;
    private readonly supabaseService;
    constructor(prismaService: PrismaService, supabaseService: SupabaseService);
    findProfileById(userId: string): Promise<User | null>;
    updateProfile(userId: string, dto: UpdateProfileDto): Promise<User>;
    findNotificationSettingsByUserId(userId: string): Promise<UserNotificationSettings | null>;
    updateNotificationSettings(userId: string, dto: UpdateNotificationSettingsDto): Promise<UserNotificationSettings>;
    softDeleteUser(userId: string, authUserId: string): Promise<void>;
    findAllUsers(options: {
        page?: number;
        limit?: number;
        filterByStatus?: UserStatus;
        sortBy?: UserSortableFields;
        sortOrder?: 'asc' | 'desc';
    }): Promise<{
        users: AdminUserListItem[];
        total: number;
        page: number;
        limit: number;
    }>;
    updateUserStatus(adminPerformingUpdate: User, targetUserId: string, newStatus: UserStatus): Promise<User>;
    updateUserRole(adminPerformingUpdate: User, targetUserId: string, newRole: UserRole): Promise<User>;
}
export {};
