"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mapTagsToConnectOrCreate = exports.mapCategoriesToConnectOrCreate = void 0;
const mapCategoriesToConnectOrCreate = (categoryIds) => {
    if (!categoryIds || categoryIds.length === 0) {
        return undefined;
    }
    return {
        connectOrCreate: categoryIds.map((id) => ({
            where: { id },
            create: { id },
        })),
    };
};
exports.mapCategoriesToConnectOrCreate = mapCategoriesToConnectOrCreate;
const mapTagsToConnectOrCreate = (tagIds) => {
    if (!tagIds || tagIds.length === 0) {
        return undefined;
    }
    return {
        connectOrCreate: tagIds.map((id) => ({
            where: { id },
            create: { id },
        })),
    };
};
exports.mapTagsToConnectOrCreate = mapTagsToConnectOrCreate;
//# sourceMappingURL=prisma-helpers.js.map