-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public._prisma_migrations (
  id character varying NOT NULL,
  checksum character varying NOT NULL,
  finished_at timestamp with time zone,
  migration_name character varying NOT NULL,
  logs text,
  rolled_back_at timestamp with time zone,
  started_at timestamp with time zone NOT NULL DEFAULT now(),
  applied_steps_count integer NOT NULL DEFAULT 0,
  CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id)
);
CREATE TABLE public.badge_types (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  slug text NOT NULL,
  description text NOT NULL,
  icon_url text NOT NULL,
  criteria_details text,
  scope USER-DEFINED NOT NULL,
  is_auto_granted boolean NOT NULL DEFAULT false,
  is_manual_granted boolean NOT NULL DEFAULT true,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  CONSTRAINT badge_types_pkey PRIMARY KEY (id)
);
CREATE TABLE public.categories (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  slug text NOT NULL,
  parent_category_id uuid,
  icon_url text,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  CONSTRAINT categories_pkey PRIMARY KEY (id),
  CONSTRAINT fk_category_parent_category_id FOREIGN KEY (parent_category_id) REFERENCES public.categories(id)
);
CREATE TABLE public.entities (
  id uuid NOT NULL,
  entity_type_id uuid NOT NULL,
  name text NOT NULL,
  short_description text,
  description text,
  logo_url text,
  website_url text,
  documentation_url text,
  contact_url text,
  privacy_policy_url text,
  founded_year integer,
  social_links jsonb,
  status USER-DEFINED NOT NULL DEFAULT 'PENDING'::"EntityStatus",
  avg_rating double precision NOT NULL DEFAULT 0,
  review_count integer NOT NULL DEFAULT 0,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  legacy_id text,
  submitter_id uuid NOT NULL,
  CONSTRAINT entities_pkey PRIMARY KEY (id),
  CONSTRAINT fk_entity_submitter_id FOREIGN KEY (submitter_id) REFERENCES public.users(id),
  CONSTRAINT fk_entity_entity_type_id FOREIGN KEY (entity_type_id) REFERENCES public.entity_types(id)
);
CREATE TABLE public.entity_badges (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  entity_id uuid NOT NULL,
  badge_type_id uuid NOT NULL,
  granted_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  granted_by_user_id uuid,
  notes text,
  CONSTRAINT entity_badges_pkey PRIMARY KEY (id),
  CONSTRAINT entity_badges_badge_type_id_fkey FOREIGN KEY (badge_type_id) REFERENCES public.badge_types(id),
  CONSTRAINT entity_badges_entity_id_fkey FOREIGN KEY (entity_id) REFERENCES public.entities(id),
  CONSTRAINT entity_badges_granted_by_user_id_fkey FOREIGN KEY (granted_by_user_id) REFERENCES public.users(id)
);
CREATE TABLE public.entity_categories (
  entity_id uuid NOT NULL,
  category_id uuid NOT NULL,
  assigned_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT entity_categories_pkey PRIMARY KEY (entity_id, category_id),
  CONSTRAINT fk_entity_category_category_id FOREIGN KEY (category_id) REFERENCES public.categories(id),
  CONSTRAINT fk_entity_category_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_agency (
  entity_id uuid NOT NULL,
  services_offered jsonb,
  industry_focus jsonb,
  target_client_size jsonb,
  target_audience jsonb,
  location_summary text,
  portfolio_url text,
  pricing_info text,
  CONSTRAINT entity_details_agency_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_agency_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_book (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  authorNames jsonb,
  isbn text,
  page_count integer,
  publication_year integer,
  publisher text,
  purchase_url text,
  summary text,
  CONSTRAINT entity_details_book_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_book_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_bounty (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  bounty_issuer text,
  difficulty_level text,
  platform_url text,
  requirements text,
  reward_amount text,
  submission_deadline date,
  CONSTRAINT entity_details_bounty_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_bounty_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_community (
  entity_id uuid NOT NULL,
  platform text,
  member_count integer DEFAULT 0,
  focus_topics jsonb,
  rules_url text,
  invite_url text,
  main_channel_url text,
  CONSTRAINT entity_details_community_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_community_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_content_creator (
  entity_id uuid NOT NULL,
  creator_name text,
  primary_platform text,
  focus_areas jsonb,
  follower_count integer DEFAULT 0,
  example_content_url text,
  CONSTRAINT entity_details_content_creator_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_content_creator_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_course (
  entity_id uuid NOT NULL,
  instructor_name text,
  duration_text text,
  skill_level USER-DEFINED,
  prerequisites text,
  syllabus_url text,
  enrollment_count integer DEFAULT 0,
  certificate_available boolean DEFAULT false,
  CONSTRAINT entity_details_course_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_course_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_dataset (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  access_notes text,
  description text,
  format text,
  license text,
  size_in_bytes bigint,
  source_url text,
  CONSTRAINT entity_details_dataset_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_dataset_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_event (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  agenda_url text,
  end_date timestamp without time zone,
  eventType text,
  location text,
  price text,
  registration_url text,
  speakerList jsonb,
  start_date timestamp without time zone,
  CONSTRAINT entity_details_event_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_event_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_grant (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  application_deadline date,
  application_url text,
  eligibility_criteria text,
  funding_amount text,
  grant_focus_area text,
  granting_institution text,
  CONSTRAINT entity_details_grant_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_grant_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_hardware (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  datasheet_url text,
  hardware_type text,
  manufacturer text,
  price_range text,
  release_date date,
  specifications jsonb,
  CONSTRAINT entity_details_hardware_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_hardware_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_investor (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  contact_email text,
  investmentFocusAreas jsonb,
  investmentStages jsonb,
  portfolio_url text,
  preferred_communication text,
  typical_investment_size text,
  CONSTRAINT entity_details_investor_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_investor_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_job (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  application_url text,
  company_name text,
  employment_type text,
  experience_level text,
  job_description text,
  job_title text,
  location_type text,
  salary_range text,
  CONSTRAINT entity_details_job_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_job_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_model (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  license text,
  model_architecture text,
  model_url text,
  parameters_count bigint,
  performanceMetrics jsonb,
  training_dataset text,
  CONSTRAINT entity_details_model_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_model_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_news (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  article_url text NOT NULL,
  author text,
  publication_date date,
  source_name text,
  summary text,
  CONSTRAINT entity_details_news_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_news_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_newsletter (
  entity_id uuid NOT NULL,
  frequency text,
  main_topics jsonb,
  archive_url text,
  subscribe_url text,
  author_name text,
  subscriber_count integer DEFAULT 0,
  CONSTRAINT entity_details_newsletter_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_newsletter_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_platform (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  documentation_url text,
  keyServices jsonb,
  platform_type text,
  pricing_model text,
  sla_url text,
  supported_regions jsonb,
  CONSTRAINT entity_details_platform_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_platform_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_podcast (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  average_episode_length text,
  frequency text,
  hostNames jsonb,
  listen_url text,
  mainTopics jsonb,
  primary_language text DEFAULT 'English'::text,
  CONSTRAINT entity_details_podcast_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_podcast_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_project_reference (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  contributors jsonb,
  live_demo_url text,
  project_goals text,
  project_status text,
  source_code_url text,
  technologies jsonb,
  CONSTRAINT entity_details_project_reference_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_project_reference_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_research_paper (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  abstract text,
  authors jsonb,
  citation_count integer DEFAULT 0,
  doi text,
  journal_or_conference text,
  publication_date date,
  publication_url text,
  CONSTRAINT entity_details_research_paper_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_research_paper_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_service_provider (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  case_studies_url text,
  company_size_focus text,
  consultation_booking_url text,
  hourly_rate_range text,
  industrySpecializations jsonb,
  serviceAreas jsonb,
  CONSTRAINT entity_details_service_provider_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_service_provider_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_software (
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  current_version text,
  license_type text,
  platformCompatibility jsonb,
  programmingLanguages jsonb,
  release_date date,
  repository_url text,
  CONSTRAINT entity_details_software_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_software_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_details_tool (
  entity_id uuid NOT NULL,
  technical_level USER-DEFINED,
  learning_curve USER-DEFINED,
  target_audience jsonb,
  has_api boolean NOT NULL DEFAULT false,
  api_documentation_url text,
  api_sandbox_url text,
  key_features jsonb,
  use_cases jsonb,
  pricing_model USER-DEFINED,
  price_range USER-DEFINED,
  pricing_details text,
  pricing_url text,
  has_free_tier boolean NOT NULL DEFAULT false,
  platforms jsonb,
  integrations jsonb,
  supported_languages jsonb,
  current_version text,
  last_version_update_date date,
  changelog_url text,
  CONSTRAINT entity_details_tool_pkey PRIMARY KEY (entity_id),
  CONSTRAINT fk_entity_details_tool_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.entity_features (
  entity_id uuid NOT NULL,
  feature_id uuid NOT NULL,
  assigned_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT entity_features_pkey PRIMARY KEY (entity_id, feature_id),
  CONSTRAINT fk_entity_feature_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id),
  CONSTRAINT fk_entity_feature_feature_id FOREIGN KEY (feature_id) REFERENCES public.features(id)
);
CREATE TABLE public.entity_tags (
  entity_id uuid NOT NULL,
  tag_id uuid NOT NULL,
  CONSTRAINT entity_tags_pkey PRIMARY KEY (entity_id, tag_id),
  CONSTRAINT fk_entity_tag_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id),
  CONSTRAINT fk_entity_tag_tag_id FOREIGN KEY (tag_id) REFERENCES public.tags(id)
);
CREATE TABLE public.entity_types (
  id uuid NOT NULL,
  name text NOT NULL,
  description text,
  slug text NOT NULL,
  icon_url text,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  CONSTRAINT entity_types_pkey PRIMARY KEY (id)
);
CREATE TABLE public.features (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  slug text NOT NULL,
  description text,
  icon_url text,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  CONSTRAINT features_pkey PRIMARY KEY (id)
);
CREATE TABLE public.review_votes (
  review_id uuid NOT NULL,
  user_id uuid NOT NULL,
  vote smallint NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT review_votes_pkey PRIMARY KEY (review_id, user_id),
  CONSTRAINT review_votes_review_id_fkey FOREIGN KEY (review_id) REFERENCES public.reviews(id),
  CONSTRAINT review_votes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.reviews (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  entity_id uuid NOT NULL,
  user_id uuid NOT NULL,
  rating integer NOT NULL,
  title text,
  review_text text,
  status USER-DEFINED NOT NULL DEFAULT 'PENDING'::"ReviewStatus",
  helpfulness_score integer NOT NULL DEFAULT 0,
  moderator_user_id uuid,
  moderated_at timestamp without time zone,
  moderation_notes text,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  CONSTRAINT reviews_pkey PRIMARY KEY (id),
  CONSTRAINT fk_review_moderator_id FOREIGN KEY (moderator_user_id) REFERENCES public.users(id),
  CONSTRAINT fk_review_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id),
  CONSTRAINT fk_review_user_id FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.tags (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  name text NOT NULL,
  description text,
  slug text NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  icon_url text,
  CONSTRAINT tags_pkey PRIMARY KEY (id)
);
CREATE TABLE public.user_activity_logs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  action_type USER-DEFINED NOT NULL,
  entity_id uuid,
  category_id uuid,
  tag_id uuid,
  review_id uuid,
  target_user_id uuid,
  timestamp timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  details jsonb,
  CONSTRAINT user_activity_logs_pkey PRIMARY KEY (id),
  CONSTRAINT fk_user_activity_log_tag_id FOREIGN KEY (tag_id) REFERENCES public.tags(id),
  CONSTRAINT fk_user_activity_log_category_id FOREIGN KEY (category_id) REFERENCES public.categories(id),
  CONSTRAINT fk_user_activity_log_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id),
  CONSTRAINT fk_user_activity_log_review_id FOREIGN KEY (review_id) REFERENCES public.reviews(id),
  CONSTRAINT fk_user_activity_log_target_user_id FOREIGN KEY (target_user_id) REFERENCES public.users(id),
  CONSTRAINT fk_user_activity_log_user_id FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.user_badges (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL,
  badge_type_id uuid NOT NULL,
  granted_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  granted_by_user_id uuid,
  notes text,
  CONSTRAINT user_badges_pkey PRIMARY KEY (id),
  CONSTRAINT user_badges_badge_type_id_fkey FOREIGN KEY (badge_type_id) REFERENCES public.badge_types(id),
  CONSTRAINT user_badges_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT user_badges_granted_by_user_id_fkey FOREIGN KEY (granted_by_user_id) REFERENCES public.users(id)
);
CREATE TABLE public.user_followed_categories (
  user_id uuid NOT NULL,
  category_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT user_followed_categories_pkey PRIMARY KEY (user_id, category_id),
  CONSTRAINT user_followed_categories_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT user_followed_categories_category_id_fkey FOREIGN KEY (category_id) REFERENCES public.categories(id)
);
CREATE TABLE public.user_followed_tags (
  user_id uuid NOT NULL,
  tag_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT user_followed_tags_pkey PRIMARY KEY (user_id, tag_id),
  CONSTRAINT user_followed_tags_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT user_followed_tags_tag_id_fkey FOREIGN KEY (tag_id) REFERENCES public.tags(id)
);
CREATE TABLE public.user_notification_settings (
  user_id uuid NOT NULL,
  email_newsletter boolean NOT NULL DEFAULT true,
  email_new_entity_in_followed_category boolean NOT NULL DEFAULT true,
  email_new_entity_in_followed_tag boolean NOT NULL DEFAULT false,
  email_new_review_on_saved_entity boolean NOT NULL DEFAULT true,
  email_updates_on_saved_entity boolean NOT NULL DEFAULT false,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  CONSTRAINT user_notification_settings_pkey PRIMARY KEY (user_id),
  CONSTRAINT user_notification_settings_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.user_saved_entities (
  user_id uuid NOT NULL,
  entity_id uuid NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT user_saved_entities_pkey PRIMARY KEY (user_id, entity_id),
  CONSTRAINT fk_user_saved_entity_user_id FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT fk_user_saved_entity_entity_id FOREIGN KEY (entity_id) REFERENCES public.entities(id)
);
CREATE TABLE public.users (
  id uuid NOT NULL,
  auth_user_id uuid NOT NULL,
  username text,
  display_name text,
  email text NOT NULL,
  role USER-DEFINED NOT NULL DEFAULT 'USER'::"UserRole",
  status USER-DEFINED NOT NULL DEFAULT 'ACTIVE'::"UserStatus",
  technical_level USER-DEFINED,
  profile_picture_url text,
  bio text,
  social_links jsonb,
  created_at timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at timestamp without time zone NOT NULL,
  last_login timestamp without time zone,
  CONSTRAINT users_pkey PRIMARY KEY (id)
);