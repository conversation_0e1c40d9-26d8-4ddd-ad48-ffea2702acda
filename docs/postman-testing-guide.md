# Postman Testing Guide - AI Navigator Backend

## Quick Start for Testing

### 1. Authentication Setup

First, you need to authenticate to get a JWT token:

#### Step 1: Create a Test User (if needed)
```
POST http://localhost:3001/auth/signup
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "TestPassword123!",
  "username": "testuser",
  "display_name": "Test User"
}
```

#### Step 2: Login to Get JWT Token
```
POST http://localhost:3001/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "TestPassword123!"
}
```

**Response will include:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "username": "testuser"
  }
}
```

#### Step 3: Use the Token for Protected Endpoints
Add this header to all protected requests:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2. Test User Profile (Verify Authentication)

```
GET http://localhost:3001/users/me
Authorization: Bearer <your-jwt-token>
```

This should return your user profile. If this works, authentication is properly configured.

### 3. Test Entity Creation

First, you need to get an entity type ID:

#### Get Entity Types
```
GET http://localhost:3001/entity-types
```

#### Create an Entity
```
POST http://localhost:3001/entities
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "name": "Test AI Tool",
  "website_url": "https://testaitool.com",
  "entity_type_id": "82d988e5-df78-4d63-a1a3-ed2159cf0c68",
  "short_description": "A test AI tool for demonstration",
  "description": "This is a longer description of the test AI tool that provides various AI capabilities.",
  "logo_url": "https://testaitool.com/logo.png",
  "documentation_url": "https://testaitool.com/docs",
  "contact_url": "https://testaitool.com/contact",
  "privacy_policy_url": "https://testaitool.com/privacy",
  "founded_year": 2023,
  "social_links": {
    "twitter": "https://twitter.com/testaitool",
    "linkedin": "https://linkedin.com/company/testaitool"
  },
  "entity_details_tool": {
    "api_documentation_url": "https://testaitool.com/api-docs",
    "learning_curve": "LOW",
    "key_features": [
      "Easy to use interface",
      "Advanced AI capabilities",
      "Real-time processing"
    ],
    "has_free_tier": true,
    "use_cases": [
      "Data Analysis",
      "Content Generation"
    ],
    "pricing_model": "FREEMIUM",
    "price_range": "$0-$99/month",
    "pricing_details": "Free tier available with premium features",
    "pricing_url": "https://testaitool.com/pricing",
    "integrations": [
      "Slack",
      "Google Drive",
      "Microsoft Teams"
    ],
    "support_email": "<EMAIL>",
    "has_live_chat": true,
    "community_url": "https://community.testaitool.com",
    "programming_languages": [
      "Python",
      "JavaScript"
    ],
    "frameworks": [
      "React",
      "FastAPI"
    ],
    "libraries": [
      "TensorFlow",
      "PyTorch"
    ],
    "target_audience": [
      "Developers",
      "Data Scientists"
    ],
    "deployment_options": [
      "Cloud",
      "On-premise"
    ],
    "supported_os": [
      "Windows",
      "macOS",
      "Linux"
    ],
    "mobile_support": true,
    "api_access": true,
    "customization_level": "High",
    "trial_available": true,
    "demo_available": true,
    "open_source": false,
    "support_channels": [
      "Email",
      "Chat",
      "Documentation"
    ]
  }
}
```

### 4. Common Test Scenarios

#### List Entities with Filters
```
GET http://localhost:3001/entities?page=1&limit=10&status=APPROVED&searchTerm=AI
```

#### Get Single Entity
```
GET http://localhost:3001/entities/{entity-id}
```

#### Update Entity (as owner)
```
PATCH http://localhost:3001/entities/{entity-id}
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "name": "Updated AI Tool Name",
  "short_description": "Updated description"
}
```

#### Create Review
```
POST http://localhost:3001/entities/{entity-id}/reviews
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "rating": 5,
  "title": "Excellent Tool",
  "reviewText": "This AI tool has significantly improved my workflow. The interface is intuitive and the results are consistently high quality."
}
```

#### Bookmark Entity
```
POST http://localhost:3001/bookmarks
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "entity_id": "{entity-id}"
}
```

#### List Bookmarks
```
GET http://localhost:3001/bookmarks
Authorization: Bearer <your-jwt-token>
```

### 5. Admin Testing (if you have admin role)

#### List All Users
```
GET http://localhost:3001/admin/users
Authorization: Bearer <admin-jwt-token>
```

#### Update User Status
```
PUT http://localhost:3001/admin/users/{user-id}/status
Authorization: Bearer <admin-jwt-token>
Content-Type: application/json

{
  "status": "SUSPENDED"
}
```

#### Moderate Review
```
PATCH http://localhost:3001/admin/reviews/{review-id}/status
Authorization: Bearer <admin-jwt-token>
Content-Type: application/json

{
  "status": "APPROVED"
}
```

### 6. Error Testing

#### Test Invalid Token
```
GET http://localhost:3001/users/me
Authorization: Bearer invalid-token
```
Should return 401 Unauthorized.

#### Test Missing Required Fields
```
POST http://localhost:3001/entities
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "name": "Test"
  // Missing required fields
}
```
Should return 400 Bad Request with validation errors.

#### Test Invalid UUID
```
GET http://localhost:3001/entities/invalid-uuid
```
Should return 400 Bad Request.

### 7. Rate Limiting Testing

Try making multiple rapid requests to auth endpoints:

```
POST http://localhost:3001/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "wrongpassword"
}
```

After 10 failed attempts in a minute, you should get rate limited.

### 8. Troubleshooting

#### Common Issues:

1. **401 Unauthorized on protected endpoints**
   - Check that you're including the `Authorization: Bearer <token>` header
   - Verify the token is valid and not expired
   - Ensure you've completed the signup/login flow

2. **400 Bad Request on entity creation**
   - Check that all required fields are provided
   - Verify the `entity_type_id` exists (get it from `/entity-types`)
   - Ensure URLs are properly formatted

3. **User profile not found error**
   - This indicates the JWT token is valid but the user doesn't exist in the database
   - Try the `/auth/sync-profile` endpoint to sync your profile

4. **Database connection errors**
   - Ensure PostgreSQL is running
   - Check your `.env` file has the correct `DATABASE_URL`
   - Run the database fix script if needed

### 9. Postman Collection Setup

You can create a Postman collection with these requests and use environment variables:

**Environment Variables:**
- `base_url`: `http://localhost:3001`
- `jwt_token`: `{{access_token}}` (set from login response)
- `entity_id`: (set from entity creation response)
- `user_id`: (set from profile response)

**Pre-request Script for Auth:**
```javascript
// Auto-set token from login response
if (pm.response && pm.response.json() && pm.response.json().access_token) {
    pm.environment.set("jwt_token", pm.response.json().access_token);
}
```

This guide should help you thoroughly test all the API endpoints and verify that the authentication fixes are working correctly.
