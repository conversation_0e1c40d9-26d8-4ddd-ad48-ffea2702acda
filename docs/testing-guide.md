# Testing Guide - AI Navigator Backend

## Overview

This guide covers the comprehensive testing strategy implemented for the AI Navigator Backend, including unit tests, integration tests, and testing best practices.

## Testing Stack

- **Jest**: Testing framework with TypeScript support
- **Supertest**: HTTP assertion library for integration tests
- **@nestjs/testing**: NestJS testing utilities
- **Custom Mocks**: Prisma service mocking for database operations

## Test Structure

```
src/
├── test/
│   ├── setup.ts                    # Global test setup
│   ├── mocks/
│   │   └── prisma.service.mock.ts  # Prisma service mock
│   └── testing.module.ts           # Testing module helper
├── features/
│   ├── features.service.spec.ts    # Unit tests
│   └── features.controller.spec.ts # Integration tests
├── entities/
│   └── entities.service.spec.ts    # Unit tests
├── users/
│   └── users.service.spec.ts       # Unit tests
├── reviews/
│   └── reviews.service.spec.ts     # Unit tests
└── auth/
    └── auth.controller.spec.ts     # Integration tests
```

## Memory Leak Prevention

Our Jest configuration includes several memory leak prevention measures:

```javascript
// jest.config.js
module.exports = {
  // Prevent memory leaks
  maxWorkers: 1,
  forceExit: true,
  detectOpenHandles: true,
  
  // Clear mocks between tests
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,
  
  // Global setup
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
};
```

## Prisma Mocking Strategy

We use a comprehensive mocking strategy to avoid database connections during tests:

### Global Mock Setup
```typescript
// src/test/setup.ts
jest.mock('@generated-prisma', () => ({
  PrismaClient: jest.fn().mockImplementation(() => ({
    // Mock all Prisma methods
  })),
}));
```

### Service-Level Mocking
```typescript
// src/test/mocks/prisma.service.mock.ts
export const mockPrismaService = {
  feature: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  // ... other models
};
```

## Running Tests

### Basic Commands

```bash
# Run all tests
npm test

# Run with coverage
npm run test:cov

# Run in watch mode
npm run test:watch

# Run specific test suites
npm run test:unit          # Unit tests only
npm run test:integration   # Integration tests only
npm run test:features      # Features module tests
npm run test:entities      # Entities module tests
npm run test:users         # Users module tests
npm run test:reviews       # Reviews module tests
npm run test:auth          # Auth module tests
```

### Advanced Test Runner

Use our custom test runner for enhanced reporting:

```bash
# Run all tests with detailed reporting
npm run test:script

# Run only unit tests
npm run test:script unit

# Run only integration tests
npm run test:script integration

# Run tests matching a pattern
npm run test:script pattern "features"

# Show help
npm run test:script help
```

## Test Categories

### Unit Tests

Unit tests focus on testing individual service methods in isolation:

- **Features Service**: CRUD operations, slug generation, validation
- **Entities Service**: Complex business logic, filtering, permissions
- **Users Service**: Profile management, admin operations, soft deletion
- **Reviews Service**: Review lifecycle, moderation, permissions

Example unit test structure:
```typescript
describe('FeaturesService', () => {
  let service: FeaturesService;
  let prisma: typeof mockPrismaService;

  beforeEach(async () => {
    // Setup test module with mocked dependencies
  });

  describe('create', () => {
    it('should create a feature with unique slug', async () => {
      // Test implementation
    });
  });
});
```

### Integration Tests

Integration tests verify the complete request-response cycle:

- **Features Controller**: API endpoints, validation, error handling
- **Auth Controller**: Authentication flows, rate limiting

Example integration test:
```typescript
describe('FeaturesController (Integration)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    // Setup test application
  });

  it('should create a feature successfully', async () => {
    const response = await request(app.getHttpServer())
      .post('/features')
      .send(createFeatureDto)
      .expect(201);
  });
});
```

## Structured Logging Implementation

### Logger Service

We've implemented structured logging with correlation IDs:

```typescript
// src/common/logger/logger.service.ts
@Injectable()
export class AppLoggerService implements LoggerService {
  logRequest(method: string, url: string, context?: LogContext): void {
    // Structured JSON logging
  }
  
  logError(error: Error, context?: LogContext): void {
    // Error logging with correlation ID
  }
}
```

### Correlation ID Middleware

```typescript
// src/common/middleware/correlation-id.middleware.ts
@Injectable()
export class CorrelationIdMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction): void {
    const correlationId = req.headers['x-correlation-id'] || generateUuid();
    res.setHeader('x-correlation-id', correlationId);
    (req as any).correlationId = correlationId;
    next();
  }
}
```

## Rate Limiting Implementation

Specific throttle decorators are applied to sensitive endpoints:

```typescript
// Auth endpoints with rate limiting
@Post('signup')
@Throttle({ default: { limit: 5, ttl: 60000 } }) // 5 attempts per minute
async signUp(@Body() registerAuthDto: RegisterAuthDto) {
  // Implementation
}

@Post('login')
@Throttle({ default: { limit: 10, ttl: 60000 } }) // 10 attempts per minute
async signIn(@Body() loginAuthDto: LoginAuthDto) {
  // Implementation
}
```

## Best Practices

### Test Organization
- Group related tests using `describe` blocks
- Use descriptive test names that explain the expected behavior
- Follow the AAA pattern: Arrange, Act, Assert

### Mock Management
- Reset mocks between tests using `resetPrismaMocks()`
- Use specific mock implementations for each test case
- Verify mock calls with `expect().toHaveBeenCalledWith()`

### Error Testing
- Test both success and failure scenarios
- Verify specific error types and messages
- Test edge cases and boundary conditions

### Performance
- Use `maxWorkers: 1` to prevent memory issues
- Enable `forceExit` and `detectOpenHandles`
- Clear mocks and modules between tests

## Coverage Goals

- **Unit Tests**: Aim for 90%+ coverage of service methods
- **Integration Tests**: Cover all API endpoints and error scenarios
- **Business Logic**: 100% coverage of critical business rules

## Troubleshooting

### Common Issues

1. **Memory Leaks**: Ensure all mocks are reset and no database connections remain open
2. **Timeout Issues**: Increase Jest timeout for complex operations
3. **Mock Conflicts**: Use `jest.clearAllMocks()` in `afterEach` blocks

### Debug Mode

Run tests in debug mode:
```bash
npm run test:debug
```

Then attach a debugger to `localhost:9229`.

## Future Enhancements

- [ ] E2E tests with Cypress integration
- [ ] Performance testing with load scenarios
- [ ] Contract testing for API specifications
- [ ] Visual regression testing for admin interfaces
