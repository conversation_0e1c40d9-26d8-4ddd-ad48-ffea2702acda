# Troubleshooting Guide - AI Navigator Backend

## Quick Fix for Current Issues

### 🚨 Immediate Steps to Fix TypeScript Errors

The current TypeScript compilation errors have been identified and fixed. Follow these steps:

#### 1. Run the Database Fix Script

**For Unix/Linux/macOS:**
```bash
chmod +x scripts/fix-database.sh
./scripts/fix-database.sh
```

**For Windows:**
```cmd
scripts\fix-database.bat
```

**Or manually run these commands:**
```bash
# Create migration for the schema fix
npx prisma migrate dev --name make_news_articleUrl_optional

# Regenerate Prisma client
npx prisma generate

# Push schema changes
npx prisma db push --accept-data-loss
```

#### 2. Start the Development Server

```bash
npm run start:dev
```

#### 3. Test in Postman

The server should now start without TypeScript errors. You can test the API endpoints in Postman.

## Fixed Issues

### ✅ 1. PrismaClientExceptionFilter - Duplicate Variable Declaration
**Error:** `Cannot redeclare block-scoped variable 'request'`
**Fix:** Removed duplicate variable declaration in the exception filter.

### ✅ 2. Main.ts - Missing Logger Dependency
**Error:** `Expected 2 arguments, but got 1`
**Fix:** Added AppLoggerService dependency injection in main.ts.

### ✅ 3. EntitiesService - Type Mismatch
**Error:** `Type 'CreateNewsDetailsDto' is not assignable to type 'EntityDetailsNewsCreateWithoutEntityInput'`
**Fix:** Updated schema.prisma to make `articleUrl` optional in `EntityDetailsNews` model.

## Common Development Issues

### Database Connection Issues

**Problem:** Cannot connect to database
**Solutions:**
1. Check your `.env` file has correct `DATABASE_URL`
2. Ensure PostgreSQL is running
3. Verify database exists: `npx prisma db push`

### Prisma Client Issues

**Problem:** Prisma client out of sync
**Solutions:**
```bash
# Regenerate client
npx prisma generate

# Reset database (⚠️ loses data)
npx prisma migrate reset

# Apply pending migrations
npx prisma migrate deploy
```

### TypeScript Compilation Errors

**Problem:** Type mismatches between DTOs and Prisma models
**Solutions:**
1. Check schema.prisma matches your DTO definitions
2. Run `npx prisma generate` after schema changes
3. Ensure optional fields are marked correctly (`String?` vs `String`)

### Test Failures

**Problem:** Tests failing due to mock issues
**Solutions:**
1. Clear Jest cache: `npx jest --clearCache`
2. Reset mocks in test setup
3. Check mock implementations match service signatures

## API Testing with Postman

### Base URL
```
http://localhost:3001
```

### Authentication
Most endpoints require JWT authentication. Get a token from:
```
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

Use the returned token in subsequent requests:
```
Authorization: Bearer <your-jwt-token>
```

### Key Endpoints to Test

#### 1. Features
```
GET /features                    # List all features
POST /features                   # Create feature (auth required)
GET /features/{id}              # Get single feature
PATCH /features/{id}            # Update feature (auth required)
DELETE /features/{id}           # Delete feature (admin required)
```

#### 2. Entities
```
GET /entities                   # List entities with filtering
POST /entities                  # Create entity (auth required)
GET /entities/{id}             # Get single entity
PATCH /entities/{id}           # Update entity (auth required)
DELETE /entities/{id}          # Delete entity (admin required)
```

#### 3. Users
```
GET /users/me                  # Get current user profile
PUT /users/me                  # Update profile
GET /users/me/preferences      # Get notification settings
PUT /users/me/preferences      # Update notification settings
DELETE /users/me               # Soft delete account
```

#### 4. Reviews
```
POST /entities/{id}/reviews    # Create review
GET /entities/{id}/reviews     # Get approved reviews
PUT /reviews/{id}              # Update own review
DELETE /reviews/{id}           # Delete own review
```

#### 5. Admin Endpoints
```
GET /admin/users               # List all users (admin)
PUT /admin/users/{id}/status   # Update user status (admin)
PUT /admin/users/{id}/role     # Update user role (admin)
PATCH /admin/reviews/{id}/status # Moderate review (admin)
DELETE /admin/reviews/{id}     # Delete review (admin)
```

### Sample Request Bodies

#### Create Feature
```json
{
  "name": "Real-time Collaboration",
  "description": "Enables multiple users to work together in real-time",
  "category": "Collaboration"
}
```

#### Create Entity
```json
{
  "name": "ChatGPT",
  "website_url": "https://chat.openai.com",
  "entity_type_id": "uuid-of-ai-tool-type",
  "short_description": "AI-powered conversational assistant",
  "description": "ChatGPT is a large language model...",
  "logo_url": "https://example.com/logo.png",
  "category_ids": ["uuid1", "uuid2"],
  "tag_ids": ["uuid3", "uuid4"],
  "feature_ids": ["uuid5"]
}
```

#### Create Review
```json
{
  "rating": 5,
  "title": "Excellent AI Tool",
  "reviewText": "This tool has significantly improved my productivity..."
}
```

## Performance Monitoring

### Key Metrics to Watch
- Response times for `/entities` endpoint with filters
- Database query performance
- Memory usage during test runs
- JWT token validation overhead

### Optimization Opportunities
- Add caching for frequently accessed entities
- Implement database query optimization
- Consider pagination limits for large datasets
- Monitor vector search performance when implemented

## Logging and Debugging

### Structured Logging
All requests include correlation IDs for tracking:
```
X-Correlation-ID: uuid-v4
```

### Log Levels
- `error`: Critical issues requiring immediate attention
- `warn`: Potential issues or security concerns
- `info`: General application flow
- `debug`: Detailed debugging information

### Common Log Patterns
```bash
# Filter logs by correlation ID
grep "correlation-id-here" logs/app.log

# Monitor authentication failures
grep "authentication_failed" logs/app.log

# Track database errors
grep "prisma_error" logs/app.log
```

## Security Considerations

### Rate Limiting
- Login: 10 attempts per minute
- Signup: 5 attempts per minute
- Password reset: 3 attempts per 5 minutes

### Input Validation
- All DTOs use class-validator
- Non-whitelisted properties are stripped
- SQL injection protection via Prisma

### Authentication
- JWT tokens with expiration
- Role-based access control (USER, MODERATOR, ADMIN)
- Protected admin endpoints

## Next Steps

1. **Test All Endpoints**: Use Postman to verify functionality
2. **Performance Testing**: Monitor query performance on `/entities`
3. **Vector Search**: Implement semantic search functionality
4. **E2E Tests**: Add Cypress tests for critical user flows
5. **Deployment**: Prepare for production deployment

## Getting Help

If you encounter issues not covered here:
1. Check the application logs for correlation IDs
2. Verify database schema matches expectations
3. Ensure all environment variables are set
4. Run the test suite to identify specific failures
5. Check Prisma client generation is up to date
