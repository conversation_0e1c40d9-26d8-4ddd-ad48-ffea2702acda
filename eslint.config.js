// @ts-check

import eslint from '@eslint/js';
import tseslint from 'typescript-eslint';

export default tseslint.config(
  eslint.configs.recommended,
  ...tseslint.configs.recommended,
  {
    languageOptions: {
      parser: tseslint.parser,
      parserOptions: {
        project: './tsconfig.json', // Point to your tsconfig.json
      },
    },
    rules: {
      // Add any specific project rules here
      // e.g., '@typescript-eslint/no-unused-vars': 'warn',
    },
    ignores: [
        "dist/",
        "node_modules/",
        "eslint.config.js" // Ignore this config file itself
        // Add other directories/files to ignore if needed
    ]
  }
); 