// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider        = "prisma-client-js"
  output          = "../generated/prisma"
  previewFeatures = ["multiSchema", "fullTextSearchPostgres"]
  binaryTargets   = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["public"]
}

// Enums based on database inspection
enum UserRole {
  USER
  ADMIN
  MODERATOR
  // Add other roles discovered if any

  @@schema("public")
}

enum UserStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
  DELETED // Added for soft deletion

  @@schema("public")
}

enum TechnicalLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
  // Add other levels discovered if any

  @@schema("public")
}

enum EntityStatus {
  PENDING
  ACTIVE
  REJECTED
  NEEDS_REVISION
  INACTIVE
  ARCHIVED // Added for soft deletion

  @@schema("public")
}

enum AffiliateStatus {
  NONE
  APPLIED
  APPROVED
  REJECTED
  // Add other statuses discovered if any

  @@schema("public")
}

enum ReviewStatus {
  PENDING
  APPROVED
  REJECTED
  // Add other statuses discovered if any

  @@schema("public")
}

enum LearningCurve {
  LOW
  MEDIUM
  HIGH
  // Add other curves discovered if any

  @@schema("public")
}

enum PricingModel {
  FREE
  FREEMIUM
  SUBSCRIPTION // Replaces PAID, or is distinct if PAID was meant to be kept and mapped
  PAY_PER_USE // Replaces USAGE_BASED
  ONE_TIME_PURCHASE // Replaces ONE_TIME
  CONTACT_SALES // New
  OPEN_SOURCE // New
  // OTHER // Intentionally removed as per plan

  @@schema("public")
}

enum PriceRange {
  FREE
  LOW
  MEDIUM
  HIGH
  ENTERPRISE
  // Add other ranges discovered if any

  @@schema("public")
}

enum SkillLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
  // Add other levels discovered if any

  @@schema("public")
}

enum ActionType {
  VIEW_ENTITY
  CLICK_ENTITY_LINK
  SAVE_ENTITY
  UNSAVE_ENTITY
  SUBMIT_REVIEW
  VOTE_REVIEW
  FOLLOW_TAG
  UNFOLLOW_TAG
  FOLLOW_CATEGORY
  UNFOLLOW_CATEGORY
  SEARCH
  LOGIN
  LOGOUT
  SIGNUP
  UPDATE_PROFILE
  GRANT_BADGE
  REVOKE_BADGE
  // Add other actions discovered if any

  @@schema("public")
}

enum BadgeScope {
  USER
  ENTITY
  // Add other scopes discovered if any

  @@schema("public")
}

enum EmployeeCountRange {
  C1_10
  C11_50
  C51_200
  C201_500
  C501_1000
  C1001_5000
  C5001_PLUS

  @@schema("public")
}

enum FundingStage {
  SEED
  PRE_SEED
  SERIES_A
  SERIES_B
  SERIES_C
  SERIES_D_PLUS
  PUBLIC

  @@schema("public")
}

// Models based on database inspection

model User {
  id                       String                    @id @default(uuid()) @db.Uuid
  authUserId               String                    @unique @map("auth_user_id") @db.Uuid
  username                 String?                   @unique
  displayName              String?                   @map("display_name")
  email                    String                    @unique
  role                     UserRole                  @default(USER)
  status                   UserStatus                @default(ACTIVE)
  technicalLevel           TechnicalLevel?           @map("technical_level")
  profilePictureUrl        String?                   @map("profile_picture_url")
  bio                      String?
  socialLinks              Json?                     @map("social_links") @db.JsonB
  createdAt                DateTime                  @default(now()) @map("created_at")
  updatedAt                DateTime                  @updatedAt @map("updated_at")
  lastLogin                DateTime?                 @map("last_login")
  reviews                  Review[]
  reviewsModerated         Review[]                  @relation("ModeratorReviews")
  userSavedEntities        UserSavedEntity[]         @relation("UserSavedEntities")
  userFollowedTags         UserFollowedTag[]         @relation("UserFollowedTags")
  userFollowedCategories   UserFollowedCategory[]    @relation("UserFollowedCategories")
  submittedEntities        Entity[]                  @relation("Submitter")
  userActivityLogs         UserActivityLog[]         @relation("UserLogs")
  userActivityLogTargets   UserActivityLog[]         @relation("TargetUserLogs")
  userNotificationSettings UserNotificationSettings?
  userBadges               UserBadge[]               @relation("UserBadges")
  badgesGranted            UserBadge[]               @relation("UserBadgesGranted")
  entityBadgesGranted      EntityBadge[]             @relation("EntityBadgesGranted")
  reviewVotes              ReviewVote[]              @relation("UserReviewVotes")

  @@map("users")
  @@schema("public")
}

model EntityType {
  id          String   @id @default(uuid()) @db.Uuid
  name        String   @unique
  description String?
  slug        String   @unique
  iconUrl     String?  @map("icon_url")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  entities    Entity[]

  @@map("entity_types")
  @@schema("public")
}

model Entity {
  id                            String                         @id @default(uuid()) @db.Uuid
  name                          String                         @unique
  slug                          String                         @unique
  websiteUrl                    String?                        @map("website_url")
  entityTypeId                  String                         @map("entity_type_id") @db.Uuid
  shortDescription              String?                        @map("short_description")
  description                   String?
  logoUrl                       String?                        @map("logo_url")
  documentationUrl              String?                        @map("documentation_url")
  contactUrl                    String?                        @map("contact_url")
  privacyPolicyUrl              String?                        @map("privacy_policy_url")
  foundedYear                   Int?                           @map("founded_year")
  status                        EntityStatus                   @default(PENDING)
  socialLinks                   Json?                          @map("social_links") @db.JsonB
  submitterId                   String                         @map("submitter_id") @db.Uuid
  legacyId                      String?                        @map("legacy_id")
  reviewCount                   Int                            @default(0) @map("review_count")
  avgRating                     Float                          @default(0) @map("avg_rating")
  createdAt                     DateTime                       @default(now()) @map("created_at")
  updatedAt                     DateTime                       @updatedAt @map("updated_at")
  ftsDocument                   Unsupported("tsvector")?       @map("ftsDocument")
  vectorEmbedding               Unsupported("vector")?         @map("vector_embedding")
  metaTitle                     String?                        @map("meta_title")
  metaDescription               String?                        @map("meta_description")
  scrapedReviewSentimentLabel   String?                        @map("scraped_review_sentiment_label")
  scrapedReviewSentimentScore   Float?                         @map("scraped_review_sentiment_score")
  scrapedReviewCount            Int?                           @map("scraped_review_count")
  employeeCountRange            EmployeeCountRange?            @map("employee_count_range")
  fundingStage                  FundingStage?                  @map("funding_stage")
  locationSummary               String?                        @map("location_summary")
  refLink                       String?                        @map("ref_link")
  affiliateStatus               AffiliateStatus?               @default(NONE)
  entityType                    EntityType                     @relation(fields: [entityTypeId], references: [id])
  submitter                     User                           @relation("Submitter", fields: [submitterId], references: [id])
  entityCategories              EntityCategory[]
  entityTags                    EntityTag[]
  entityFeatures                EntityFeature[]
  reviews                       Review[]
  userSavedEntities             UserSavedEntity[]
  entityBadges                  EntityBadge[]
  entityDetailsTool             EntityDetailsTool?
  entityDetailsAgency           EntityDetailsAgency?
  entityDetailsContentCreator   EntityDetailsContentCreator?
  entityDetailsCommunity        EntityDetailsCommunity?
  entityDetailsNewsletter       EntityDetailsNewsletter?
  entityDetailsCourse           EntityDetailsCourse?
  entityDetailsDataset          EntityDetailsDataset?
  entityDetailsResearchPaper    EntityDetailsResearchPaper?
  entityDetailsSoftware         EntityDetailsSoftware?
  entityDetailsModel            EntityDetailsModel?
  entityDetailsProjectReference EntityDetailsProjectReference?
  entityDetailsServiceProvider  EntityDetailsServiceProvider?
  entityDetailsInvestor         EntityDetailsInvestor?
  entityDetailsEvent            EntityDetailsEvent?
  entityDetailsJob              EntityDetailsJob?
  entityDetailsGrant            EntityDetailsGrant?
  entityDetailsBounty           EntityDetailsBounty?
  entityDetailsHardware         EntityDetailsHardware?
  entityDetailsNews             EntityDetailsNews?
  entityDetailsBook             EntityDetailsBook?
  entityDetailsPodcast          EntityDetailsPodcast?
  entityDetailsPlatform         EntityDetailsPlatform?
  userActivityLogs              UserActivityLog[]              @relation("EntityLogs")

  @@map("entities")
  @@schema("public")
}

model Category {
  id                     String                 @id @default(uuid()) @db.Uuid
  name                   String                 @unique
  slug                   String                 @unique
  description            String?
  iconUrl                String?                @map("icon_url")
  parentId               String?                @map("parent_id") @db.Uuid
  createdAt              DateTime               @default(now()) @map("created_at")
  updatedAt              DateTime               @updatedAt @map("updated_at")
  parent                 Category?              @relation("CategoryToParent", fields: [parentId], references: [id])
  children               Category[]             @relation("CategoryToParent")
  entityCategories       EntityCategory[]
  userFollowedCategories UserFollowedCategory[]
  userActivityLogs       UserActivityLog[]

  @@map("categories")
  @@schema("public")
}

model Tag {
  id               String            @id @default(uuid()) @db.Uuid
  name             String            @unique
  description      String?
  slug             String            @unique
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")
  entityTags       EntityTag[]
  userFollowed     UserFollowedTag[]
  userActivityLogs UserActivityLog[] @relation("TagLogs")

  @@map("tags")
  @@schema("public")
}

model EntityTag {
  id         String   @id @default(uuid()) @db.Uuid
  entityId   String   @map("entity_id") @db.Uuid
  tagId      String   @map("tag_id") @db.Uuid
  assignedBy String   @map("assigned_by") @db.Uuid
  createdAt  DateTime @default(now()) @map("created_at")
  entity     Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  tag        Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([entityId, tagId], map: "uq_entity_tag")
  @@map("entity_tags")
  @@schema("public")
}

model EntityCategory {
  entityId   String   @map("entity_id") @db.Uuid
  categoryId String   @map("category_id") @db.Uuid
  assignedAt DateTime @default(now()) @map("assigned_at")
  assignedBy String   @map("assigned_by") @db.Uuid
  entity     Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@id([entityId, categoryId])
  @@map("entity_categories")
  @@schema("public")
}

model Review {
  id               String            @id @default(uuid()) @db.Uuid
  entityId         String            @map("entity_id") @db.Uuid
  userId           String            @map("user_id") @db.Uuid
  rating           Int
  title            String?
  content          String?
  status           ReviewStatus      @default(PENDING)
  moderatorId      String?           @map("moderator_id") @db.Uuid
  moderationNotes  String?           @map("moderation_notes")
  upvotes          Int               @default(0)
  downvotes        Int               @default(0)
  createdAt        DateTime          @default(now()) @map("created_at")
  updatedAt        DateTime          @updatedAt @map("updated_at")
  entity           Entity            @relation(fields: [entityId], references: [id], onDelete: Cascade)
  user             User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  moderator        User?             @relation("ModeratorReviews", fields: [moderatorId], references: [id], onDelete: SetNull)
  reviewVotes      ReviewVote[]
  userActivityLogs UserActivityLog[] @relation("ReviewLogs")

  @@unique([entityId, userId], map: "uq_review_entity_user")
  @@map("reviews")
  @@schema("public")
}

model ReviewVote {
  id        String   @id @default(uuid()) @db.Uuid
  reviewId  String   @map("review_id") @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  isUpvote  Boolean  @map("is_upvote")
  createdAt DateTime @default(now()) @map("created_at")
  review    Review   @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  user      User     @relation("UserReviewVotes", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([reviewId, userId], map: "uq_review_vote_user")
  @@map("review_votes")
  @@schema("public")
}

model EntityDetailsTool {
  id                   String          @id @default(uuid()) @db.Uuid
  entityId             String          @unique @map("entity_id") @db.Uuid
  technicalLevel       TechnicalLevel? @map("technical_level")
  hasApi               Boolean?        @map("has_api")
  hasFreeTier          Boolean?        @map("has_free_tier")
  keyFeatures          Json?           @map("key_features") @db.JsonB
  useCases             Json?           @map("use_cases") @db.JsonB
  learningCurve        LearningCurve?  @map("learning_curve")
  pricingModel         PricingModel?   @map("pricing_model")
  priceRange           PriceRange?     @map("price_range")
  integrations         Json?           @db.JsonB
  platforms            Json?           @db.JsonB
  programmingLanguages Json?           @map("programming_languages") @db.JsonB
  frameworks           Json?           @db.JsonB
  libraries            Json?           @db.JsonB
  targetAudience       Json?           @map("target_audience") @db.JsonB
  deploymentOptions    Json?           @map("deployment_options") @db.JsonB
  supportedOs          Json?           @map("supported_os") @db.JsonB
  mobileSupport        Boolean?        @map("mobile_support")
  apiAccess            Boolean?        @map("api_access")
  customizationLevel   String?         @map("customization_level")
  trialAvailable       Boolean?        @map("trial_available")
  demoAvailable        Boolean?        @map("demo_available")
  openSource           Boolean?        @map("open_source")
  supportChannels      Json?           @map("support_channels") @db.JsonB
  pricingDetails       String?         @map("pricing_details")
  pricingUrl           String?         @map("pricing_url")
  supportEmail         String?         @map("support_email")
  hasLiveChat          Boolean?        @map("has_live_chat")
  communityUrl         String?         @map("community_url")
  entity               Entity          @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_tool")
  @@schema("public")
}

model EntityDetailsAgency {
  id               String  @id @default(uuid()) @db.Uuid
  entityId         String  @unique @map("entity_id") @db.Uuid
  servicesOffered  Json?   @map("services_offered") @db.JsonB
  industryFocus    Json?   @map("industry_focus") @db.JsonB
  targetClientSize Json?   @map("target_client_size") @db.JsonB
  portfolioUrl     String? @map("portfolio_url")
  pricingInfo      String? @map("pricing_info")
  entity           Entity  @relation(fields: [entityId], references: [id], onDelete: Cascade)
  targetAudience   Json?   @map("target_audience") @db.JsonB
  locationSummary  String? @map("location_summary")

  @@map("entity_details_agency")
  @@schema("public")
}

model EntityDetailsContentCreator {
  id                String  @id @default(uuid()) @db.Uuid
  entityId          String  @unique @map("entity_id") @db.Uuid
  creatorName       String? @map("creator_name")
  primaryPlatform   String? @map("primary_platform")
  focusAreas        Json?   @map("focus_areas") @db.JsonB
  followerCount     Int?    @map("follower_count")
  exampleContentUrl String? @map("example_content_url")
  entity            Entity  @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_content_creator")
  @@schema("public")
}

model EntityDetailsCommunity {
  id             String  @id @default(uuid()) @db.Uuid
  entityId       String  @unique @map("entity_id") @db.Uuid
  platform       String?
  memberCount    Int?    @map("member_count")
  focusTopics    Json?   @map("focus_topics") @db.JsonB
  rulesUrl       String? @map("rules_url")
  inviteUrl      String? @map("invite_url")
  mainChannelUrl String? @map("main_channel_url")
  entity         Entity  @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_community")
  @@schema("public")
}

model EntityDetailsNewsletter {
  id              String  @id @default(uuid()) @db.Uuid
  entityId        String  @unique @map("entity_id") @db.Uuid
  frequency       String?
  mainTopics      Json?   @map("main_topics") @db.JsonB
  archiveUrl      String? @map("archive_url")
  subscribeUrl    String? @map("subscribe_url")
  authorName      String? @map("author_name")
  subscriberCount Int?    @default(0) @map("subscriber_count")
  entity          Entity  @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_newsletter")
  @@schema("public")
}

model EntityDetailsCourse {
  id                   String      @id @default(uuid()) @db.Uuid
  entityId             String      @unique @map("entity_id") @db.Uuid
  instructorName       String?     @map("instructor_name")
  durationText         String?     @map("duration_text")
  skillLevel           SkillLevel? @map("skill_level")
  prerequisites        String?
  syllabusUrl          String?     @map("syllabus_url")
  enrollmentCount      Int?        @map("enrollment_count")
  certificateAvailable Boolean?    @default(false) @map("certificate_available")
  entity               Entity      @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@map("entity_details_course")
  @@schema("public")
}

model UserSavedEntity {
  id        String   @id @default(uuid()) @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  entityId  String   @map("entity_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")
  user      User     @relation("UserSavedEntities", fields: [userId], references: [id], onDelete: Cascade)
  entity    Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)

  @@unique([userId, entityId], map: "uq_user_saved_entity")
  @@map("user_saved_entities")
  @@schema("public")
}

model UserFollowedTag {
  id        String   @id @default(uuid()) @db.Uuid
  userId    String   @map("user_id") @db.Uuid
  tagId     String   @map("tag_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")
  user      User     @relation("UserFollowedTags", fields: [userId], references: [id], onDelete: Cascade)
  tag       Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([userId, tagId], map: "uq_user_followed_tag")
  @@map("user_followed_tags")
  @@schema("public")
}

model UserFollowedCategory {
  userId     String   @map("user_id") @db.Uuid
  categoryId String   @map("category_id") @db.Uuid
  followedAt DateTime @default(now()) @map("followed_at")
  user       User     @relation("UserFollowedCategories", fields: [userId], references: [id], onDelete: Cascade)
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@id([userId, categoryId])
  @@map("user_followed_categories")
  @@schema("public")
}

model UserActivityLog {
  id           String     @id @default(uuid()) @db.Uuid
  userId       String     @map("user_id") @db.Uuid
  actionType   ActionType @map("action_type")
  entityId     String?    @map("entity_id") @db.Uuid
  categoryId   String?    @map("category_id") @db.Uuid
  tagId        String?    @map("tag_id") @db.Uuid
  reviewId     String?    @map("review_id") @db.Uuid
  targetUserId String?    @map("target_user_id") @db.Uuid
  details      Json?      @db.JsonB
  createdAt    DateTime   @default(now()) @map("created_at")
  user         User       @relation("UserLogs", fields: [userId], references: [id], onDelete: Cascade)
  entity       Entity?    @relation("EntityLogs", fields: [entityId], references: [id], onDelete: SetNull)
  category     Category?  @relation(fields: [categoryId], references: [id])
  tag          Tag?       @relation("TagLogs", fields: [tagId], references: [id], onDelete: SetNull)
  targetUser   User?      @relation("TargetUserLogs", fields: [targetUserId], references: [id], onDelete: SetNull)
  review       Review?    @relation("ReviewLogs", fields: [reviewId], references: [id], onDelete: SetNull)

  @@map("user_activity_logs")
  @@schema("public")
}

model UserNotificationSettings {
  id                         String   @id @default(uuid()) @db.Uuid
  userId                     String   @unique @map("user_id") @db.Uuid
  emailOnNewReview           Boolean  @default(true) @map("email_on_new_review")
  emailOnReviewResponse      Boolean  @default(true) @map("email_on_review_response")
  emailOnNewFollower         Boolean  @default(true) @map("email_on_new_follower")
  emailOnNewEntityInFollowed Boolean  @default(true) @map("email_on_new_entity_in_followed")
  emailNewsletter            Boolean  @default(true) @map("email_newsletter")
  emailMarketing             Boolean  @default(true) @map("email_marketing")
  createdAt                  DateTime @default(now()) @map("created_at")
  updatedAt                  DateTime @updatedAt @map("updated_at")
  user                       User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_notification_settings")
  @@schema("public")
}

model BadgeType {
  id           String        @id @default(uuid()) @db.Uuid
  name         String        @unique
  description  String?
  iconUrl      String?       @map("icon_url")
  scope        BadgeScope
  criteria     Json?         @db.JsonB
  createdAt    DateTime      @default(now()) @map("created_at")
  updatedAt    DateTime      @updatedAt @map("updated_at")
  userBadges   UserBadge[]   @relation("BadgeTypeUser")
  entityBadges EntityBadge[] @relation("BadgeTypeEntity")

  @@map("badge_types")
  @@schema("public")
}

model UserBadge {
  id            String    @id @default(uuid()) @db.Uuid
  userId        String    @map("user_id") @db.Uuid
  badgeTypeId   String    @map("badge_type_id") @db.Uuid
  grantedAt     DateTime  @default(now()) @map("granted_at")
  grantedBy     String?   @map("granted_by") @db.Uuid
  notes         String?
  user          User      @relation("UserBadges", fields: [userId], references: [id], onDelete: Cascade)
  badgeType     BadgeType @relation("BadgeTypeUser", fields: [badgeTypeId], references: [id], onDelete: Cascade)
  grantedByUser User?     @relation("UserBadgesGranted", fields: [grantedBy], references: [id], onDelete: SetNull)

  @@unique([userId, badgeTypeId], map: "uq_user_badge")
  @@map("user_badges")
  @@schema("public")
}

model EntityBadge {
  id            String    @id @default(uuid()) @db.Uuid
  entityId      String    @map("entity_id") @db.Uuid
  badgeTypeId   String    @map("badge_type_id") @db.Uuid
  grantedAt     DateTime  @default(now()) @map("granted_at")
  grantedBy     String?   @map("granted_by") @db.Uuid
  expiresAt     DateTime? @map("expires_at")
  notes         String?
  entity        Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)
  badgeType     BadgeType @relation("BadgeTypeEntity", fields: [badgeTypeId], references: [id], onDelete: Cascade)
  grantedByUser User?     @relation("EntityBadgesGranted", fields: [grantedBy], references: [id], onDelete: SetNull)

  @@unique([entityId, badgeTypeId], map: "uq_entity_badge")
  @@map("entity_badges")
  @@schema("public")
}

model EntityDetailsDataset {
  id               String   @id @default(uuid()) @db.Uuid
  entityId         String   @unique @map("entity_id") @db.Uuid
  format           String?
  sourceUrl        String?  @map("source_url")
  license          String?
  sizeInBytes      BigInt?  @map("size_in_bytes")
  description      String?
  accessNotes      String?  @map("access_notes")
  collectionMethod String?  @map("collection_method")
  updateFrequency  String?  @map("update_frequency")
  entity           Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  @@map("entity_details_dataset")
  @@schema("public")
}

model EntityDetailsResearchPaper {
  id                  String    @id @default(uuid()) @db.Uuid
  entityId            String    @unique @map("entity_id") @db.Uuid
  publicationDate     DateTime? @map("publication_date")
  doi                 String?
  authors             String[]
  journalOrConference String?   @map("journal_or_conference")
  citationCount       Int?      @map("citation_count")
  abstract            String?
  pdfUrl              String?   @map("pdf_url")
  entity              Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt           DateTime  @default(now()) @map("created_at")
  updatedAt           DateTime  @updatedAt @map("updated_at")

  @@map("entity_details_research_paper")
  @@schema("public")
}

model EntityDetailsSoftware {
  id                    String        @id @default(uuid()) @db.Uuid
  entityId              String        @unique @map("entity_id") @db.Uuid
  currentVersion        String?       @map("current_version")
  licenseType           String?       @map("license_type")
  hasApi                Boolean?      @map("has_api")
  hasFreeTier           Boolean?      @map("has_free_tier")
  keyFeatures           String[]      @map("key_features")
  useCases              String[]      @map("use_cases")
  pricingModel          PricingModel? @map("pricing_model")
  priceRange            PriceRange?   @map("price_range")
  integrations          String[]
  platformCompatibility String[]      @map("platform_compatibility")
  programmingLanguages  String[]      @map("programming_languages")
  frameworks            String[]
  libraries             String[]
  targetAudience        String[]      @map("target_audience")
  deploymentOptions     String[]      @map("deployment_options")
  supportedOs           String[]      @map("supported_os")
  mobileSupport         Boolean?      @map("mobile_support")
  apiAccess             Boolean?      @map("api_access")
  customizationLevel    String?       @map("customization_level")
  trialAvailable        Boolean?      @map("trial_available")
  demoAvailable         Boolean?      @map("demo_available")
  openSource            Boolean?      @map("open_source")
  supportChannels       String[]      @map("support_channels")
  pricingDetails        String?       @map("pricing_details")
  pricingUrl            String?       @map("pricing_url")
  supportEmail          String?       @map("support_email")
  hasLiveChat           Boolean?      @map("has_live_chat")
  communityUrl          String?       @map("community_url")
  entity                Entity        @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt             DateTime      @default(now()) @map("created_at")
  updatedAt             DateTime      @updatedAt @map("updated_at")

  @@map("entity_details_software")
  @@schema("public")
}

model EntityDetailsModel {
  id                 String   @id @default(uuid()) @db.Uuid
  entityId           String   @unique @map("entity_id") @db.Uuid
  license            String?
  modelArchitecture  String?  @map("model_architecture")
  trainingDataset    String?  @map("training_dataset")
  performanceMetrics Json?    @map("performance_metrics") @db.JsonB
  useCases           String[] @map("use_cases")
  frameworks         String[]
  libraries          String[]
  targetAudience     String[] @map("target_audience")
  deploymentOptions  String[] @map("deployment_options")
  inputDataTypes     String[] @map("input_data_types")
  outputDataTypes    String[] @map("output_data_types")
  entity             Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  @@map("entity_details_model")
  @@schema("public")
}

model EntityDetailsProjectReference {
  id              String   @id @default(uuid()) @db.Uuid
  entityId        String   @unique @map("entity_id") @db.Uuid
  repositoryUrl   String?  @map("repository_url")
  license         String?
  keyTechnologies String[] @map("key_technologies")
  useCases        String[] @map("use_cases")
  status          String?
  contributors    Int?
  stars           Int?
  forks           Int?
  entity          Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  @@map("entity_details_project_reference")
  @@schema("public")
}

model EntityDetailsServiceProvider {
  id                      String   @id @default(uuid()) @db.Uuid
  entityId                String   @unique @map("entity_id") @db.Uuid
  servicesOffered         String[] @map("services_offered")
  industrySpecializations String[] @map("industry_specializations")
  companySizeFocus        String?  @map("company_size_focus")
  portfolioUrl            String?  @map("portfolio_url")
  pricingInfo             String?  @map("pricing_info")
  targetAudience          String[] @map("target_audience")
  locationSummary         String?  @map("location_summary")
  entity                  Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt               DateTime @default(now()) @map("created_at")
  updatedAt               DateTime @updatedAt @map("updated_at")

  @@map("entity_details_service_provider")
  @@schema("public")
}

model EntityDetailsInvestor {
  id                 String   @id @default(uuid()) @db.Uuid
  entityId           String   @unique @map("entity_id") @db.Uuid
  investmentStages   String[] @map("investment_stages")
  focusAreas         String[] @map("focus_areas")
  portfolioSize      Int?     @map("portfolio_size")
  notableInvestments String[] @map("notable_investments")
  contactEmail       String?  @map("contact_email")
  applicationUrl     String?  @map("application_url")
  locationSummary    String?  @map("location_summary")
  investorType       String?  @map("investor_type")
  entity             Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt          DateTime @default(now()) @map("created_at")
  updatedAt          DateTime @updatedAt @map("updated_at")

  @@map("entity_details_investor")
  @@schema("public")
}

model EntityDetailsEvent {
  id              String    @id @default(uuid()) @db.Uuid
  entityId        String    @unique @map("entity_id") @db.Uuid
  startDate       DateTime? @map("start_date")
  endDate         DateTime? @map("end_date")
  location        String?
  isOnline        Boolean?  @map("is_online")
  registrationUrl String?   @map("registration_url")
  price           String?
  keySpeakers     String[]  @map("key_speakers")
  topics          String[]
  eventType       String?   @map("event_type")
  targetAudience  String[]  @map("target_audience")
  entity          Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  @@map("entity_details_event")
  @@schema("public")
}

model EntityDetailsJob {
  id                  String   @id @default(uuid()) @db.Uuid
  entityId            String   @unique @map("entity_id") @db.Uuid
  companyName         String?  @map("company_name")
  location            String?
  isRemote            Boolean? @map("is_remote")
  salaryMin           Float?   @map("salary_min")
  salaryMax           Float?   @map("salary_max")
  jobType             String?  @map("job_type")
  experienceLevel     String?  @map("experience_level")
  keyResponsibilities String[] @map("key_responsibilities")
  requiredSkills      String[] @map("required_skills")
  applicationUrl      String?  @map("application_url")
  entity              Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  @@map("entity_details_job")
  @@schema("public")
}

model EntityDetailsGrant {
  id             String    @id @default(uuid()) @db.Uuid
  entityId       String    @unique @map("entity_id") @db.Uuid
  funderName     String?   @map("funder_name")
  amount         String?
  deadline       DateTime?
  eligibility    String?
  applicationUrl String?   @map("application_url")
  focusAreas     String[]  @map("focus_areas")
  location       String?
  grantType      String?   @map("grant_type")
  entity         Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")

  @@map("entity_details_grant")
  @@schema("public")
}

model EntityDetailsBounty {
  id              String    @id @default(uuid()) @db.Uuid
  entityId        String    @unique @map("entity_id") @db.Uuid
  amount          String?
  status          String?
  deadline        DateTime?
  platform        String?
  taskDescription String?   @map("task_description")
  requiredSkills  String[]  @map("required_skills")
  url             String?
  entity          Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  @@map("entity_details_bounty")
  @@schema("public")
}

model EntityDetailsHardware {
  id               String   @id @default(uuid()) @db.Uuid
  entityId         String   @unique @map("entity_id") @db.Uuid
  processor        String?
  memory           String?
  storage          String?
  gpu              String?
  price            String?
  availability     String?
  useCases         String[] @map("use_cases")
  powerConsumption String?  @map("power_consumption")
  entity           Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  @@map("entity_details_hardware")
  @@schema("public")
}

model EntityDetailsNews {
  id              String    @id @default(uuid()) @db.Uuid
  entityId        String    @unique @map("entity_id") @db.Uuid
  publicationDate DateTime? @map("publication_date")
  sourceName      String?   @map("source_name")
  author          String?
  articleUrl      String?   @map("article_url")
  summary         String?
  tags            String[]
  entity          Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  @@map("entity_details_news")
  @@schema("public")
}

model EntityDetailsBook {
  id              String    @id @default(uuid()) @db.Uuid
  entityId        String    @unique @map("entity_id") @db.Uuid
  author          String?
  publisher       String?
  publicationDate DateTime? @map("publication_date")
  isbn            String?
  pageCount       Int?      @map("page_count")
  format          String?
  summary         String?
  purchaseUrl     String?   @map("purchase_url")
  entity          Entity    @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  @@map("entity_details_book")
  @@schema("public")
}

model EntityDetailsPodcast {
  id                String   @id @default(uuid()) @db.Uuid
  entityId          String   @unique @map("entity_id") @db.Uuid
  host              String?
  mainTopics        String[] @map("main_topics")
  averageLength     String?  @map("average_length")
  frequency         String?
  spotifyUrl        String?  @map("spotify_url")
  applePodcastsUrl  String?  @map("apple_podcasts_url")
  googlePodcastsUrl String?  @map("google_podcasts_url")
  youtubeUrl        String?  @map("youtube_url")
  entity            Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  @@map("entity_details_podcast")
  @@schema("public")
}

model EntityDetailsPlatform {
  id                 String        @id @default(uuid()) @db.Uuid
  entityId           String        @unique @map("entity_id") @db.Uuid
  platformType       String?       @map("platform_type")
  keyServices        String[]      @map("key_services")
  documentationUrl   String?       @map("documentation_url")
  hasApi             Boolean?      @map("has_api")
  hasFreeTier        Boolean?      @map("has_free_tier")
  useCases           String[]      @map("use_cases")
  pricingModel       PricingModel? @map("pricing_model")
  priceRange         PriceRange?   @map("price_range")
  integrations       String[]
  targetAudience     String[]      @map("target_audience")
  deploymentOptions  String[]      @map("deployment_options")
  supportedOs        String[]      @map("supported_os")
  mobileSupport      Boolean?      @map("mobile_support")
  apiAccess          Boolean?      @map("api_access")
  customizationLevel String?       @map("customization_level")
  trialAvailable     Boolean?      @map("trial_available")
  demoAvailable      Boolean?      @map("demo_available")
  openSource         Boolean?      @map("open_source")
  supportChannels    String[]      @map("support_channels")
  pricingDetails     String?       @map("pricing_details")
  pricingUrl         String?       @map("pricing_url")
  supportEmail       String?       @map("support_email")
  hasLiveChat        Boolean?      @map("has_live_chat")
  communityUrl       String?       @map("community_url")
  entity             Entity        @relation(fields: [entityId], references: [id], onDelete: Cascade)
  createdAt          DateTime      @default(now()) @map("created_at")
  updatedAt          DateTime      @updatedAt @map("updated_at")

  @@map("entity_details_platform")
  @@schema("public")
}

model EntityFeature {
  id         String   @id @default(uuid()) @db.Uuid
  entityId   String   @map("entity_id") @db.Uuid
  featureId  String   @map("feature_id") @db.Uuid
  assignedBy String   @map("assigned_by") @db.Uuid
  createdAt  DateTime @default(now()) @map("created_at")
  entity     Entity   @relation(fields: [entityId], references: [id], onDelete: Cascade)
  feature    Feature  @relation(fields: [featureId], references: [id], onDelete: Cascade)

  @@unique([entityId, featureId], map: "uq_entity_feature")
  @@map("entity_features")
  @@schema("public")
}

model AppSetting {
  key         String   @id
  value       String
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("app_settings")
  @@schema("public")
}

model Feature {
  id             String          @id @default(uuid()) @db.Uuid
  name           String          @unique
  description    String?
  slug           String          @unique
  iconUrl        String?         @map("icon_url")
  createdAt      DateTime        @default(now()) @map("created_at")
  updatedAt      DateTime        @updatedAt @map("updated_at")
  entityFeatures EntityFeature[]

  @@map("features")
  @@schema("public")
}
