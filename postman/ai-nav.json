{"info": {"_postman_id": "54acfb02-b70a-4b0f-9217-4ff371286b27", "name": "AI nav", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12729153"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "sign up", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n      \"email\": \"<EMAIL>\",\n      \"password\": \"password123\"\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://ai-nav.onrender.com/auth/signup", "protocol": "https", "host": ["ai-nav", "onrender", "com"], "path": ["auth", "signup"]}}, "response": []}, {"name": "Forgot Password", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n      \"email\": \"<EMAIL>\"\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://ai-nav.onrender.com/auth/forgot-password", "protocol": "https", "host": ["ai-nav", "onrender", "com"], "path": ["auth", "forgot-password"]}}, "response": []}, {"name": "Resend Confirmation", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n      \"email\": \"<EMAIL>\"\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://ai-nav.onrender.com/auth/resend-confirmation", "protocol": "https", "host": ["ai-nav", "onrender", "com"], "path": ["auth", "resend-confirmation"]}}, "response": []}, {"name": "profile", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fVcrABAjG2mAxBz8pWjMBuTVrq9_QIgpIbgsCodonWw", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://ai-nav.onrender.com/auth/profile", "protocol": "https", "host": ["ai-nav", "onrender", "com"], "path": ["auth", "profile"]}}, "response": []}, {"name": "test connection", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fVcrABAjG2mAxBz8pWjMBuTVrq9_QIgpIbgsCodonWw", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://ai-nav.onrender.com/test-connection", "protocol": "https", "host": ["ai-nav", "onrender", "com"], "path": ["test-connection"]}}, "response": []}, {"name": "logout", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fVcrABAjG2mAxBz8pWjMBuTVrq9_QIgpIbgsCodonWw", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://ai-nav.onrender.com/auth/profile", "protocol": "https", "host": ["ai-nav", "onrender", "com"], "path": ["auth", "profile"]}}, "response": []}, {"name": "login", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n      \"email\": \"<EMAIL>\",\n      \"password\": \"password123\"\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://ai-nav.onrender.com/auth/login", "protocol": "https", "host": ["ai-nav", "onrender", "com"], "path": ["auth", "login"]}}, "response": []}]}, {"name": "Local", "item": [{"name": "users/me", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bHLEGGACKwQNB9bNjA_ZrUjYojinBiMgN0pHvdrzZB4", "type": "text"}], "body": {"mode": "raw", "raw": "{\n      \"email\": \"<EMAIL>\",\n      \"password\": \"password123\"\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/users/me", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["users", "me"]}}, "response": []}, {"name": "/admin/users", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iSiFZ3dEr2fn7YcIg3UCUUS8TQRCFgWBynSBbaqtiYc", "type": "text"}], "body": {"mode": "raw", "raw": "{\n      \"email\": \"<EMAIL>\",\n      \"password\": \"password123\"\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/admin/users", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["admin", "users"]}}, "response": []}, {"name": "/admin/users/userid", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iSiFZ3dEr2fn7YcIg3UCUUS8TQRCFgWBynSBbaqtiYc", "type": "text"}], "body": {"mode": "raw", "raw": "{\n      \"email\": \"<EMAIL>\",\n      \"password\": \"password123\"\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/admin/users", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["admin", "users"]}}, "response": []}, {"name": "/admin/users/userid Copy", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iSiFZ3dEr2fn7YcIg3UCUUS8TQRCFgWBynSBbaqtiYc", "type": "text"}], "body": {"mode": "raw", "raw": "{\n      \"email\": \"<EMAIL>\",\n      \"password\": \"password123\"\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/admin/users", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["admin", "users"]}}, "response": []}, {"name": "users/me Copy", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.8LF_npHaF5aSnVItiLUKbqr6MfDJoXrs0BLz9nfFxZQ", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/users/me", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["users", "me"]}}, "response": []}, {"name": "/users/me/preferences", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bHLEGGACKwQNB9bNjA_ZrUjYojinBiMgN0pHvdrzZB4", "type": "text"}], "body": {"mode": "raw", "raw": "{\n      \"email\": \"<EMAIL>\",\n      \"password\": \"password123\"\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/users/me/preferences", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["users", "me", "preferences"]}}, "response": []}, {"name": "/users/me/preferences Copy", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzI1NiIsImtpZCI6Ikt3WTZnd1BhVksxZTJqVjUiLCJ0eXAiOiJKV1QifQ.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bHLEGGACKwQNB9bNjA_ZrUjYojinBiMgN0pHvdrzZB4", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"emailNewsletter\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/users/me/preferences", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["users", "me", "preferences"]}}, "response": []}, {"name": "login Copy", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n      \"email\": \"<EMAIL>\",\n      \"password\": \"password123\"\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/auth/login", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["auth", "login"]}}, "response": []}, {"name": "Signup", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n      \"email\": \"<EMAIL>\",\n      \"password\": \"password123\"\n    }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/auth/signup", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["auth", "signup"]}}, "response": []}]}]}