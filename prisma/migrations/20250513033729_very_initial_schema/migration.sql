CREATE EXTENSION IF NOT EXISTS vector WITH SCHEMA public;

-- <PERSON><PERSON><PERSON>num
CREATE TYPE "public"."UserRole" AS ENUM ('USER', 'ADMIN', 'MODERATOR');

-- CreateEnum
CREATE TYPE "public"."UserStatus" AS ENUM ('ACTIVE', 'INACTIVE', 'PENDING', 'SUSPENDED');

-- <PERSON>reateEnum
CREATE TYPE "public"."TechnicalLevel" AS ENUM ('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT');

-- CreateEnum
CREATE TYPE "public"."EntityStatus" AS ENUM ('PENDING', 'ACTIVE', 'REJECTED', 'INACTIVE');

-- CreateEnum
CREATE TYPE "public"."AffiliateStatus" AS ENUM ('NONE', 'APPLIED', 'APPROVED', 'REJECTED');

-- CreateEnum
CREATE TYPE "public"."ReviewStatus" AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- CreateEnum
CREATE TYPE "public"."LearningCurve" AS ENUM ('LOW', 'MEDIUM', 'HIGH');

-- CreateEnum
CREATE TYPE "public"."PricingModel" AS ENUM ('FREE', 'FREEMIUM', 'PAID', 'SUBSCRIPTION', 'ONE_TIME', 'USAGE_BASED', 'OTHER');

-- CreateEnum
CREATE TYPE "public"."PriceRange" AS ENUM ('FREE', 'LOW', 'MEDIUM', 'HIGH', 'ENTERPRISE');

-- CreateEnum
CREATE TYPE "public"."SkillLevel" AS ENUM ('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT');

-- CreateEnum
CREATE TYPE "public"."ActionType" AS ENUM ('VIEW_ENTITY', 'CLICK_ENTITY_LINK', 'SAVE_ENTITY', 'UNSAVE_ENTITY', 'SUBMIT_REVIEW', 'VOTE_REVIEW', 'FOLLOW_TAG', 'UNFOLLOW_TAG', 'FOLLOW_CATEGORY', 'UNFOLLOW_CATEGORY', 'SEARCH', 'LOGIN', 'LOGOUT', 'SIGNUP', 'UPDATE_PROFILE', 'GRANT_BADGE', 'REVOKE_BADGE');

-- CreateEnum
CREATE TYPE "public"."BadgeScope" AS ENUM ('USER', 'ENTITY');

-- CreateTable
CREATE TABLE "public"."users" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "auth_user_id" UUID NOT NULL,
    "username" TEXT,
    "display_name" TEXT,
    "email" TEXT NOT NULL,
    "role" "public"."UserRole" NOT NULL DEFAULT 'USER',
    "status" "public"."UserStatus" NOT NULL DEFAULT 'ACTIVE',
    "technical_level" "public"."TechnicalLevel",
    "profile_picture_url" TEXT,
    "bio" TEXT,
    "social_links" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "last_login" TIMESTAMP(3),

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."entity_types" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "description" TEXT,
    "slug" TEXT NOT NULL,
    "icon_url" TEXT,
    "display_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_types_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."entities" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "entity_type_id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "short_description" TEXT,
    "description" TEXT,
    "logo_url" TEXT,
    "website_url" TEXT NOT NULL,
    "documentation_url" TEXT,
    "contact_url" TEXT,
    "privacy_policy_url" TEXT,
    "founded_year" INTEGER,
    "social_links" JSONB,
    "status" "public"."EntityStatus" NOT NULL DEFAULT 'PENDING',
    "verified" BOOLEAN NOT NULL DEFAULT false,
    "featured" BOOLEAN NOT NULL DEFAULT false,
    "sponsored" BOOLEAN NOT NULL DEFAULT false,
    "platform_vetted" BOOLEAN NOT NULL DEFAULT false,
    "avg_rating" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "review_count" INTEGER NOT NULL DEFAULT 0,
    "save_count" INTEGER NOT NULL DEFAULT 0,
    "click_count" INTEGER NOT NULL DEFAULT 0,
    "review_summary_ai" TEXT,
    "verification_token" UUID,
    "verification_email_sent" TIMESTAMP(3),
    "verified_at" TIMESTAMP(3),
    "verified_by" UUID,
    "affiliate_status" "public"."AffiliateStatus" NOT NULL DEFAULT 'NONE',
    "ref_link" TEXT,
    "user_submitted" BOOLEAN NOT NULL DEFAULT false,
    "submitter_user_id" UUID,
    "source_url" TEXT,
    "admin_notes" TEXT,
    "last_scraped_update" TIMESTAMP(3),
    "last_manual_update" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "vector_embedding" vector(1536),

    CONSTRAINT "entities_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."categories" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "description" TEXT,
    "slug" TEXT NOT NULL,
    "parent_category_id" UUID,
    "icon_url" TEXT,
    "display_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "categories_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."tags" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "description" TEXT,
    "slug" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "test_col" BOOLEAN DEFAULT false,

    CONSTRAINT "tags_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."entity_tags" (
    "entity_id" UUID NOT NULL,
    "tag_id" UUID NOT NULL,

    CONSTRAINT "entity_tags_pkey" PRIMARY KEY ("entity_id","tag_id")
);

-- CreateTable
CREATE TABLE "public"."entity_categories" (
    "entity_id" UUID NOT NULL,
    "category_id" UUID NOT NULL,

    CONSTRAINT "entity_categories_pkey" PRIMARY KEY ("entity_id","category_id")
);

-- CreateTable
CREATE TABLE "public"."reviews" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "entity_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "rating" INTEGER NOT NULL,
    "title" TEXT,
    "review_text" TEXT,
    "status" "public"."ReviewStatus" NOT NULL DEFAULT 'PENDING',
    "helpfulness_score" INTEGER NOT NULL DEFAULT 0,
    "moderator_user_id" UUID,
    "moderated_at" TIMESTAMP(3),
    "moderation_notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "reviews_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."review_votes" (
    "review_id" UUID NOT NULL,
    "user_id" UUID NOT NULL,
    "vote" SMALLINT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "review_votes_pkey" PRIMARY KEY ("review_id","user_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_tool" (
    "entity_id" UUID NOT NULL,
    "technical_level" "public"."TechnicalLevel",
    "learning_curve" "public"."LearningCurve",
    "target_audience" JSONB,
    "has_api" BOOLEAN NOT NULL DEFAULT false,
    "api_documentation_url" TEXT,
    "api_sandbox_url" TEXT,
    "key_features" JSONB,
    "use_cases" JSONB,
    "pricing_model" "public"."PricingModel",
    "price_range" "public"."PriceRange",
    "pricing_details" TEXT,
    "pricing_url" TEXT,
    "has_free_tier" BOOLEAN NOT NULL DEFAULT false,
    "platforms" JSONB,
    "integrations" JSONB,
    "supported_languages" JSONB,
    "current_version" TEXT,
    "last_version_update_date" DATE,
    "changelog_url" TEXT,

    CONSTRAINT "entity_details_tool_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_agency" (
    "entity_id" UUID NOT NULL,
    "services_offered" JSONB,
    "industry_focus" JSONB,
    "target_client_size" JSONB,
    "target_audience" JSONB,
    "location_summary" TEXT,
    "portfolio_url" TEXT,
    "pricing_info" TEXT,

    CONSTRAINT "entity_details_agency_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_content_creator" (
    "entity_id" UUID NOT NULL,
    "creator_name" TEXT,
    "primary_platform" TEXT,
    "focus_areas" JSONB,
    "follower_count" INTEGER DEFAULT 0,
    "example_content_url" TEXT,

    CONSTRAINT "entity_details_content_creator_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_community" (
    "entity_id" UUID NOT NULL,
    "platform" TEXT,
    "member_count" INTEGER DEFAULT 0,
    "focus_topics" JSONB,
    "rules_url" TEXT,
    "invite_url" TEXT,
    "main_channel_url" TEXT,

    CONSTRAINT "entity_details_community_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_newsletter" (
    "entity_id" UUID NOT NULL,
    "frequency" TEXT,
    "main_topics" JSONB,
    "archive_url" TEXT,
    "subscribe_url" TEXT,
    "author_name" TEXT,
    "subscriber_count" INTEGER DEFAULT 0,

    CONSTRAINT "entity_details_newsletter_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_course" (
    "entity_id" UUID NOT NULL,
    "instructor_name" TEXT,
    "duration_text" TEXT,
    "skill_level" "public"."SkillLevel",
    "prerequisites" TEXT,
    "syllabus_url" TEXT,
    "enrollment_count" INTEGER DEFAULT 0,
    "certificate_available" BOOLEAN DEFAULT false,

    CONSTRAINT "entity_details_course_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."user_saved_entities" (
    "user_id" UUID NOT NULL,
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_saved_entities_pkey" PRIMARY KEY ("user_id","entity_id")
);

-- CreateTable
CREATE TABLE "public"."user_followed_tags" (
    "user_id" UUID NOT NULL,
    "tag_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_followed_tags_pkey" PRIMARY KEY ("user_id","tag_id")
);

-- CreateTable
CREATE TABLE "public"."user_followed_categories" (
    "user_id" UUID NOT NULL,
    "category_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_followed_categories_pkey" PRIMARY KEY ("user_id","category_id")
);

-- CreateTable
CREATE TABLE "public"."user_activity_logs" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL,
    "action_type" "public"."ActionType" NOT NULL,
    "entity_id" UUID,
    "category_id" UUID,
    "tag_id" UUID,
    "review_id" UUID,
    "target_user_id" UUID,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "details" JSONB,

    CONSTRAINT "user_activity_logs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."user_notification_settings" (
    "user_id" UUID NOT NULL,
    "email_newsletter" BOOLEAN NOT NULL DEFAULT true,
    "email_new_entity_in_followed_category" BOOLEAN NOT NULL DEFAULT true,
    "email_new_entity_in_followed_tag" BOOLEAN NOT NULL DEFAULT false,
    "email_new_review_on_saved_entity" BOOLEAN NOT NULL DEFAULT true,
    "email_updates_on_saved_entity" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_notification_settings_pkey" PRIMARY KEY ("user_id")
);

-- CreateTable
CREATE TABLE "public"."badge_types" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "icon_url" TEXT NOT NULL,
    "criteria_details" TEXT,
    "scope" "public"."BadgeScope" NOT NULL,
    "is_auto_granted" BOOLEAN NOT NULL DEFAULT false,
    "is_manual_granted" BOOLEAN NOT NULL DEFAULT true,
    "display_order" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "badge_types_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."user_badges" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL,
    "badge_type_id" UUID NOT NULL,
    "granted_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "granted_by_user_id" UUID,
    "notes" TEXT,

    CONSTRAINT "user_badges_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."entity_badges" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "entity_id" UUID NOT NULL,
    "badge_type_id" UUID NOT NULL,
    "granted_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "granted_by_user_id" UUID,
    "notes" TEXT,

    CONSTRAINT "entity_badges_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "users_auth_user_id_key" ON "public"."users"("auth_user_id");

-- CreateIndex
CREATE UNIQUE INDEX "users_username_key" ON "public"."users"("username");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "public"."users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "entity_types_name_key" ON "public"."entity_types"("name");

-- CreateIndex
CREATE UNIQUE INDEX "entity_types_slug_key" ON "public"."entity_types"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "entities_verification_token_key" ON "public"."entities"("verification_token");

-- CreateIndex
CREATE INDEX "entities_entity_type_id_idx" ON "public"."entities"("entity_type_id");

-- CreateIndex
CREATE INDEX "entities_submitter_user_id_idx" ON "public"."entities"("submitter_user_id");

-- CreateIndex
CREATE INDEX "entities_status_idx" ON "public"."entities"("status");

-- CreateIndex
CREATE INDEX "entities_name_idx" ON "public"."entities"("name");

-- CreateIndex
CREATE UNIQUE INDEX "categories_name_key" ON "public"."categories"("name");

-- CreateIndex
CREATE UNIQUE INDEX "categories_slug_key" ON "public"."categories"("slug");

-- CreateIndex
CREATE INDEX "categories_parent_category_id_idx" ON "public"."categories"("parent_category_id");

-- CreateIndex
CREATE UNIQUE INDEX "tags_name_key" ON "public"."tags"("name");

-- CreateIndex
CREATE UNIQUE INDEX "tags_slug_key" ON "public"."tags"("slug");

-- CreateIndex
CREATE INDEX "reviews_entity_id_idx" ON "public"."reviews"("entity_id");

-- CreateIndex
CREATE INDEX "reviews_user_id_idx" ON "public"."reviews"("user_id");

-- CreateIndex
CREATE INDEX "reviews_moderator_user_id_idx" ON "public"."reviews"("moderator_user_id");

-- CreateIndex
CREATE INDEX "user_activity_logs_user_id_idx" ON "public"."user_activity_logs"("user_id");

-- CreateIndex
CREATE INDEX "user_activity_logs_action_type_idx" ON "public"."user_activity_logs"("action_type");

-- CreateIndex
CREATE INDEX "user_activity_logs_entity_id_idx" ON "public"."user_activity_logs"("entity_id");

-- CreateIndex
CREATE INDEX "user_activity_logs_category_id_idx" ON "public"."user_activity_logs"("category_id");

-- CreateIndex
CREATE INDEX "user_activity_logs_tag_id_idx" ON "public"."user_activity_logs"("tag_id");

-- CreateIndex
CREATE INDEX "user_activity_logs_review_id_idx" ON "public"."user_activity_logs"("review_id");

-- CreateIndex
CREATE INDEX "user_activity_logs_target_user_id_idx" ON "public"."user_activity_logs"("target_user_id");

-- CreateIndex
CREATE UNIQUE INDEX "badge_types_name_key" ON "public"."badge_types"("name");

-- CreateIndex
CREATE UNIQUE INDEX "badge_types_slug_key" ON "public"."badge_types"("slug");

-- CreateIndex
CREATE INDEX "user_badges_user_id_idx" ON "public"."user_badges"("user_id");

-- CreateIndex
CREATE INDEX "user_badges_badge_type_id_idx" ON "public"."user_badges"("badge_type_id");

-- CreateIndex
CREATE INDEX "user_badges_granted_by_user_id_idx" ON "public"."user_badges"("granted_by_user_id");

-- CreateIndex
CREATE INDEX "entity_badges_entity_id_idx" ON "public"."entity_badges"("entity_id");

-- CreateIndex
CREATE INDEX "entity_badges_badge_type_id_idx" ON "public"."entity_badges"("badge_type_id");

-- CreateIndex
CREATE INDEX "entity_badges_granted_by_user_id_idx" ON "public"."entity_badges"("granted_by_user_id");

-- AddForeignKey
ALTER TABLE "public"."entities" ADD CONSTRAINT "entities_entity_type_id_fkey" FOREIGN KEY ("entity_type_id") REFERENCES "public"."entity_types"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entities" ADD CONSTRAINT "entities_submitter_user_id_fkey" FOREIGN KEY ("submitter_user_id") REFERENCES "public"."users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."categories" ADD CONSTRAINT "categories_parent_category_id_fkey" FOREIGN KEY ("parent_category_id") REFERENCES "public"."categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_tags" ADD CONSTRAINT "entity_tags_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_tags" ADD CONSTRAINT "entity_tags_tag_id_fkey" FOREIGN KEY ("tag_id") REFERENCES "public"."tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_categories" ADD CONSTRAINT "entity_categories_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_categories" ADD CONSTRAINT "entity_categories_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."reviews" ADD CONSTRAINT "reviews_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."reviews" ADD CONSTRAINT "reviews_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."reviews" ADD CONSTRAINT "reviews_moderator_user_id_fkey" FOREIGN KEY ("moderator_user_id") REFERENCES "public"."users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."review_votes" ADD CONSTRAINT "review_votes_review_id_fkey" FOREIGN KEY ("review_id") REFERENCES "public"."reviews"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."review_votes" ADD CONSTRAINT "review_votes_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_tool" ADD CONSTRAINT "entity_details_tool_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_agency" ADD CONSTRAINT "entity_details_agency_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_content_creator" ADD CONSTRAINT "entity_details_content_creator_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_community" ADD CONSTRAINT "entity_details_community_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_newsletter" ADD CONSTRAINT "entity_details_newsletter_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_course" ADD CONSTRAINT "entity_details_course_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_saved_entities" ADD CONSTRAINT "user_saved_entities_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_saved_entities" ADD CONSTRAINT "user_saved_entities_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_followed_tags" ADD CONSTRAINT "user_followed_tags_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_followed_tags" ADD CONSTRAINT "user_followed_tags_tag_id_fkey" FOREIGN KEY ("tag_id") REFERENCES "public"."tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_followed_categories" ADD CONSTRAINT "user_followed_categories_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_followed_categories" ADD CONSTRAINT "user_followed_categories_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_activity_logs" ADD CONSTRAINT "user_activity_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_activity_logs" ADD CONSTRAINT "user_activity_logs_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_activity_logs" ADD CONSTRAINT "user_activity_logs_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_activity_logs" ADD CONSTRAINT "user_activity_logs_tag_id_fkey" FOREIGN KEY ("tag_id") REFERENCES "public"."tags"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_activity_logs" ADD CONSTRAINT "user_activity_logs_review_id_fkey" FOREIGN KEY ("review_id") REFERENCES "public"."reviews"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_activity_logs" ADD CONSTRAINT "user_activity_logs_target_user_id_fkey" FOREIGN KEY ("target_user_id") REFERENCES "public"."users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_notification_settings" ADD CONSTRAINT "user_notification_settings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_badges" ADD CONSTRAINT "user_badges_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_badges" ADD CONSTRAINT "user_badges_badge_type_id_fkey" FOREIGN KEY ("badge_type_id") REFERENCES "public"."badge_types"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_badges" ADD CONSTRAINT "user_badges_granted_by_user_id_fkey" FOREIGN KEY ("granted_by_user_id") REFERENCES "public"."users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_badges" ADD CONSTRAINT "entity_badges_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_badges" ADD CONSTRAINT "entity_badges_badge_type_id_fkey" FOREIGN KEY ("badge_type_id") REFERENCES "public"."badge_types"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_badges" ADD CONSTRAINT "entity_badges_granted_by_user_id_fkey" FOREIGN KEY ("granted_by_user_id") REFERENCES "public"."users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
