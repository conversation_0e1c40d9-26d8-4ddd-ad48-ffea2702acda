-- This is an empty migration.

-- 1. Create the trigger function
CREATE OR REPLACE FUNCTION public.update_entity_save_count()
RETURNS TRIGGER AS $$
DECLARE
  target_entity_id uuid;
  new_count int;
BEGIN
  IF (TG_OP = 'DELETE') THEN
    target_entity_id := OLD.entity_id;
  ELSE -- INSERT
    target_entity_id := NEW.entity_id;
  END IF;

  -- Recalculate count for the specific entity
  SELECT COUNT(*) INTO new_count
  FROM public.user_saved_entities
  WHERE entity_id = target_entity_id;

  -- Update the parent entity
  UPDATE public.entities
  SET save_count = new_count,
      updated_at = now() -- Also update entity timestamp
  WHERE id = target_entity_id;

  IF (TG_OP = 'DELETE') THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; -- Consider SECURITY DEFINER if <PERSON><PERSON> might interfere
COMMENT ON FUNCTION public.update_entity_save_count() IS 'Updates the save_count on the entities table when a user saves or unsaves an entity.';

-- 2. Apply the trigger to the user_saved_entities table
DROP TRIGGER IF EXISTS trigger_update_entity_save_count ON public.user_saved_entities;
CREATE TRIGGER trigger_update_entity_save_count
AFTER INSERT OR DELETE ON public.user_saved_entities
FOR EACH ROW EXECUTE FUNCTION public.update_entity_save_count();

COMMENT ON TRIGGER trigger_update_entity_save_count ON public.user_saved_entities IS 'Automatically updates the save_count on the entities table after a bookmark is created or deleted.';