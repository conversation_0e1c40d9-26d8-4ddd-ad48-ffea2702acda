/*
  Warnings:

  - A unique constraint covering the columns `[user_id,entity_id]` on the table `reviews` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateIndex
CREATE UNIQUE INDEX "reviews_user_id_entity_id_key" ON "public"."reviews"("user_id", "entity_id");

-- Function to update review_count and avg_rating on the entities table
CREATE OR REPLACE FUNCTION public.update_entity_rating_stats()
RETURNS TRIGGER AS $$
DECLARE
  target_entity_id uuid;
  new_review_count int;
  new_avg_rating float;
BEGIN
  -- Determine the target entity_id based on the operation
  IF (TG_OP = 'DELETE') THEN
    target_entity_id := OLD.entity_id;
  ELSE -- INSERT or UPDATE
    target_entity_id := NEW.entity_id;
  END IF;

  -- Recalculate review_count and avg_rating for the specific entity
  -- Only consider 'APPROVED' reviews
  SELECT
    COUNT(*),
    COALESCE(AVG(rating), 0)
  INTO
    new_review_count,
    new_avg_rating
  FROM
    public.reviews
  WHERE
    entity_id = target_entity_id AND status = 'APPROVED';

  -- Update the parent entity
  UPDATE public.entities
  SET
    review_count = new_review_count,
    avg_rating = new_avg_rating,
    updated_at = NOW() -- Also update the entity's timestamp
  WHERE
    id = target_entity_id;

  -- Return the appropriate record
  IF (TG_OP = 'DELETE') THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMENT ON FUNCTION public.update_entity_rating_stats() IS 'Updates review_count and avg_rating on the entities table when a review is inserted, updated (status or rating), or deleted.';

-- Apply the trigger to the reviews table
DROP TRIGGER IF EXISTS trigger_update_entity_stats_on_review_change ON public.reviews;
CREATE TRIGGER trigger_update_entity_stats_on_review_change
AFTER INSERT OR DELETE OR UPDATE OF rating, status ON public.reviews
FOR EACH ROW EXECUTE FUNCTION public.update_entity_rating_stats();

COMMENT ON TRIGGER trigger_update_entity_stats_on_review_change ON public.reviews IS 'Automatically updates review_count and avg_rating on the entities table after a review is created, deleted, or its rating/status changes.';
