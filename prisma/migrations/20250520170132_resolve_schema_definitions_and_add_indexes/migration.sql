/*
  Warnings:

  - You are about to drop the column `admin_notes` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `affiliate_status` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `click_count` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `featured` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `last_manual_update` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `last_scraped_update` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `platform_vetted` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `ref_link` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `review_summary_ai` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `save_count` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `source_url` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `sponsored` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `submitter_user_id` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `user_submitted` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `vector_embedding` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `verification_email_sent` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `verification_token` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `verified` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `verified_at` on the `entities` table. All the data in the column will be lost.
  - You are about to drop the column `verified_by` on the `entities` table. All the data in the column will be lost.
  - Added the required column `submitter_id` to the `entities` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "public"."entities" DROP CONSTRAINT "entities_entity_type_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."entities" DROP CONSTRAINT "entities_submitter_user_id_fkey";

-- DropIndex
DROP INDEX "public"."entities_submitter_user_id_idx";

-- DropIndex
DROP INDEX "public"."entities_verification_token_key";

-- AlterTable
ALTER TABLE "public"."entities" DROP COLUMN "admin_notes",
DROP COLUMN "affiliate_status",
DROP COLUMN "click_count",
DROP COLUMN "featured",
DROP COLUMN "last_manual_update",
DROP COLUMN "last_scraped_update",
DROP COLUMN "platform_vetted",
DROP COLUMN "ref_link",
DROP COLUMN "review_summary_ai",
DROP COLUMN "save_count",
DROP COLUMN "source_url",
DROP COLUMN "sponsored",
DROP COLUMN "submitter_user_id",
DROP COLUMN "user_submitted",
DROP COLUMN "vector_embedding",
DROP COLUMN "verification_email_sent",
DROP COLUMN "verification_token",
DROP COLUMN "verified",
DROP COLUMN "verified_at",
DROP COLUMN "verified_by",
ADD COLUMN     "legacy_id" TEXT,
ADD COLUMN     "submitter_id" UUID,
ALTER COLUMN "website_url" DROP NOT NULL;

-- CreateTable
CREATE TABLE "public"."entity_details_dataset" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_dataset_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_research_paper" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_research_paper_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_software" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_software_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_model" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_model_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_project_reference" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_project_reference_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_service_provider" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_service_provider_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_investor" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_investor_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_event" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_event_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_job" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_job_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_grant" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_grant_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_bounty" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_bounty_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_hardware" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_hardware_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_news" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_news_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_book" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_book_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_podcast" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_podcast_pkey" PRIMARY KEY ("entity_id")
);

-- CreateTable
CREATE TABLE "public"."entity_details_platform" (
    "entity_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "entity_details_platform_pkey" PRIMARY KEY ("entity_id")
);

-- CreateIndex
CREATE INDEX "entities_submitter_id_idx" ON "public"."entities"("submitter_id");

-- CreateIndex
CREATE INDEX "entities_created_at_idx" ON "public"."entities"("created_at");

-- CreateIndex
CREATE INDEX "entities_updated_at_idx" ON "public"."entities"("updated_at");

-- RenameForeignKey
ALTER TABLE "public"."entity_categories" RENAME CONSTRAINT "entity_categories_category_id_fkey" TO "fk_entity_category_category_id";

-- RenameForeignKey
ALTER TABLE "public"."entity_categories" RENAME CONSTRAINT "entity_categories_entity_id_fkey" TO "fk_entity_category_entity_id";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_agency" RENAME CONSTRAINT "entity_details_agency_entity_id_fkey" TO "fk_entity_details_agency_entity_id";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_community" RENAME CONSTRAINT "entity_details_community_entity_id_fkey" TO "fk_entity_details_community_entity_id";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_content_creator" RENAME CONSTRAINT "entity_details_content_creator_entity_id_fkey" TO "fk_entity_details_content_creator_entity_id";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_course" RENAME CONSTRAINT "entity_details_course_entity_id_fkey" TO "fk_entity_details_course_entity_id";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_newsletter" RENAME CONSTRAINT "entity_details_newsletter_entity_id_fkey" TO "fk_entity_details_newsletter_entity_id";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_tool" RENAME CONSTRAINT "entity_details_tool_entity_id_fkey" TO "fk_entity_details_tool_entity_id";

-- RenameForeignKey
ALTER TABLE "public"."entity_tags" RENAME CONSTRAINT "entity_tags_entity_id_fkey" TO "fk_entity_tag_entity_id";

-- RenameForeignKey
ALTER TABLE "public"."entity_tags" RENAME CONSTRAINT "entity_tags_tag_id_fkey" TO "fk_entity_tag_tag_id";

-- RenameForeignKey
ALTER TABLE "public"."reviews" RENAME CONSTRAINT "reviews_entity_id_fkey" TO "fk_review_entity_id";

-- RenameForeignKey
ALTER TABLE "public"."reviews" RENAME CONSTRAINT "reviews_moderator_user_id_fkey" TO "fk_review_moderator_id";

-- RenameForeignKey
ALTER TABLE "public"."reviews" RENAME CONSTRAINT "reviews_user_id_fkey" TO "fk_review_user_id";

-- RenameForeignKey
ALTER TABLE "public"."user_activity_logs" RENAME CONSTRAINT "user_activity_logs_category_id_fkey" TO "fk_user_activity_log_category_id";

-- RenameForeignKey
ALTER TABLE "public"."user_activity_logs" RENAME CONSTRAINT "user_activity_logs_entity_id_fkey" TO "fk_user_activity_log_entity_id";

-- RenameForeignKey
ALTER TABLE "public"."user_activity_logs" RENAME CONSTRAINT "user_activity_logs_review_id_fkey" TO "fk_user_activity_log_review_id";

-- RenameForeignKey
ALTER TABLE "public"."user_activity_logs" RENAME CONSTRAINT "user_activity_logs_tag_id_fkey" TO "fk_user_activity_log_tag_id";

-- RenameForeignKey
ALTER TABLE "public"."user_activity_logs" RENAME CONSTRAINT "user_activity_logs_target_user_id_fkey" TO "fk_user_activity_log_target_user_id";

-- RenameForeignKey
ALTER TABLE "public"."user_activity_logs" RENAME CONSTRAINT "user_activity_logs_user_id_fkey" TO "fk_user_activity_log_user_id";

-- RenameForeignKey
ALTER TABLE "public"."user_saved_entities" RENAME CONSTRAINT "user_saved_entities_entity_id_fkey" TO "fk_user_saved_entity_entity_id";

-- RenameForeignKey
ALTER TABLE "public"."user_saved_entities" RENAME CONSTRAINT "user_saved_entities_user_id_fkey" TO "fk_user_saved_entity_user_id";

-- AddForeignKey
ALTER TABLE "public"."entities" ADD CONSTRAINT "fk_entity_entity_type_id" FOREIGN KEY ("entity_type_id") REFERENCES "public"."entity_types"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entities" ADD CONSTRAINT "fk_entity_submitter_id" FOREIGN KEY ("submitter_id") REFERENCES "public"."users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_dataset" ADD CONSTRAINT "fk_entity_details_dataset_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_research_paper" ADD CONSTRAINT "fk_entity_details_research_paper_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_software" ADD CONSTRAINT "fk_entity_details_software_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_model" ADD CONSTRAINT "fk_entity_details_model_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_project_reference" ADD CONSTRAINT "fk_entity_details_project_reference_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_service_provider" ADD CONSTRAINT "fk_entity_details_service_provider_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_investor" ADD CONSTRAINT "fk_entity_details_investor_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_event" ADD CONSTRAINT "fk_entity_details_event_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_job" ADD CONSTRAINT "fk_entity_details_job_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_grant" ADD CONSTRAINT "fk_entity_details_grant_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_bounty" ADD CONSTRAINT "fk_entity_details_bounty_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_hardware" ADD CONSTRAINT "fk_entity_details_hardware_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_news" ADD CONSTRAINT "fk_entity_details_news_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_book" ADD CONSTRAINT "fk_entity_details_book_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_podcast" ADD CONSTRAINT "fk_entity_details_podcast_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_details_platform" ADD CONSTRAINT "fk_entity_details_platform_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;
