-- This is an empty migration.

-- Add a tsvector column to store the document for FTS
ALTER TABLE "public"."entities"
ADD COLUMN "search_vector" tsvector;

-- Create a function to update the tsvector column
CREATE OR REPLACE FUNCTION public.update_entity_search_vector()
RETURNS TRIGGER AS $$
BEGIN
  NEW.search_vector :=
    setweight(to_tsvector('english', coalesce(NEW.name, '')), 'A') ||
    setweight(to_tsvector('english', coalesce(NEW.description, '')), 'B') ||
    setweight(to_tsvector('english', coalesce(NEW.short_description, '')), 'C');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to automatically update the tsvector column
DROP TRIGGER IF EXISTS tsvector_update_trigger ON "public"."entities";
CREATE TRIGGER tsvector_update_trigger
BEFORE INSERT OR UPDATE OF name, description, short_description ON "public"."entities"
FOR EACH ROW EXECUTE FUNCTION public.update_entity_search_vector();

-- Create a GIN index on the tsvector column for performance
CREATE INDEX IF NOT EXISTS entities_search_vector_idx ON "public"."entities" USING GIN (search_vector);

-- IMPORTANT: To populate existing rows, run the following UPDATE statement MANUALLY in your Supabase SQL editor
-- AFTER this migration has been applied, especially if you have existing data.
-- It's commented out here to prevent it from running automatically during every migration apply if not needed.
-- UPDATE "public"."entities" SET search_vector =
--    setweight(to_tsvector('english', coalesce(name, '')), 'A') ||
--    setweight(to_tsvector('english', coalesce(description, '')), 'B') ||
--    setweight(to_tsvector('english', coalesce(short_description, '')), 'C')
-- WHERE search_vector IS NULL;