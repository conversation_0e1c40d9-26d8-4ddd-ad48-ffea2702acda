/*
  Warnings:

  - You are about to drop the column `search_vector` on the `entities` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[isbn]` on the table `entity_details_book` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[article_url]` on the table `entity_details_news` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[doi]` on the table `entity_details_research_paper` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `article_url` to the `entity_details_news` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "public"."entities_search_vector_idx";

-- AlterTable
ALTER TABLE "public"."entities" DROP COLUMN "search_vector";

-- Drop the trigger and function associated with the dropped search_vector column
DROP TRIGGER IF EXISTS tsvector_update_trigger ON "public"."entities";
DROP FUNCTION IF EXISTS public.update_entity_search_vector();

-- AlterTable
ALTER TABLE "public"."entity_details_book" ADD COLUMN     "authorNames" JSONB,
ADD COLUMN     "isbn" TEXT,
ADD COLUMN     "page_count" INTEGER,
ADD COLUMN     "publication_year" INTEGER,
ADD COLUMN     "publisher" TEXT,
ADD COLUMN     "purchase_url" TEXT,
ADD COLUMN     "summary" TEXT;

-- AlterTable
ALTER TABLE "public"."entity_details_bounty" ADD COLUMN     "bounty_issuer" TEXT,
ADD COLUMN     "difficulty_level" TEXT,
ADD COLUMN     "platform_url" TEXT,
ADD COLUMN     "requirements" TEXT,
ADD COLUMN     "reward_amount" TEXT,
ADD COLUMN     "submission_deadline" DATE;

-- AlterTable
ALTER TABLE "public"."entity_details_dataset" ADD COLUMN     "access_notes" TEXT,
ADD COLUMN     "description" TEXT,
ADD COLUMN     "format" TEXT,
ADD COLUMN     "license" TEXT,
ADD COLUMN     "size_in_bytes" BIGINT,
ADD COLUMN     "source_url" TEXT;

-- AlterTable
ALTER TABLE "public"."entity_details_event" ADD COLUMN     "agenda_url" TEXT,
ADD COLUMN     "end_date" TIMESTAMP(3),
ADD COLUMN     "eventType" TEXT,
ADD COLUMN     "location" TEXT,
ADD COLUMN     "price" TEXT,
ADD COLUMN     "registration_url" TEXT,
ADD COLUMN     "speakerList" JSONB,
ADD COLUMN     "start_date" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "public"."entity_details_grant" ADD COLUMN     "application_deadline" DATE,
ADD COLUMN     "application_url" TEXT,
ADD COLUMN     "eligibility_criteria" TEXT,
ADD COLUMN     "funding_amount" TEXT,
ADD COLUMN     "grant_focus_area" TEXT,
ADD COLUMN     "granting_institution" TEXT;

-- AlterTable
ALTER TABLE "public"."entity_details_hardware" ADD COLUMN     "datasheet_url" TEXT,
ADD COLUMN     "hardware_type" TEXT,
ADD COLUMN     "manufacturer" TEXT,
ADD COLUMN     "price_range" TEXT,
ADD COLUMN     "release_date" DATE,
ADD COLUMN     "specifications" JSONB;

-- AlterTable
ALTER TABLE "public"."entity_details_investor" ADD COLUMN     "contact_email" TEXT,
ADD COLUMN     "investmentFocusAreas" JSONB,
ADD COLUMN     "investmentStages" JSONB,
ADD COLUMN     "portfolio_url" TEXT,
ADD COLUMN     "preferred_communication" TEXT,
ADD COLUMN     "typical_investment_size" TEXT;

-- AlterTable
ALTER TABLE "public"."entity_details_job" ADD COLUMN     "application_url" TEXT,
ADD COLUMN     "company_name" TEXT,
ADD COLUMN     "employment_type" TEXT,
ADD COLUMN     "experience_level" TEXT,
ADD COLUMN     "job_description" TEXT,
ADD COLUMN     "job_title" TEXT,
ADD COLUMN     "location_type" TEXT,
ADD COLUMN     "salary_range" TEXT;

-- AlterTable
ALTER TABLE "public"."entity_details_model" ADD COLUMN     "license" TEXT,
ADD COLUMN     "model_architecture" TEXT,
ADD COLUMN     "model_url" TEXT,
ADD COLUMN     "parameters_count" BIGINT,
ADD COLUMN     "performanceMetrics" JSONB,
ADD COLUMN     "training_dataset" TEXT;

-- AlterTable
ALTER TABLE "public"."entity_details_news" ADD COLUMN     "article_url" TEXT NOT NULL,
ADD COLUMN     "author" TEXT,
ADD COLUMN     "publication_date" DATE,
ADD COLUMN     "source_name" TEXT,
ADD COLUMN     "summary" TEXT;

-- AlterTable
ALTER TABLE "public"."entity_details_platform" ADD COLUMN     "documentation_url" TEXT,
ADD COLUMN     "keyServices" JSONB,
ADD COLUMN     "platform_type" TEXT,
ADD COLUMN     "pricing_model" TEXT,
ADD COLUMN     "sla_url" TEXT,
ADD COLUMN     "supported_regions" JSONB;

-- AlterTable
ALTER TABLE "public"."entity_details_podcast" ADD COLUMN     "average_episode_length" TEXT,
ADD COLUMN     "frequency" TEXT,
ADD COLUMN     "hostNames" JSONB,
ADD COLUMN     "listen_url" TEXT,
ADD COLUMN     "mainTopics" JSONB,
ADD COLUMN     "primary_language" TEXT DEFAULT 'English';

-- AlterTable
ALTER TABLE "public"."entity_details_project_reference" ADD COLUMN     "contributors" JSONB,
ADD COLUMN     "live_demo_url" TEXT,
ADD COLUMN     "project_goals" TEXT,
ADD COLUMN     "project_status" TEXT,
ADD COLUMN     "source_code_url" TEXT,
ADD COLUMN     "technologies" JSONB;

-- AlterTable
ALTER TABLE "public"."entity_details_research_paper" ADD COLUMN     "abstract" TEXT,
ADD COLUMN     "authors" JSONB,
ADD COLUMN     "citation_count" INTEGER DEFAULT 0,
ADD COLUMN     "doi" TEXT,
ADD COLUMN     "journal_or_conference" TEXT,
ADD COLUMN     "publication_date" DATE,
ADD COLUMN     "publication_url" TEXT;

-- AlterTable
ALTER TABLE "public"."entity_details_service_provider" ADD COLUMN     "case_studies_url" TEXT,
ADD COLUMN     "company_size_focus" TEXT,
ADD COLUMN     "consultation_booking_url" TEXT,
ADD COLUMN     "hourly_rate_range" TEXT,
ADD COLUMN     "industrySpecializations" JSONB,
ADD COLUMN     "serviceAreas" JSONB;

-- AlterTable
ALTER TABLE "public"."entity_details_software" ADD COLUMN     "current_version" TEXT,
ADD COLUMN     "license_type" TEXT,
ADD COLUMN     "platformCompatibility" JSONB,
ADD COLUMN     "programmingLanguages" JSONB,
ADD COLUMN     "release_date" DATE,
ADD COLUMN     "repository_url" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_book_isbn_key" ON "public"."entity_details_book"("isbn");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_news_article_url_key" ON "public"."entity_details_news"("article_url");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_research_paper_doi_key" ON "public"."entity_details_research_paper"("doi");
