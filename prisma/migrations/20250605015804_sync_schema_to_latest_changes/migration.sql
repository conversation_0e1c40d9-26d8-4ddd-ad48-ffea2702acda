/*
  Warnings:

  - You are about to drop the column `display_order` on the `badge_types` table. All the data in the column will be lost.
  - You are about to drop the column `display_order` on the `categories` table. All the data in the column will be lost.
  - You are about to drop the column `display_order` on the `entity_types` table. All the data in the column will be lost.
  - You are about to drop the column `test_col` on the `tags` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "public"."badge_types" DROP COLUMN "display_order";

-- AlterTable
ALTER TABLE "public"."categories" DROP COLUMN "display_order";

-- AlterTable
ALTER TABLE "public"."entity_categories" ADD COLUMN     "assigned_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "public"."entity_types" DROP COLUMN "display_order";

-- AlterTable
ALTER TABLE "public"."tags" DROP COLUMN "test_col",
ADD COLUMN     "icon_url" TEXT;

-- CreateTable
CREATE TABLE "public"."entity_features" (
    "entity_id" UUID NOT NULL,
    "feature_id" UUID NOT NULL,
    "assigned_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "pk_entity_feature" PRIMARY KEY ("entity_id","feature_id")
);

-- CreateTable
CREATE TABLE "public"."features" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "description" TEXT,
    "icon_url" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "features_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_entity_feature_feature_id" ON "public"."entity_features"("feature_id");

-- CreateIndex
CREATE INDEX "idx_entity_feature_entity_id" ON "public"."entity_features"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "features_name_key" ON "public"."features"("name");

-- CreateIndex
CREATE UNIQUE INDEX "features_slug_key" ON "public"."features"("slug");

-- CreateIndex
CREATE INDEX "idx_entity_category_category_id" ON "public"."entity_categories"("category_id");

-- CreateIndex
CREATE INDEX "idx_entity_category_entity_id" ON "public"."entity_categories"("entity_id");

-- RenameForeignKey
ALTER TABLE "public"."categories" RENAME CONSTRAINT "categories_parent_category_id_fkey" TO "fk_category_parent_category_id";

-- AddForeignKey
ALTER TABLE "public"."entity_features" ADD CONSTRAINT "fk_entity_feature_entity_id" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_features" ADD CONSTRAINT "fk_entity_feature_feature_id" FOREIGN KEY ("feature_id") REFERENCES "public"."features"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- RenameIndex
ALTER INDEX "public"."entities_entity_type_id_idx" RENAME TO "idx_entity_entity_type_id";
