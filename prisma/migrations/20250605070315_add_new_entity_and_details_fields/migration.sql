/*
  Warnings:

  - You are about to drop the column `keyServices` on the `entity_details_platform` table. All the data in the column will be lost.
  - You are about to drop the column `platformCompatibility` on the `entity_details_software` table. All the data in the column will be lost.
  - You are about to drop the column `programmingLanguages` on the `entity_details_software` table. All the data in the column will be lost.
  - You are about to drop the column `api_documentation_url` on the `entity_details_tool` table. All the data in the column will be lost.
  - You are about to drop the column `api_sandbox_url` on the `entity_details_tool` table. All the data in the column will be lost.
  - You are about to drop the column `changelog_url` on the `entity_details_tool` table. All the data in the column will be lost.
  - You are about to drop the column `current_version` on the `entity_details_tool` table. All the data in the column will be lost.
  - You are about to drop the column `has_api` on the `entity_details_tool` table. All the data in the column will be lost.
  - You are about to drop the column `last_version_update_date` on the `entity_details_tool` table. All the data in the column will be lost.
  - You are about to drop the column `platforms` on the `entity_details_tool` table. All the data in the column will be lost.
  - You are about to drop the column `supported_languages` on the `entity_details_tool` table. All the data in the column will be lost.
  - You are about to drop the column `technical_level` on the `entity_details_tool` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "public"."entities" ADD COLUMN     "affiliateStatus" "public"."AffiliateStatus" DEFAULT 'NONE',
ADD COLUMN     "employee_count_range" TEXT,
ADD COLUMN     "funding_stage" TEXT,
ADD COLUMN     "location_summary" TEXT,
ADD COLUMN     "meta_description" TEXT,
ADD COLUMN     "meta_title" TEXT,
ADD COLUMN     "ref_link" TEXT,
ADD COLUMN     "scraped_review_count" INTEGER DEFAULT 0,
ADD COLUMN     "scraped_review_sentiment_label" TEXT,
ADD COLUMN     "scraped_review_sentiment_score" DOUBLE PRECISION,
ADD COLUMN     "vector_embedding" vector(1536);

-- AlterTable
ALTER TABLE "public"."entity_details_platform" DROP COLUMN "keyServices",
ADD COLUMN     "community_url" TEXT,
ADD COLUMN     "has_free_tier" BOOLEAN DEFAULT false,
ADD COLUMN     "has_live_chat" BOOLEAN DEFAULT false,
ADD COLUMN     "integrations" JSONB,
ADD COLUMN     "key_services" JSONB,
ADD COLUMN     "price_range" "public"."PriceRange",
ADD COLUMN     "pricing_details" TEXT,
ADD COLUMN     "pricing_url" TEXT,
ADD COLUMN     "support_email" TEXT,
ADD COLUMN     "use_cases" JSONB;

-- AlterTable
ALTER TABLE "public"."entity_details_software" DROP COLUMN "platformCompatibility",
DROP COLUMN "programmingLanguages",
ADD COLUMN     "community_url" TEXT,
ADD COLUMN     "has_free_tier" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "has_live_chat" BOOLEAN DEFAULT false,
ADD COLUMN     "integrations" JSONB,
ADD COLUMN     "platform_compatibility" JSONB,
ADD COLUMN     "price_range" "public"."PriceRange",
ADD COLUMN     "pricing_details" TEXT,
ADD COLUMN     "pricing_model" "public"."PricingModel",
ADD COLUMN     "pricing_url" TEXT,
ADD COLUMN     "programming_languages" JSONB,
ADD COLUMN     "support_email" TEXT,
ADD COLUMN     "use_cases" JSONB;

-- AlterTable
ALTER TABLE "public"."entity_details_tool" DROP COLUMN "api_documentation_url",
DROP COLUMN "api_sandbox_url",
DROP COLUMN "changelog_url",
DROP COLUMN "current_version",
DROP COLUMN "has_api",
DROP COLUMN "last_version_update_date",
DROP COLUMN "platforms",
DROP COLUMN "supported_languages",
DROP COLUMN "technical_level",
ADD COLUMN     "api_access" BOOLEAN,
ADD COLUMN     "community_url" TEXT,
ADD COLUMN     "customization_level" TEXT,
ADD COLUMN     "demo_available" BOOLEAN,
ADD COLUMN     "deployment_options" JSONB,
ADD COLUMN     "frameworks" JSONB,
ADD COLUMN     "has_live_chat" BOOLEAN DEFAULT false,
ADD COLUMN     "libraries" JSONB,
ADD COLUMN     "mobile_support" BOOLEAN,
ADD COLUMN     "open_source" BOOLEAN,
ADD COLUMN     "programming_languages" JSONB,
ADD COLUMN     "support_channels" JSONB,
ADD COLUMN     "support_email" TEXT,
ADD COLUMN     "supported_os" JSONB,
ADD COLUMN     "trial_available" BOOLEAN;
