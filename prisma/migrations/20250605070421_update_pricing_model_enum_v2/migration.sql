/*
  Warnings:

  - The values [PA<PERSON>,ONE_TIME,<PERSON><PERSON>_BASED,OTHER] on the enum `PricingModel` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "public"."PricingModel_new" AS ENUM ('FREE', 'FREEMIUM', 'SUBSCRIPTION', 'PAY_PER_USE', 'ONE_TIME_PURCHASE', 'CONTACT_SALES', 'OPEN_SOURCE');
ALTER TABLE "public"."entity_details_tool" ALTER COLUMN "pricing_model" TYPE "public"."PricingModel_new" USING ("pricing_model"::text::"public"."PricingModel_new");
ALTER TABLE "public"."entity_details_software" ALTER COLUMN "pricing_model" TYPE "public"."PricingModel_new" USING ("pricing_model"::text::"public"."PricingModel_new");
ALTER TYPE "public"."PricingModel" RENAME TO "PricingModel_old";
ALTER TYPE "public"."PricingModel_new" RENAME TO "PricingModel";
DROP TYPE "public"."PricingModel_old";
COMMIT;
