/*
  Warnings:

  - You are about to drop the column `criteria_details` on the `badge_types` table. All the data in the column will be lost.
  - You are about to drop the column `is_auto_granted` on the `badge_types` table. All the data in the column will be lost.
  - You are about to drop the column `is_manual_granted` on the `badge_types` table. All the data in the column will be lost.
  - You are about to drop the column `slug` on the `badge_types` table. All the data in the column will be lost.
  - You are about to drop the column `parent_category_id` on the `categories` table. All the data in the column will be lost.
  - The `employee_count_range` column on the `entities` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `funding_stage` column on the `entities` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `granted_by_user_id` on the `entity_badges` table. All the data in the column will be lost.
  - The primary key for the `entity_details_agency` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `entity_details_book` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `authorN<PERSON>s` on the `entity_details_book` table. All the data in the column will be lost.
  - You are about to drop the column `publication_year` on the `entity_details_book` table. All the data in the column will be lost.
  - The primary key for the `entity_details_bounty` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `bounty_issuer` on the `entity_details_bounty` table. All the data in the column will be lost.
  - You are about to drop the column `difficulty_level` on the `entity_details_bounty` table. All the data in the column will be lost.
  - You are about to drop the column `platform_url` on the `entity_details_bounty` table. All the data in the column will be lost.
  - You are about to drop the column `requirements` on the `entity_details_bounty` table. All the data in the column will be lost.
  - You are about to drop the column `reward_amount` on the `entity_details_bounty` table. All the data in the column will be lost.
  - You are about to drop the column `submission_deadline` on the `entity_details_bounty` table. All the data in the column will be lost.
  - The primary key for the `entity_details_community` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `entity_details_content_creator` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `entity_details_course` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `entity_details_dataset` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `entity_details_event` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `agenda_url` on the `entity_details_event` table. All the data in the column will be lost.
  - You are about to drop the column `eventType` on the `entity_details_event` table. All the data in the column will be lost.
  - You are about to drop the column `speakerList` on the `entity_details_event` table. All the data in the column will be lost.
  - The primary key for the `entity_details_grant` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `application_deadline` on the `entity_details_grant` table. All the data in the column will be lost.
  - You are about to drop the column `eligibility_criteria` on the `entity_details_grant` table. All the data in the column will be lost.
  - You are about to drop the column `funding_amount` on the `entity_details_grant` table. All the data in the column will be lost.
  - You are about to drop the column `grant_focus_area` on the `entity_details_grant` table. All the data in the column will be lost.
  - You are about to drop the column `granting_institution` on the `entity_details_grant` table. All the data in the column will be lost.
  - The primary key for the `entity_details_hardware` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `datasheet_url` on the `entity_details_hardware` table. All the data in the column will be lost.
  - You are about to drop the column `hardware_type` on the `entity_details_hardware` table. All the data in the column will be lost.
  - You are about to drop the column `manufacturer` on the `entity_details_hardware` table. All the data in the column will be lost.
  - You are about to drop the column `price_range` on the `entity_details_hardware` table. All the data in the column will be lost.
  - You are about to drop the column `release_date` on the `entity_details_hardware` table. All the data in the column will be lost.
  - You are about to drop the column `specifications` on the `entity_details_hardware` table. All the data in the column will be lost.
  - The primary key for the `entity_details_investor` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `investmentFocusAreas` on the `entity_details_investor` table. All the data in the column will be lost.
  - You are about to drop the column `investmentStages` on the `entity_details_investor` table. All the data in the column will be lost.
  - You are about to drop the column `portfolio_url` on the `entity_details_investor` table. All the data in the column will be lost.
  - You are about to drop the column `preferred_communication` on the `entity_details_investor` table. All the data in the column will be lost.
  - You are about to drop the column `typical_investment_size` on the `entity_details_investor` table. All the data in the column will be lost.
  - The primary key for the `entity_details_job` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `employment_type` on the `entity_details_job` table. All the data in the column will be lost.
  - You are about to drop the column `job_description` on the `entity_details_job` table. All the data in the column will be lost.
  - You are about to drop the column `job_title` on the `entity_details_job` table. All the data in the column will be lost.
  - You are about to drop the column `location_type` on the `entity_details_job` table. All the data in the column will be lost.
  - You are about to drop the column `salary_range` on the `entity_details_job` table. All the data in the column will be lost.
  - The primary key for the `entity_details_model` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `model_url` on the `entity_details_model` table. All the data in the column will be lost.
  - You are about to drop the column `parameters_count` on the `entity_details_model` table. All the data in the column will be lost.
  - You are about to drop the column `performanceMetrics` on the `entity_details_model` table. All the data in the column will be lost.
  - The primary key for the `entity_details_news` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `entity_details_newsletter` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `entity_details_platform` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `sla_url` on the `entity_details_platform` table. All the data in the column will be lost.
  - You are about to drop the column `supported_regions` on the `entity_details_platform` table. All the data in the column will be lost.
  - The `integrations` column on the `entity_details_platform` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `key_services` column on the `entity_details_platform` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `use_cases` column on the `entity_details_platform` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The primary key for the `entity_details_podcast` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `average_episode_length` on the `entity_details_podcast` table. All the data in the column will be lost.
  - You are about to drop the column `hostNames` on the `entity_details_podcast` table. All the data in the column will be lost.
  - You are about to drop the column `listen_url` on the `entity_details_podcast` table. All the data in the column will be lost.
  - You are about to drop the column `mainTopics` on the `entity_details_podcast` table. All the data in the column will be lost.
  - You are about to drop the column `primary_language` on the `entity_details_podcast` table. All the data in the column will be lost.
  - The primary key for the `entity_details_project_reference` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `live_demo_url` on the `entity_details_project_reference` table. All the data in the column will be lost.
  - You are about to drop the column `project_goals` on the `entity_details_project_reference` table. All the data in the column will be lost.
  - You are about to drop the column `project_status` on the `entity_details_project_reference` table. All the data in the column will be lost.
  - You are about to drop the column `source_code_url` on the `entity_details_project_reference` table. All the data in the column will be lost.
  - You are about to drop the column `technologies` on the `entity_details_project_reference` table. All the data in the column will be lost.
  - The `contributors` column on the `entity_details_project_reference` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The primary key for the `entity_details_research_paper` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `publication_url` on the `entity_details_research_paper` table. All the data in the column will be lost.
  - The `authors` column on the `entity_details_research_paper` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The primary key for the `entity_details_service_provider` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `case_studies_url` on the `entity_details_service_provider` table. All the data in the column will be lost.
  - You are about to drop the column `consultation_booking_url` on the `entity_details_service_provider` table. All the data in the column will be lost.
  - You are about to drop the column `hourly_rate_range` on the `entity_details_service_provider` table. All the data in the column will be lost.
  - You are about to drop the column `industrySpecializations` on the `entity_details_service_provider` table. All the data in the column will be lost.
  - You are about to drop the column `serviceAreas` on the `entity_details_service_provider` table. All the data in the column will be lost.
  - The primary key for the `entity_details_software` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `release_date` on the `entity_details_software` table. All the data in the column will be lost.
  - You are about to drop the column `repository_url` on the `entity_details_software` table. All the data in the column will be lost.
  - The `integrations` column on the `entity_details_software` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `platform_compatibility` column on the `entity_details_software` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `programming_languages` column on the `entity_details_software` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `use_cases` column on the `entity_details_software` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The primary key for the `entity_details_tool` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `entity_features` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `assigned_at` on the `entity_features` table. All the data in the column will be lost.
  - The primary key for the `entity_tags` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `icon_url` on the `features` table. All the data in the column will be lost.
  - The primary key for the `review_votes` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `vote` on the `review_votes` table. All the data in the column will be lost.
  - You are about to drop the column `helpfulness_score` on the `reviews` table. All the data in the column will be lost.
  - You are about to drop the column `moderated_at` on the `reviews` table. All the data in the column will be lost.
  - You are about to drop the column `moderator_user_id` on the `reviews` table. All the data in the column will be lost.
  - You are about to drop the column `review_text` on the `reviews` table. All the data in the column will be lost.
  - You are about to drop the column `icon_url` on the `tags` table. All the data in the column will be lost.
  - You are about to drop the column `timestamp` on the `user_activity_logs` table. All the data in the column will be lost.
  - You are about to drop the column `granted_by_user_id` on the `user_badges` table. All the data in the column will be lost.
  - You are about to drop the column `created_at` on the `user_followed_categories` table. All the data in the column will be lost.
  - The primary key for the `user_followed_tags` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `user_notification_settings` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `email_new_entity_in_followed_category` on the `user_notification_settings` table. All the data in the column will be lost.
  - You are about to drop the column `email_new_entity_in_followed_tag` on the `user_notification_settings` table. All the data in the column will be lost.
  - You are about to drop the column `email_new_review_on_saved_entity` on the `user_notification_settings` table. All the data in the column will be lost.
  - You are about to drop the column `email_updates_on_saved_entity` on the `user_notification_settings` table. All the data in the column will be lost.
  - The primary key for the `user_saved_entities` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - A unique constraint covering the columns `[entity_id,badge_type_id]` on the table `entity_badges` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_agency` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_book` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_bounty` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_community` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_content_creator` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_course` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_dataset` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_event` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_grant` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_hardware` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_investor` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_job` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_model` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_news` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_newsletter` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_platform` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_podcast` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_project_reference` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_research_paper` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_service_provider` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_software` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id]` on the table `entity_details_tool` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id,feature_id]` on the table `entity_features` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id,tag_id]` on the table `entity_tags` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[review_id,user_id]` on the table `review_votes` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[entity_id,user_id]` on the table `reviews` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[user_id,badge_type_id]` on the table `user_badges` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[user_id,tag_id]` on the table `user_followed_tags` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[user_id]` on the table `user_notification_settings` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[user_id,entity_id]` on the table `user_saved_entities` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `assigned_by` to the `entity_categories` table without a default value. This is not possible if the table is not empty.
  - The required column `id` was added to the `entity_details_agency` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_book` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_bounty` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_community` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_content_creator` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_course` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_dataset` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_event` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_grant` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_hardware` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_investor` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_job` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_model` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_news` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_newsletter` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_platform` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_podcast` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_project_reference` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_research_paper` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_service_provider` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_software` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `entity_details_tool` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - Added the required column `assigned_by` to the `entity_features` table without a default value. This is not possible if the table is not empty.
  - The required column `id` was added to the `entity_features` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - Added the required column `assigned_by` to the `entity_tags` table without a default value. This is not possible if the table is not empty.
  - The required column `id` was added to the `entity_tags` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `review_votes` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - Added the required column `is_upvote` to the `review_votes` table without a default value. This is not possible if the table is not empty.
  - The required column `id` was added to the `user_followed_tags` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `user_notification_settings` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.
  - The required column `id` was added to the `user_saved_entities` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.

*/
-- CreateEnum
CREATE TYPE "public"."EmployeeCountRange" AS ENUM ('C1_10', 'C11_50', 'C51_200', 'C201_500', 'C501_1000', 'C1001_5000', 'C5001_PLUS');

-- CreateEnum
CREATE TYPE "public"."FundingStage" AS ENUM ('SEED', 'PRE_SEED', 'SERIES_A', 'SERIES_B', 'SERIES_C', 'SERIES_D_PLUS', 'PUBLIC');

-- DropForeignKey
ALTER TABLE "public"."categories" DROP CONSTRAINT "fk_category_parent_category_id";

-- DropForeignKey
ALTER TABLE "public"."entity_badges" DROP CONSTRAINT "entity_badges_granted_by_user_id_fkey";

-- DropForeignKey
ALTER TABLE "public"."reviews" DROP CONSTRAINT "fk_review_moderator_id";

-- DropForeignKey
ALTER TABLE "public"."user_badges" DROP CONSTRAINT "user_badges_granted_by_user_id_fkey";

-- DropIndex
DROP INDEX "public"."badge_types_slug_key";

-- DropIndex
DROP INDEX "public"."categories_parent_category_id_idx";

-- DropIndex
DROP INDEX "public"."entities_created_at_idx";

-- DropIndex
DROP INDEX "public"."entities_ftsDocument_idx";

-- DropIndex
DROP INDEX "public"."entities_name_idx";

-- DropIndex
DROP INDEX "public"."entities_status_idx";

-- DropIndex
DROP INDEX "public"."entities_submitter_id_idx";

-- DropIndex
DROP INDEX "public"."entities_updated_at_idx";

-- DropIndex
DROP INDEX "public"."idx_entity_entity_type_id";

-- DropIndex
DROP INDEX "public"."entity_badges_badge_type_id_idx";

-- DropIndex
DROP INDEX "public"."entity_badges_entity_id_idx";

-- DropIndex
DROP INDEX "public"."entity_badges_granted_by_user_id_idx";

-- DropIndex
DROP INDEX "public"."idx_entity_category_category_id";

-- DropIndex
DROP INDEX "public"."idx_entity_category_entity_id";

-- DropIndex
DROP INDEX "public"."entity_details_book_isbn_key";

-- DropIndex
DROP INDEX "public"."entity_details_research_paper_doi_key";

-- DropIndex
DROP INDEX "public"."idx_entity_feature_entity_id";

-- DropIndex
DROP INDEX "public"."idx_entity_feature_feature_id";

-- DropIndex
DROP INDEX "public"."reviews_entity_id_idx";

-- DropIndex
DROP INDEX "public"."reviews_moderator_user_id_idx";

-- DropIndex
DROP INDEX "public"."reviews_user_id_entity_id_key";

-- DropIndex
DROP INDEX "public"."reviews_user_id_idx";

-- DropIndex
DROP INDEX "public"."user_activity_logs_action_type_idx";

-- DropIndex
DROP INDEX "public"."user_activity_logs_category_id_idx";

-- DropIndex
DROP INDEX "public"."user_activity_logs_entity_id_idx";

-- DropIndex
DROP INDEX "public"."user_activity_logs_review_id_idx";

-- DropIndex
DROP INDEX "public"."user_activity_logs_tag_id_idx";

-- DropIndex
DROP INDEX "public"."user_activity_logs_target_user_id_idx";

-- DropIndex
DROP INDEX "public"."user_activity_logs_user_id_idx";

-- DropIndex
DROP INDEX "public"."user_badges_badge_type_id_idx";

-- DropIndex
DROP INDEX "public"."user_badges_granted_by_user_id_idx";

-- DropIndex
DROP INDEX "public"."user_badges_user_id_idx";

-- AlterTable
ALTER TABLE "public"."badge_types" DROP COLUMN "criteria_details",
DROP COLUMN "is_auto_granted",
DROP COLUMN "is_manual_granted",
DROP COLUMN "slug",
ADD COLUMN     "criteria" JSONB,
ALTER COLUMN "id" DROP DEFAULT,
ALTER COLUMN "description" DROP NOT NULL,
ALTER COLUMN "icon_url" DROP NOT NULL;

-- AlterTable
ALTER TABLE "public"."categories" DROP COLUMN "parent_category_id",
ADD COLUMN     "parent_id" UUID,
ALTER COLUMN "id" DROP DEFAULT;

-- AlterTable
ALTER TABLE "public"."entities" DROP COLUMN "employee_count_range",
ADD COLUMN     "employee_count_range" "public"."EmployeeCountRange",
DROP COLUMN "funding_stage",
ADD COLUMN     "funding_stage" "public"."FundingStage",
ALTER COLUMN "scraped_review_count" DROP DEFAULT;

-- AlterTable
ALTER TABLE "public"."entity_badges" DROP COLUMN "granted_by_user_id",
ADD COLUMN     "expires_at" TIMESTAMP(3),
ADD COLUMN     "granted_by" UUID,
ALTER COLUMN "id" DROP DEFAULT;

-- AlterTable
ALTER TABLE "public"."entity_categories" ADD COLUMN     "assigned_by" UUID NOT NULL;

-- AlterTable
ALTER TABLE "public"."entity_details_agency" DROP CONSTRAINT "entity_details_agency_pkey",
ADD COLUMN     "id" UUID NOT NULL,
ADD CONSTRAINT "entity_details_agency_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_book" DROP CONSTRAINT "entity_details_book_pkey",
DROP COLUMN "authorNames",
DROP COLUMN "publication_year",
ADD COLUMN     "author" TEXT,
ADD COLUMN     "format" TEXT,
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "publication_date" TIMESTAMP(3),
ADD CONSTRAINT "entity_details_book_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_bounty" DROP CONSTRAINT "entity_details_bounty_pkey",
DROP COLUMN "bounty_issuer",
DROP COLUMN "difficulty_level",
DROP COLUMN "platform_url",
DROP COLUMN "requirements",
DROP COLUMN "reward_amount",
DROP COLUMN "submission_deadline",
ADD COLUMN     "amount" TEXT,
ADD COLUMN     "deadline" TIMESTAMP(3),
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "platform" TEXT,
ADD COLUMN     "required_skills" TEXT[],
ADD COLUMN     "status" TEXT,
ADD COLUMN     "task_description" TEXT,
ADD COLUMN     "url" TEXT,
ADD CONSTRAINT "entity_details_bounty_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_community" DROP CONSTRAINT "entity_details_community_pkey",
ADD COLUMN     "id" UUID NOT NULL,
ALTER COLUMN "member_count" DROP DEFAULT,
ADD CONSTRAINT "entity_details_community_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_content_creator" DROP CONSTRAINT "entity_details_content_creator_pkey",
ADD COLUMN     "id" UUID NOT NULL,
ALTER COLUMN "follower_count" DROP DEFAULT,
ADD CONSTRAINT "entity_details_content_creator_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_course" DROP CONSTRAINT "entity_details_course_pkey",
ADD COLUMN     "id" UUID NOT NULL,
ALTER COLUMN "enrollment_count" DROP DEFAULT,
ADD CONSTRAINT "entity_details_course_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_dataset" DROP CONSTRAINT "entity_details_dataset_pkey",
ADD COLUMN     "collection_method" TEXT,
ADD COLUMN     "data_collection_method" TEXT,
ADD COLUMN     "data_formats" TEXT[],
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "update_frequency" TEXT,
ADD CONSTRAINT "entity_details_dataset_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_event" DROP CONSTRAINT "entity_details_event_pkey",
DROP COLUMN "agenda_url",
DROP COLUMN "eventType",
DROP COLUMN "speakerList",
ADD COLUMN     "event_type" TEXT,
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "is_online" BOOLEAN,
ADD COLUMN     "key_speakers" TEXT[],
ADD COLUMN     "target_audience" TEXT[],
ADD COLUMN     "topics" TEXT[],
ADD CONSTRAINT "entity_details_event_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_grant" DROP CONSTRAINT "entity_details_grant_pkey",
DROP COLUMN "application_deadline",
DROP COLUMN "eligibility_criteria",
DROP COLUMN "funding_amount",
DROP COLUMN "grant_focus_area",
DROP COLUMN "granting_institution",
ADD COLUMN     "amount" TEXT,
ADD COLUMN     "deadline" TIMESTAMP(3),
ADD COLUMN     "eligibility" TEXT,
ADD COLUMN     "focus_areas" TEXT[],
ADD COLUMN     "funder_name" TEXT,
ADD COLUMN     "grant_type" TEXT,
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "location" TEXT,
ADD CONSTRAINT "entity_details_grant_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_hardware" DROP CONSTRAINT "entity_details_hardware_pkey",
DROP COLUMN "datasheet_url",
DROP COLUMN "hardware_type",
DROP COLUMN "manufacturer",
DROP COLUMN "price_range",
DROP COLUMN "release_date",
DROP COLUMN "specifications",
ADD COLUMN     "availability" TEXT,
ADD COLUMN     "gpu" TEXT,
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "memory" TEXT,
ADD COLUMN     "power_consumption" TEXT,
ADD COLUMN     "price" TEXT,
ADD COLUMN     "processor" TEXT,
ADD COLUMN     "storage" TEXT,
ADD COLUMN     "use_cases" TEXT[],
ADD CONSTRAINT "entity_details_hardware_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_investor" DROP CONSTRAINT "entity_details_investor_pkey",
DROP COLUMN "investmentFocusAreas",
DROP COLUMN "investmentStages",
DROP COLUMN "portfolio_url",
DROP COLUMN "preferred_communication",
DROP COLUMN "typical_investment_size",
ADD COLUMN     "application_url" TEXT,
ADD COLUMN     "focus_areas" TEXT[],
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "investment_stages" TEXT[],
ADD COLUMN     "investor_type" TEXT,
ADD COLUMN     "location_summary" TEXT,
ADD COLUMN     "notable_investments" TEXT[],
ADD COLUMN     "portfolio_size" INTEGER,
ADD CONSTRAINT "entity_details_investor_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_job" DROP CONSTRAINT "entity_details_job_pkey",
DROP COLUMN "employment_type",
DROP COLUMN "job_description",
DROP COLUMN "job_title",
DROP COLUMN "location_type",
DROP COLUMN "salary_range",
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "is_remote" BOOLEAN,
ADD COLUMN     "job_type" TEXT,
ADD COLUMN     "key_responsibilities" TEXT[],
ADD COLUMN     "location" TEXT,
ADD COLUMN     "required_skills" TEXT[],
ADD COLUMN     "salary_max" DOUBLE PRECISION,
ADD COLUMN     "salary_min" DOUBLE PRECISION,
ADD CONSTRAINT "entity_details_job_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_model" DROP CONSTRAINT "entity_details_model_pkey",
DROP COLUMN "model_url",
DROP COLUMN "parameters_count",
DROP COLUMN "performanceMetrics",
ADD COLUMN     "deployment_options" TEXT[],
ADD COLUMN     "frameworks" TEXT[],
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "input_data_types" TEXT[],
ADD COLUMN     "libraries" TEXT[],
ADD COLUMN     "output_data_types" TEXT[],
ADD COLUMN     "performance_metrics" JSONB,
ADD COLUMN     "target_audience" TEXT[],
ADD COLUMN     "use_cases" TEXT[],
ADD CONSTRAINT "entity_details_model_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_news" DROP CONSTRAINT "entity_details_news_pkey",
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "tags" TEXT[],
ALTER COLUMN "publication_date" SET DATA TYPE TIMESTAMP(3),
ADD CONSTRAINT "entity_details_news_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_newsletter" DROP CONSTRAINT "entity_details_newsletter_pkey",
ADD COLUMN     "id" UUID NOT NULL,
ADD CONSTRAINT "entity_details_newsletter_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_platform" DROP CONSTRAINT "entity_details_platform_pkey",
DROP COLUMN "sla_url",
DROP COLUMN "supported_regions",
ADD COLUMN     "api_access" BOOLEAN,
ADD COLUMN     "customization_level" TEXT,
ADD COLUMN     "demo_available" BOOLEAN,
ADD COLUMN     "deployment_options" TEXT[],
ADD COLUMN     "has_api" BOOLEAN,
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "mobile_support" BOOLEAN,
ADD COLUMN     "open_source" BOOLEAN,
ADD COLUMN     "support_channels" TEXT[],
ADD COLUMN     "supported_os" TEXT[],
ADD COLUMN     "target_audience" TEXT[],
ADD COLUMN     "trial_available" BOOLEAN,
ALTER COLUMN "has_free_tier" DROP DEFAULT,
ALTER COLUMN "has_live_chat" DROP DEFAULT,
DROP COLUMN "integrations",
ADD COLUMN     "integrations" TEXT[],
DROP COLUMN "key_services",
ADD COLUMN     "key_services" TEXT[],
DROP COLUMN "use_cases",
ADD COLUMN     "use_cases" TEXT[],
ADD CONSTRAINT "entity_details_platform_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_podcast" DROP CONSTRAINT "entity_details_podcast_pkey",
DROP COLUMN "average_episode_length",
DROP COLUMN "hostNames",
DROP COLUMN "listen_url",
DROP COLUMN "mainTopics",
DROP COLUMN "primary_language",
ADD COLUMN     "apple_podcasts_url" TEXT,
ADD COLUMN     "average_length" TEXT,
ADD COLUMN     "google_podcasts_url" TEXT,
ADD COLUMN     "host" TEXT,
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "main_topics" TEXT[],
ADD COLUMN     "spotify_url" TEXT,
ADD COLUMN     "youtube_url" TEXT,
ADD CONSTRAINT "entity_details_podcast_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_project_reference" DROP CONSTRAINT "entity_details_project_reference_pkey",
DROP COLUMN "live_demo_url",
DROP COLUMN "project_goals",
DROP COLUMN "project_status",
DROP COLUMN "source_code_url",
DROP COLUMN "technologies",
ADD COLUMN     "forks" INTEGER,
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "key_technologies" TEXT[],
ADD COLUMN     "license" TEXT,
ADD COLUMN     "repository_url" TEXT,
ADD COLUMN     "stars" INTEGER,
ADD COLUMN     "status" TEXT,
ADD COLUMN     "use_cases" TEXT[],
DROP COLUMN "contributors",
ADD COLUMN     "contributors" INTEGER,
ADD CONSTRAINT "entity_details_project_reference_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_research_paper" DROP CONSTRAINT "entity_details_research_paper_pkey",
DROP COLUMN "publication_url",
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "pdf_url" TEXT,
DROP COLUMN "authors",
ADD COLUMN     "authors" TEXT[],
ALTER COLUMN "citation_count" DROP DEFAULT,
ALTER COLUMN "publication_date" SET DATA TYPE TIMESTAMP(3),
ADD CONSTRAINT "entity_details_research_paper_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_service_provider" DROP CONSTRAINT "entity_details_service_provider_pkey",
DROP COLUMN "case_studies_url",
DROP COLUMN "consultation_booking_url",
DROP COLUMN "hourly_rate_range",
DROP COLUMN "industrySpecializations",
DROP COLUMN "serviceAreas",
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "industry_specializations" TEXT[],
ADD COLUMN     "location_summary" TEXT,
ADD COLUMN     "portfolio_url" TEXT,
ADD COLUMN     "pricing_info" TEXT,
ADD COLUMN     "services_offered" TEXT[],
ADD COLUMN     "target_audience" TEXT[],
ADD CONSTRAINT "entity_details_service_provider_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_software" DROP CONSTRAINT "entity_details_software_pkey",
DROP COLUMN "release_date",
DROP COLUMN "repository_url",
ADD COLUMN     "api_access" BOOLEAN,
ADD COLUMN     "customization_level" TEXT,
ADD COLUMN     "demo_available" BOOLEAN,
ADD COLUMN     "deployment_options" TEXT[],
ADD COLUMN     "frameworks" TEXT[],
ADD COLUMN     "has_api" BOOLEAN,
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "key_features" TEXT[],
ADD COLUMN     "libraries" TEXT[],
ADD COLUMN     "mobile_support" BOOLEAN,
ADD COLUMN     "open_source" BOOLEAN,
ADD COLUMN     "support_channels" TEXT[],
ADD COLUMN     "supported_os" TEXT[],
ADD COLUMN     "target_audience" TEXT[],
ADD COLUMN     "trial_available" BOOLEAN,
ALTER COLUMN "has_free_tier" DROP NOT NULL,
ALTER COLUMN "has_free_tier" DROP DEFAULT,
ALTER COLUMN "has_live_chat" DROP DEFAULT,
DROP COLUMN "integrations",
ADD COLUMN     "integrations" TEXT[],
DROP COLUMN "platform_compatibility",
ADD COLUMN     "platform_compatibility" TEXT[],
DROP COLUMN "programming_languages",
ADD COLUMN     "programming_languages" TEXT[],
DROP COLUMN "use_cases",
ADD COLUMN     "use_cases" TEXT[],
ADD CONSTRAINT "entity_details_software_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_details_tool" DROP CONSTRAINT "entity_details_tool_pkey",
ADD COLUMN     "has_api" BOOLEAN,
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "platforms" JSONB,
ADD COLUMN     "technical_level" "public"."TechnicalLevel",
ALTER COLUMN "has_free_tier" DROP NOT NULL,
ALTER COLUMN "has_free_tier" DROP DEFAULT,
ALTER COLUMN "has_live_chat" DROP DEFAULT,
ADD CONSTRAINT "entity_details_tool_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_features" DROP CONSTRAINT "pk_entity_feature",
DROP COLUMN "assigned_at",
ADD COLUMN     "assigned_by" UUID NOT NULL,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "id" UUID NOT NULL,
ADD CONSTRAINT "entity_features_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."entity_tags" DROP CONSTRAINT "entity_tags_pkey",
ADD COLUMN     "assigned_by" UUID NOT NULL,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "id" UUID NOT NULL,
ADD CONSTRAINT "entity_tags_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."features" DROP COLUMN "icon_url",
ALTER COLUMN "id" DROP DEFAULT;

-- AlterTable
ALTER TABLE "public"."review_votes" DROP CONSTRAINT "review_votes_pkey",
DROP COLUMN "vote",
ADD COLUMN     "id" UUID NOT NULL,
ADD COLUMN     "is_upvote" BOOLEAN NOT NULL,
ADD CONSTRAINT "review_votes_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."reviews" DROP COLUMN "helpfulness_score",
DROP COLUMN "moderated_at",
DROP COLUMN "moderator_user_id",
DROP COLUMN "review_text",
ADD COLUMN     "content" TEXT,
ADD COLUMN     "downvotes" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "moderator_id" UUID,
ADD COLUMN     "upvotes" INTEGER NOT NULL DEFAULT 0,
ALTER COLUMN "id" DROP DEFAULT;

-- AlterTable
ALTER TABLE "public"."tags" DROP COLUMN "icon_url",
ALTER COLUMN "id" DROP DEFAULT;

-- AlterTable
ALTER TABLE "public"."user_activity_logs" DROP COLUMN "timestamp",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ALTER COLUMN "id" DROP DEFAULT;

-- AlterTable
ALTER TABLE "public"."user_badges" DROP COLUMN "granted_by_user_id",
ADD COLUMN     "granted_by" UUID,
ALTER COLUMN "id" DROP DEFAULT;

-- AlterTable
ALTER TABLE "public"."user_followed_categories" DROP COLUMN "created_at",
ADD COLUMN     "followed_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP;

-- AlterTable
ALTER TABLE "public"."user_followed_tags" DROP CONSTRAINT "user_followed_tags_pkey",
ADD COLUMN     "id" UUID NOT NULL,
ADD CONSTRAINT "user_followed_tags_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."user_notification_settings" DROP CONSTRAINT "user_notification_settings_pkey",
DROP COLUMN "email_new_entity_in_followed_category",
DROP COLUMN "email_new_entity_in_followed_tag",
DROP COLUMN "email_new_review_on_saved_entity",
DROP COLUMN "email_updates_on_saved_entity",
ADD COLUMN     "email_marketing" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "email_on_new_entity_in_followed" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "email_on_new_follower" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "email_on_new_review" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "email_on_review_response" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "id" UUID NOT NULL,
ADD CONSTRAINT "user_notification_settings_pkey" PRIMARY KEY ("id");

-- AlterTable
ALTER TABLE "public"."user_saved_entities" DROP CONSTRAINT "user_saved_entities_pkey",
ADD COLUMN     "id" UUID NOT NULL,
ADD CONSTRAINT "user_saved_entities_pkey" PRIMARY KEY ("id");

-- CreateIndex
CREATE UNIQUE INDEX "uq_entity_badge" ON "public"."entity_badges"("entity_id", "badge_type_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_agency_entity_id_key" ON "public"."entity_details_agency"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_book_entity_id_key" ON "public"."entity_details_book"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_bounty_entity_id_key" ON "public"."entity_details_bounty"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_community_entity_id_key" ON "public"."entity_details_community"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_content_creator_entity_id_key" ON "public"."entity_details_content_creator"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_course_entity_id_key" ON "public"."entity_details_course"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_dataset_entity_id_key" ON "public"."entity_details_dataset"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_event_entity_id_key" ON "public"."entity_details_event"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_grant_entity_id_key" ON "public"."entity_details_grant"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_hardware_entity_id_key" ON "public"."entity_details_hardware"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_investor_entity_id_key" ON "public"."entity_details_investor"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_job_entity_id_key" ON "public"."entity_details_job"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_model_entity_id_key" ON "public"."entity_details_model"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_news_entity_id_key" ON "public"."entity_details_news"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_newsletter_entity_id_key" ON "public"."entity_details_newsletter"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_platform_entity_id_key" ON "public"."entity_details_platform"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_podcast_entity_id_key" ON "public"."entity_details_podcast"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_project_reference_entity_id_key" ON "public"."entity_details_project_reference"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_research_paper_entity_id_key" ON "public"."entity_details_research_paper"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_service_provider_entity_id_key" ON "public"."entity_details_service_provider"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_software_entity_id_key" ON "public"."entity_details_software"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "entity_details_tool_entity_id_key" ON "public"."entity_details_tool"("entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "uq_entity_feature" ON "public"."entity_features"("entity_id", "feature_id");

-- CreateIndex
CREATE UNIQUE INDEX "uq_entity_tag" ON "public"."entity_tags"("entity_id", "tag_id");

-- CreateIndex
CREATE UNIQUE INDEX "uq_review_vote_user" ON "public"."review_votes"("review_id", "user_id");

-- CreateIndex
CREATE UNIQUE INDEX "uq_review_entity_user" ON "public"."reviews"("entity_id", "user_id");

-- CreateIndex
CREATE UNIQUE INDEX "uq_user_badge" ON "public"."user_badges"("user_id", "badge_type_id");

-- CreateIndex
CREATE UNIQUE INDEX "uq_user_followed_tag" ON "public"."user_followed_tags"("user_id", "tag_id");

-- CreateIndex
CREATE UNIQUE INDEX "user_notification_settings_user_id_key" ON "public"."user_notification_settings"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "uq_user_saved_entity" ON "public"."user_saved_entities"("user_id", "entity_id");

-- RenameForeignKey
ALTER TABLE "public"."entities" RENAME CONSTRAINT "fk_entity_entity_type_id" TO "entities_entity_type_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entities" RENAME CONSTRAINT "fk_entity_submitter_id" TO "entities_submitter_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_categories" RENAME CONSTRAINT "fk_entity_category_category_id" TO "entity_categories_category_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_categories" RENAME CONSTRAINT "fk_entity_category_entity_id" TO "entity_categories_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_agency" RENAME CONSTRAINT "fk_entity_details_agency_entity_id" TO "entity_details_agency_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_book" RENAME CONSTRAINT "fk_entity_details_book_entity_id" TO "entity_details_book_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_bounty" RENAME CONSTRAINT "fk_entity_details_bounty_entity_id" TO "entity_details_bounty_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_community" RENAME CONSTRAINT "fk_entity_details_community_entity_id" TO "entity_details_community_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_content_creator" RENAME CONSTRAINT "fk_entity_details_content_creator_entity_id" TO "entity_details_content_creator_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_course" RENAME CONSTRAINT "fk_entity_details_course_entity_id" TO "entity_details_course_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_dataset" RENAME CONSTRAINT "fk_entity_details_dataset_entity_id" TO "entity_details_dataset_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_event" RENAME CONSTRAINT "fk_entity_details_event_entity_id" TO "entity_details_event_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_grant" RENAME CONSTRAINT "fk_entity_details_grant_entity_id" TO "entity_details_grant_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_hardware" RENAME CONSTRAINT "fk_entity_details_hardware_entity_id" TO "entity_details_hardware_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_investor" RENAME CONSTRAINT "fk_entity_details_investor_entity_id" TO "entity_details_investor_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_job" RENAME CONSTRAINT "fk_entity_details_job_entity_id" TO "entity_details_job_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_model" RENAME CONSTRAINT "fk_entity_details_model_entity_id" TO "entity_details_model_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_news" RENAME CONSTRAINT "fk_entity_details_news_entity_id" TO "entity_details_news_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_newsletter" RENAME CONSTRAINT "fk_entity_details_newsletter_entity_id" TO "entity_details_newsletter_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_platform" RENAME CONSTRAINT "fk_entity_details_platform_entity_id" TO "entity_details_platform_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_podcast" RENAME CONSTRAINT "fk_entity_details_podcast_entity_id" TO "entity_details_podcast_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_project_reference" RENAME CONSTRAINT "fk_entity_details_project_reference_entity_id" TO "entity_details_project_reference_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_research_paper" RENAME CONSTRAINT "fk_entity_details_research_paper_entity_id" TO "entity_details_research_paper_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_service_provider" RENAME CONSTRAINT "fk_entity_details_service_provider_entity_id" TO "entity_details_service_provider_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_software" RENAME CONSTRAINT "fk_entity_details_software_entity_id" TO "entity_details_software_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_details_tool" RENAME CONSTRAINT "fk_entity_details_tool_entity_id" TO "entity_details_tool_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_features" RENAME CONSTRAINT "fk_entity_feature_entity_id" TO "entity_features_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_features" RENAME CONSTRAINT "fk_entity_feature_feature_id" TO "entity_features_feature_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_tags" RENAME CONSTRAINT "fk_entity_tag_entity_id" TO "entity_tags_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."entity_tags" RENAME CONSTRAINT "fk_entity_tag_tag_id" TO "entity_tags_tag_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."reviews" RENAME CONSTRAINT "fk_review_entity_id" TO "reviews_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."reviews" RENAME CONSTRAINT "fk_review_user_id" TO "reviews_user_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."user_activity_logs" RENAME CONSTRAINT "fk_user_activity_log_category_id" TO "user_activity_logs_category_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."user_activity_logs" RENAME CONSTRAINT "fk_user_activity_log_entity_id" TO "user_activity_logs_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."user_activity_logs" RENAME CONSTRAINT "fk_user_activity_log_review_id" TO "user_activity_logs_review_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."user_activity_logs" RENAME CONSTRAINT "fk_user_activity_log_tag_id" TO "user_activity_logs_tag_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."user_activity_logs" RENAME CONSTRAINT "fk_user_activity_log_target_user_id" TO "user_activity_logs_target_user_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."user_activity_logs" RENAME CONSTRAINT "fk_user_activity_log_user_id" TO "user_activity_logs_user_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."user_saved_entities" RENAME CONSTRAINT "fk_user_saved_entity_entity_id" TO "user_saved_entities_entity_id_fkey";

-- RenameForeignKey
ALTER TABLE "public"."user_saved_entities" RENAME CONSTRAINT "fk_user_saved_entity_user_id" TO "user_saved_entities_user_id_fkey";

-- AddForeignKey
ALTER TABLE "public"."categories" ADD CONSTRAINT "categories_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "public"."categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."reviews" ADD CONSTRAINT "reviews_moderator_id_fkey" FOREIGN KEY ("moderator_id") REFERENCES "public"."users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."user_badges" ADD CONSTRAINT "user_badges_granted_by_fkey" FOREIGN KEY ("granted_by") REFERENCES "public"."users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."entity_badges" ADD CONSTRAINT "entity_badges_granted_by_fkey" FOREIGN KEY ("granted_by") REFERENCES "public"."users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
