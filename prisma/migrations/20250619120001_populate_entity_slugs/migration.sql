-- Populate slug field for existing entities
-- This migration generates slugs for all existing entities based on their names

-- Create a function to generate slugs (similar to the TypeScript generateSlug function)
CREATE OR REPLACE FUNCTION generate_slug(input_text TEXT) RETURNS TEXT AS $$
BEGIN
    IF input_text IS NULL OR input_text = '' THEN
        RETURN '';
    END IF;
    
    RETURN LOWER(
        REGEXP_REPLACE(
            REGEXP_REPLACE(
                REGEXP_REPLACE(
                    REGEXP_REPLACE(TRIM(input_text), '\s+', '-', 'g'),
                    '[^\w\-]+', '', 'g'
                ),
                '\-\-+', '-', 'g'
            ),
            '^-+|-+$', '', 'g'
        )
    );
END;
$$ LANGUAGE plpgsql;

-- Update existing entities with generated slugs
-- Handle potential duplicates by appending numbers
DO $$
DECLARE
    entity_record RECORD;
    base_slug TEXT;
    final_slug TEXT;
    counter INTEGER;
BEGIN
    FOR entity_record IN 
        SELECT id, name FROM "public"."entities" WHERE slug IS NULL ORDER BY created_at
    LOOP
        base_slug := generate_slug(entity_record.name);
        final_slug := base_slug;
        counter := 0;
        
        -- Check for duplicates and append counter if needed
        WHILE EXISTS (SELECT 1 FROM "public"."entities" WHERE slug = final_slug AND id != entity_record.id) LOOP
            counter := counter + 1;
            final_slug := base_slug || '-' || counter;
        END LOOP;
        
        -- Update the entity with the unique slug
        UPDATE "public"."entities" 
        SET slug = final_slug 
        WHERE id = entity_record.id;
    END LOOP;
END $$;

-- Drop the temporary function
DROP FUNCTION generate_slug(TEXT);
