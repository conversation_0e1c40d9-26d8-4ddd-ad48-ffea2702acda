-- Fix the FTS function to use correct field name 'format' instead of 'data_format'
-- This addresses the issue where the database function references a non-existent column

-- Drop and recreate the FTS function with the correct field reference
DROP FUNCTION IF EXISTS public.update_entity_fts_document() CASCADE;

CREATE OR REPLACE FUNCTION public.update_entity_fts_document()
RETURNS TRIGGER AS $$
DECLARE
    v_text_content TEXT;
    v_new_fts_document TSVECTOR;
BEGIN
    -- Aggregate all relevant text fields from the entity and its related detail tables
    SELECT 
        COALESCE(e.name, '') || ' ' ||
        COALESCE(e.short_description, '') || ' ' ||
        COALESCE(e.description, '') || ' ' ||
        COALESCE(e.meta_title, '') || ' ' ||
        COALESCE(e.meta_description, '') || ' ' ||

        COALESCE(d_tool.key_features::text, '') || ' ' || COALESCE(d_tool.use_cases::text, '') || ' ' || COALESCE(d_tool.target_audience::text, '') || ' ' ||
        COALESCE(d_course.prerequisites, '') || ' ' ||
        COALESCE(d_agency.services_offered::text, '') || ' ' || COALESCE(d_agency.industry_focus::text, '') || ' ' || COALESCE(d_agency.target_client_size::text, '') || ' ' || COALESCE(d_agency.pricing_info, '') || ' ' ||
        COALESCE(d_creator.focus_areas::text, '') || ' ' ||
        COALESCE(d_community.focus_topics::text, '') || ' ' ||
        COALESCE(d_newsletter.main_topics::text, '') || ' ' ||
        COALESCE(d_dataset.description, '') || ' ' || COALESCE(d_dataset.format, '') || ' ' || COALESCE(d_dataset.collection_method, '') || ' ' ||
        COALESCE(d_paper.abstract, '') || ' ' || COALESCE(d_paper.authors::text, '') || ' ' || COALESCE(d_paper.keywords::text, '') || ' ' ||
        COALESCE(d_software.features::text, '') || ' ' || COALESCE(d_software.use_cases::text, '') || ' ' || COALESCE(d_software.target_audience::text, '') || ' ' ||
        COALESCE(d_model.description, '') || ' ' || COALESCE(d_model.use_cases::text, '') || ' ' ||
        COALESCE(d_project.description, '') || ' ' || COALESCE(d_project.technologies_used::text, '') || ' ' ||
        COALESCE(d_service.services_offered::text, '') || ' ' || COALESCE(d_service.areas_of_expertise::text, '') || ' ' ||
        COALESCE(d_investor.investment_focus::text, '') || ' ' || COALESCE(d_investor.portfolio_highlights::text, '') || ' ' ||
        COALESCE(d_event.description, '') || ' ' || COALESCE(d_event.agenda_highlights::text, '') || ' ' ||
        COALESCE(d_job.description, '') || ' ' || COALESCE(d_job.responsibilities, '') || ' ' || COALESCE(d_job.qualifications, '') || ' ' ||
        COALESCE(d_grant.description, '') || ' ' || COALESCE(d_grant.eligibility_criteria, '') || ' ' ||
        COALESCE(d_bounty.description, '') || ' ' || COALESCE(d_bounty.requirements, '') || ' ' ||
        COALESCE(d_hardware.description, '') || ' ' || COALESCE(d_hardware.technical_specs::text, '') || ' ' ||
        COALESCE(d_news.content, '') || ' ' || COALESCE(d_news.summary, '') || ' ' ||
        COALESCE(d_book.summary, '') || ' ' || COALESCE(d_book.table_of_contents, '') || ' ' ||
        COALESCE(d_podcast.description, '') || ' ' || COALESCE(d_podcast.topics_covered::text, '') || ' ' ||
        COALESCE(d_platform.features::text, '') || ' ' || COALESCE(d_platform.use_cases::text, '') || ' ' || COALESCE(d_platform.target_audience::text, '')
    INTO v_text_content
    FROM "public"."entities" e
    LEFT JOIN "public"."entity_details_tool" d_tool ON e.id = d_tool.entity_id
    LEFT JOIN "public"."entity_details_course" d_course ON e.id = d_course.entity_id
    LEFT JOIN "public"."entity_details_agency" d_agency ON e.id = d_agency.entity_id
    LEFT JOIN "public"."entity_details_content_creator" d_creator ON e.id = d_creator.entity_id
    LEFT JOIN "public"."entity_details_community" d_community ON e.id = d_community.entity_id
    LEFT JOIN "public"."entity_details_newsletter" d_newsletter ON e.id = d_newsletter.entity_id
    LEFT JOIN "public"."entity_details_dataset" d_dataset ON e.id = d_dataset.entity_id
    LEFT JOIN "public"."entity_details_research_paper" d_paper ON e.id = d_paper.entity_id
    LEFT JOIN "public"."entity_details_software" d_software ON e.id = d_software.entity_id
    LEFT JOIN "public"."entity_details_model" d_model ON e.id = d_model.entity_id
    LEFT JOIN "public"."entity_details_project_reference" d_project ON e.id = d_project.entity_id
    LEFT JOIN "public"."entity_details_service_provider" d_service ON e.id = d_service.entity_id
    LEFT JOIN "public"."entity_details_investor" d_investor ON e.id = d_investor.entity_id
    LEFT JOIN "public"."entity_details_event" d_event ON e.id = d_event.entity_id
    LEFT JOIN "public"."entity_details_job" d_job ON e.id = d_job.entity_id
    LEFT JOIN "public"."entity_details_grant" d_grant ON e.id = d_grant.entity_id
    LEFT JOIN "public"."entity_details_bounty" d_bounty ON e.id = d_bounty.entity_id
    LEFT JOIN "public"."entity_details_hardware" d_hardware ON e.id = d_hardware.entity_id
    LEFT JOIN "public"."entity_details_news" d_news ON e.id = d_news.entity_id
    LEFT JOIN "public"."entity_details_book" d_book ON e.id = d_book.entity_id
    LEFT JOIN "public"."entity_details_podcast" d_podcast ON e.id = d_podcast.entity_id
    LEFT JOIN "public"."entity_details_platform" d_platform ON e.id = d_platform.entity_id
    WHERE e.id = NEW.id;

    -- Convert the aggregated text to a tsvector
    v_new_fts_document := to_tsvector('english', v_text_content);

    -- Update the ftsDocument column in the entities table
    NEW."ftsDocument" := v_new_fts_document;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger
CREATE TRIGGER tsvector_update_trigger
BEFORE INSERT OR UPDATE ON "public"."entities"
FOR EACH ROW EXECUTE FUNCTION public.update_entity_fts_document();
