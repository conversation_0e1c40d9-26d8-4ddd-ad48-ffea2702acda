import { PrismaClient, Prisma, EntityStatus, TechnicalLevel, PricingModel, SkillLevel, UserRole, UserStatus } from '../generated/prisma';
import type { EntityType, Category, Tag, Feature } from '../generated/prisma';

const prisma = new PrismaClient();

async function main() {
  console.log(`Start seeding ...`);

  // --- Upsert a default submitter user ---
  const defaultUserEmail = '<EMAIL>';
  const defaultAuthUserId = '00000000-0000-0000-0000-000000000000'; // A stable, placeholder UUID for the seed user

  const submitter = await prisma.user.upsert({
    where: { email: defaultUserEmail },
    update: {},
    create: {
      id: '00000000-0000-0000-0000-000000000001', // Stable UUID
      authUserId: defaultAuthUserId,
      email: defaultUserEmail,
      displayName: 'Seed Submitter',
      role: UserRole.ADMIN,
      status: UserStatus.ACTIVE,
    },
  });
  console.log(`Upserted submitter: ${submitter.email} (ID: ${submitter.id}, AuthUID: ${submitter.authUserId})`);

  // --- Seed App Settings ---
  console.log('Seeding App Settings...');

  // Create the app_settings table if it doesn't exist
  try {
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "public"."app_settings" (
        "key" TEXT NOT NULL,
        "value" TEXT NOT NULL,
        "description" TEXT,
        "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT "app_settings_pkey" PRIMARY KEY ("key")
      );
    `;
    console.log('App settings table created or already exists');
  } catch (error) {
    console.log('App settings table creation skipped (may already exist):', error.message);
  }

  // Upsert the LLM provider setting
  await prisma.appSetting.upsert({
    where: { key: 'CURRENT_LLM_PROVIDER' },
    update: {},
    create: {
      key: 'CURRENT_LLM_PROVIDER',
      value: 'OPENAI',
      description: 'The LLM provider to use for recommendations. Options: OPENAI, GOOGLE_GEMINI, ANTHROPIC'
    },
  });
  console.log('Upserted LLM provider setting: OPENAI');

  // --- Seed Entity Types ---
  console.log('Seeding EntityTypes...');
  const entityTypesData = [
    { id: '10000000-0000-0000-0000-000000000001', name: 'AI Tool', slug: 'ai-tool', description: 'Software, SaaS, APIs, Models, Libraries, Frameworks for AI.' },
    { id: '10000000-0000-0000-0000-000000000002', name: 'Course', slug: 'course', description: 'Educational courses, tutorials, and learning resources.' },
    { id: '10000000-0000-0000-0000-000000000003', name: 'Dataset', slug: 'dataset', description: 'Collections of data for training or analysis.' },
    { id: '10000000-0000-0000-0000-000000000004', name: 'Research Paper', slug: 'research-paper', description: 'Academic papers and research articles.' },
    { id: '10000000-0000-0000-0000-000000000005', name: 'Platform', slug: 'platform', description: 'Platforms providing AI services or infrastructure.' },
    { id: '10000000-0000-0000-0000-000000000006', name: 'Hardware', slug: 'hardware', description: 'Physical hardware for AI computation.' },
    { id: '10000000-0000-0000-0000-000000000007', name: 'Newsletter', slug: 'newsletter', description: 'Regular publications on AI topics.' },
    { id: '10000000-0000-0000-0000-000000000008', name: 'Community', slug: 'community', description: 'Online or offline AI communities.' },
    { id: '10000000-0000-0000-0000-000000000009', name: 'Event', slug: 'event', description: 'Conferences, workshops, and meetups.' },
  ];
  const entityTypes: Record<string, EntityType> = {};
  for (const etData of entityTypesData) {
    const entityType = await prisma.entityType.upsert({
      where: { slug: etData.slug }, // Slug is unique for EntityType
      update: { name: etData.name, description: etData.description },
      create: etData, // id is provided in etData
    });
    entityTypes[etData.slug] = entityType;
    console.log(`Upserted EntityType: ${entityType.name}`);
  }

  // --- Seed Categories ---
  console.log('Seeding Categories...');
  const categoriesData = [
    { id: '20000000-0000-0000-0000-000000000001', name: 'Developer Tools', slug: 'developer-tools', description: 'AI tools for software development.' },
    { id: '20000000-0000-0000-0000-000000000002', name: 'Natural Language Processing', slug: 'nlp', description: 'Tools and models for NLP.' },
    { id: '20000000-0000-0000-0000-000000000003', name: 'Computer Vision', slug: 'computer-vision', description: 'Tools and models for image and video analysis.' },
    { id: '20000000-0000-0000-0000-000000000004', name: 'Machine Learning Platforms', slug: 'ml-platforms', description: 'Platforms for building and deploying ML models.' },
    { id: '20000000-0000-0000-0000-000000000005', name: 'Data Science & Analytics', slug: 'data-science-analytics', description: 'Resources for data analysis and visualization.' },
    { id: '20000000-0000-0000-0000-000000000006', name: 'AI Ethics & Governance', slug: 'ai-ethics-governance', description: 'Resources on responsible AI development.' },
    { id: '20000000-0000-0000-0000-000000000007', name: 'Robotics & Automation', slug: 'robotics-automation', description: 'AI in robotics and process automation.' },
    { id: '20000000-0000-0000-0000-000000000008', name: 'AI Education & Learning', slug: 'ai-education', description: 'Courses, tutorials, and learning platforms for AI.' },
    { id: '20000000-0000-0000-0000-000000000009', name: 'AI Infrastructure', slug: 'ai-infrastructure', description: 'Hardware and cloud solutions for AI.' },
  ];
  const categories: Record<string, Category> = {};
  for (const catData of categoriesData) {
    const category = await prisma.category.upsert({
      where: { slug: catData.slug }, // Slug is unique for Category
      update: { name: catData.name, description: catData.description },
      create: catData, // id is provided in catData
    });
    categories[catData.slug] = category;
    console.log(`Upserted Category: ${category.name}`);
  }

  // --- Seed Tags ---
  console.log('Seeding Tags...');
  const tagsData = [
    { id: '30000000-0000-0000-0000-000000000001', name: 'API Access', slug: 'api-access', description: 'Provides API access.' },
    { id: '30000000-0000-0000-0000-000000000002', name: 'Open Source', slug: 'open-source', description: 'Open source projects.' },
    { id: '30000000-0000-0000-0000-000000000003', name: 'Free Tier', slug: 'free-tier', description: 'Offers a free tier.' },
    { id: '30000000-0000-0000-0000-000000000004', name: 'LLM', slug: 'llm', description: 'Based on Large Language Models.' },
    { id: '30000000-0000-0000-0000-000000000005', name: 'Python', slug: 'python', description: 'Related to Python.' },
    { id: '30000000-0000-0000-0000-000000000006', name: 'JavaScript', slug: 'javascript', description: 'Related to JavaScript.' },
    { id: '30000000-0000-0000-0000-000000000007', name: 'TensorFlow', slug: 'tensorflow', description: 'Uses TensorFlow.' },
    { id: '30000000-0000-0000-0000-000000000008', name: 'PyTorch', slug: 'pytorch', description: 'Uses PyTorch.' },
    { id: '30000000-0000-0000-0000-000000000009', name: 'Tutorial', slug: 'tutorial', description: 'Educational tutorial format.' },
    { id: '30000000-0000-0000-0000-000000000010', name: 'Generative AI', slug: 'generative-ai', description: 'Focuses on generative AI.' },
    { id: '30000000-0000-0000-0000-000000000011', name: 'Cloud-Based', slug: 'cloud-based', description: 'Runs on cloud infrastructure.' },
    { id: '30000000-0000-0000-0000-000000000012', name: 'MLOps', slug: 'mlops', description: 'Related to MLOps practices.' },
    { id: '30000000-0000-0000-0000-000000000013', name: 'Data Visualization', slug: 'data-visualization', description: 'Tools for visualizing data.' },
    { id: '30000000-0000-0000-0000-000000000014', name: 'Research', slug: 'research', description: 'Focus on AI research.' },
    { id: '30000000-0000-0000-0000-000000000015', name: 'Beginner-Friendly', slug: 'beginner-friendly', description: 'Suitable for beginners.' },
  ];
  const tags: Record<string, Tag> = {};
  for (const tagData of tagsData) {
    const tag = await prisma.tag.upsert({
      where: { slug: tagData.slug }, // Slug is unique for Tag
      update: { name: tagData.name, description: tagData.description },
      create: tagData, // id is provided in tagData
    });
    tags[tagData.slug] = tag;
    console.log(`Upserted Tag: ${tag.name}`);
  }

  // --- Seed Features ---
  console.log('Seeding Features...');
  const featuresData = [
    { id: '40000000-0000-0000-0000-000000000001', name: 'Has API', slug: 'has-api', description: 'Offers a programmable API.' },
    { id: '40000000-0000-0000-0000-000000000002', name: 'Free Trial Available', slug: 'free-trial', description: 'Provides a free trial period.' },
    { id: '40000000-0000-0000-0000-000000000003', name: 'GUI Interface', slug: 'gui', description: 'Graphical User Interface available.' },
    { id: '40000000-0000-0000-0000-000000000004', name: 'CLI Tool', slug: 'cli', description: 'Command Line Interface available.' },
    { id: '40000000-0000-0000-0000-000000000005', name: 'Detailed Documentation', slug: 'documentation', description: 'Comprehensive documentation provided.' },
    { id: '40000000-0000-0000-0000-000000000006', name: 'Active Community Support', slug: 'community-support', description: 'Active community support channels.' },
    { id: '40000000-0000-0000-0000-000000000007', name: 'Scalable Architecture', slug: 'scalable', description: 'Designed for scalability.' },
    { id: '40000000-0000-0000-0000-000000000008', name: 'Pre-trained Models', slug: 'pre-trained-models', description: 'Offers pre-trained models.' },
  ];
  const features: Record<string, Feature> = {};
  for (const featData of featuresData) {
    const feature = await prisma.feature.upsert({
      where: { slug: featData.slug }, // Slug is unique for Feature
      update: { name: featData.name, description: featData.description },
      create: featData, // id is provided in featData
    });
    features[featData.slug] = feature;
    console.log(`Upserted Feature: ${feature.name}`);
  }

  // --- Seed Entities ---
  console.log('Seeding Entities...');
  if (submitter && Object.keys(entityTypes).length > 0 && Object.keys(categories).length > 0 && Object.keys(tags).length > 0 && Object.keys(features).length > 0) {
    const entitiesToSeed = [
      {
        name: 'CodePal AI',
        slug: 'codepal-ai',
        websiteUrl: 'https://codepal.example.com',
        shortDescription: 'Your AI-powered coding assistant.',
        description: 'CodePal AI helps developers write better code faster using advanced AI models and integrations.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['ai-tool'].id,
        details: {
          entityDetailsTool: {
            technicalLevel: TechnicalLevel.INTERMEDIATE,
            hasApi: true,
            pricingModel: PricingModel.FREEMIUM,
            hasFreeTier: true,
            keyFeatures: ['Real-time code completion', 'AI-powered debugging', 'Code generation'],
            useCases: ['Accelerating software development', 'Improving code quality'],
            platforms: ['VS Code Extension', 'JetBrains IDEs', 'Web App'],
          }
        },
        categoryIds: [categories['developer-tools'].id, categories['nlp'].id],
        tagIds: [tags['api-access'].id, tags['llm'].id, tags['python'].id, tags['free-tier'].id],
        featureIds: [features['has-api'].id, features['documentation'].id, features['gui'].id]
      },
      {
        name: 'MLFlow Advanced Course',
        slug: 'mlflow-advanced-course',
        websiteUrl: 'https://mlcourse.example.com/mlflow',
        shortDescription: 'Master MLFlow for MLOps.',
        description: 'A comprehensive course on using MLFlow for managing the machine learning lifecycle, from experimentation to deployment.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['course'].id,
        details: {
          entityDetailsCourse: {
            instructorName: 'Dr. AI Expert',
            skillLevel: SkillLevel.ADVANCED,
            durationText: '8 Weeks',
            certificateAvailable: true,
            prerequisites: 'Basic Python, ML concepts, Docker familiarity',
          }
        },
        categoryIds: [categories['ml-platforms'].id, categories['ai-education'].id],
        tagIds: [tags['tutorial'].id, tags['mlops'].id, tags['python'].id ],
        featureIds: [features['documentation'].id, features['community-support']?.id].filter(id => !!id)
      },
      // {
      //   name: 'OpenImages V7 Dataset',
      //   websiteUrl: 'https://storage.googleapis.com/openimages/web/index.html',
      //   shortDescription: 'Large-scale, multi-label image dataset.',
      //   description: 'OpenImages is a dataset of ~9M images annotated with image-level labels, object bounding boxes, object segmentation masks, visual relationships, and localized narratives.',
      //   status: EntityStatus.ACTIVE,
      //   entityTypeId: entityTypes['dataset'].id,
      //   details: {
      //     entityDetailsDataset: {
      //       format: 'Mixed (JPEG, CSV for annotations)',
      //       sourceUrl: 'https://storage.googleapis.com/openimages/web/index.html',
      //       license: 'CC BY 4.0',
      //       sizeInBytes: BigInt(10 * 1024 * 1024 * 1024), // Approx 10TB
      //       description: 'Contains bounding boxes for 600 object classes, plus image-level labels for thousands of classes.'
      //     }
      //   },
      //   categoryIds: [categories['computer-vision'].id, categories['data-science-analytics'].id],
      //   tagIds: [tags['open-source'].id, tags['research']?.id].filter(id => !!id),
      //   featureIds: []
      // },
      {
        name: 'Attention Is All You Need',
        slug: 'attention-is-all-you-need',
        websiteUrl: 'https://arxiv.org/abs/1706.03762',
        shortDescription: 'The seminal paper introducing the Transformer model.',
        description: 'This paper proposed the Transformer, a novel network architecture based solely on attention mechanisms, dispensing with recurrence and convolutions entirely.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['research-paper'].id,
        details: {
          entityDetailsResearchPaper: {
            publicationDate: new Date('2017-06-12'),
            doi: '10.48550/arXiv.1706.03762',
            authors: ['Ashish Vaswani', 'Noam Shazeer', 'Niki Parmar', 'Jakob Uszkoreit', 'Llion Jones', 'Aidan N. Gomez', 'Łukasz Kaiser', 'Illia Polosukhin'],
            journalOrConference: 'NeurIPS 2017',
            citationCount: 90000
          }
        },
        categoryIds: [categories['nlp'].id, categories['ai-education'].id],
        tagIds: [tags['llm'].id, tags['generative-ai'].id, tags['research'].id],
        featureIds: []
      },
      {
        name: 'TensorFlow Extended (TFX)',
        slug: 'tensorflow-extended-tfx',
        websiteUrl: 'https://www.tensorflow.org/tfx',
        shortDescription: 'An end-to-end platform for deploying production ML pipelines.',
        description: 'TFX is a Google-production-scale machine learning platform that provides a configuration framework and shared libraries to integrate common components needed to define, launch, and monitor your machine learning system.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['platform'].id,
        details: {
          entityDetailsPlatform: {
            platformType: 'MLOps Platform',
            keyServices: ['Data Validation', 'Model Training', 'Model Serving', 'Pipeline Orchestration'],
            documentationUrl: 'https://www.tensorflow.org/tfx/guide',
            pricingModel: PricingModel.OPEN_SOURCE
          }
        },
        categoryIds: [categories['ml-platforms'].id, categories['developer-tools'].id, categories['ai-infrastructure'].id],
        tagIds: [tags['tensorflow'].id, tags['mlops'].id, tags['open-source'].id, tags['cloud-based'].id],
        featureIds: [features['documentation'].id, features['open-source']?.id, features['community-support']?.id, features['scalable']?.id].filter(id => !!id)
      },
       {
        name: 'AI Ethics Weekly Newsletter',
        slug: 'ai-ethics-weekly-newsletter',
        websiteUrl: 'https://aiethicsweekly.example.com',
        shortDescription: 'Curated newsletter on AI ethics and responsible AI.',
        description: 'A weekly roundup of news, research, and discussions on the ethical implications of artificial intelligence and how to build more responsible AI systems.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['newsletter'].id,
        details: {
          entityDetailsNewsletter: {
            frequency: 'Weekly',
            mainTopics: ['AI Bias', 'Fairness in ML', 'AI Governance', 'AI Transparency'],
            authorName: 'Ethica AI Group',
            subscriberCount: 15000,
            subscribeUrl: 'https://aiethicsweekly.example.com/subscribe'
          }
        },
        categoryIds: [categories['ai-ethics-governance'].id, categories['ai-education'].id],
        tagIds: [tags['generative-ai']?.id, tags['research']?.id].filter(id => !!id),
        featureIds: []
      },
      {
        name: 'AI Hardware Showcase 2024',
        slug: 'ai-hardware-showcase-2024',
        websiteUrl: 'https://aihardwareshow.example.com',
        shortDescription: 'Annual event for AI hardware innovations.',
        description: 'The premier event showcasing the latest in AI chips, servers, and hardware solutions from startups and industry giants.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['event'].id,
        details: {
          entityDetailsEvent: {
            startDate: new Date('2024-10-22'),
            endDate: new Date('2024-10-24'),
            location: 'San Francisco, CA',
            isOnline: false,
            topics: ['AI Accelerators', 'Neuromorphic Computing', 'Edge AI Hardware']
          }
        },
        categoryIds: [categories['ai-infrastructure'].id, categories['robotics-automation'].id],
        tagIds: [tags['cloud-based']?.id, tags['research']?.id].filter(id => !!id),
        featureIds: []
      }
    ];

    for (const entityData of entitiesToSeed) {
      const { details, categoryIds, tagIds, featureIds, ...restOfEntityData } = entityData;

      // 1. Upsert the base entity
      const newEntity = await prisma.entity.upsert({
        where: { name: restOfEntityData.name },
        update: {
          ...restOfEntityData,
          submitterId: submitter.id,
        },
        create: {
          ...restOfEntityData,
          submitterId: submitter.id,
        },
      });

      // 2. Upsert related details
      if (details) {
        if (details.entityDetailsTool) {
          await prisma.entityDetailsTool.upsert({
            where: { entityId: newEntity.id },
            update: { ...details.entityDetailsTool },
            create: { ...details.entityDetailsTool, entityId: newEntity.id }
          });
        }
        if (details.entityDetailsCourse) {
          await prisma.entityDetailsCourse.upsert({
            where: { entityId: newEntity.id },
            update: { ...details.entityDetailsCourse },
            create: { ...details.entityDetailsCourse, entityId: newEntity.id }
          });
        }
        if (details.entityDetailsResearchPaper) {
          await prisma.entityDetailsResearchPaper.upsert({
            where: { entityId: newEntity.id },
            update: { ...details.entityDetailsResearchPaper },
            create: { ...details.entityDetailsResearchPaper, entityId: newEntity.id }
          });
        }
        if (details.entityDetailsPlatform) {
          await prisma.entityDetailsPlatform.upsert({
            where: { entityId: newEntity.id },
            update: { ...details.entityDetailsPlatform },
            create: { ...details.entityDetailsPlatform, entityId: newEntity.id }
          });
        }
        if (details.entityDetailsNewsletter) {
          await prisma.entityDetailsNewsletter.upsert({
            where: { entityId: newEntity.id },
            update: { ...details.entityDetailsNewsletter },
            create: { ...details.entityDetailsNewsletter, entityId: newEntity.id }
          });
        }
        if (details.entityDetailsEvent) {
          await prisma.entityDetailsEvent.upsert({
            where: { entityId: newEntity.id },
            update: { ...details.entityDetailsEvent },
            create: { ...details.entityDetailsEvent, entityId: newEntity.id }
          });
        }
      }

      // 3. Link categories, tags, features (many-to-many) - skip if already exists
      if (categoryIds && categoryIds.length > 0) {
        for (const categoryId of categoryIds) {
          await prisma.entityCategory.upsert({
            where: { entityId_categoryId: { entityId: newEntity.id, categoryId } },
            update: {},
            create: { entityId: newEntity.id, categoryId, assignedBy: submitter.id }
          });
        }
      }
      if (tagIds && tagIds.length > 0) {
        for (const tagId of tagIds) {
          await prisma.entityTag.upsert({
            where: { entityId_tagId: { entityId: newEntity.id, tagId } },
            update: {},
            create: { entityId: newEntity.id, tagId, assignedBy: submitter.id }
          });
        }
      }
      if (featureIds && featureIds.length > 0) {
        for (const featureId of featureIds) {
          await prisma.entityFeature.upsert({
            where: { entityId_featureId: { entityId: newEntity.id, featureId } },
            update: {},
            create: { entityId: newEntity.id, featureId, assignedBy: submitter.id }
          });
        }
      }

      console.log(`Seeded entity: ${newEntity.name}`);
    }
  } else {
    console.log('Skipping entity seeding due to missing prerequisites (submitter, entity types, etc.).');
  }

  console.log(`Seeding finished.`);
}

main()
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 