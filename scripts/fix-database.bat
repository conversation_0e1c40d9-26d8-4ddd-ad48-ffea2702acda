@echo off
REM Fix Database Schema Script for Windows
REM This script fixes the database schema issues and regenerates the Prisma client

echo 🔧 Fixing Database Schema Issues...
echo ==================================

echo Step 1: Creating database migration for articleUrl field fix...
call npx prisma migrate dev --name make_news_articleUrl_optional

if %errorlevel% neq 0 (
    echo ❌ Migration failed
    exit /b 1
)
echo ✅ Migration created successfully

echo Step 2: Generating Prisma client...
call npx prisma generate

if %errorlevel% neq 0 (
    echo ❌ Prisma client generation failed
    exit /b 1
)
echo ✅ Prisma client generated successfully

echo Step 3: Checking database status...
call npx prisma db push --accept-data-loss

if %errorlevel% neq 0 (
    echo ⚠️  Database push had warnings (this might be normal)
) else (
    echo ✅ Database schema updated successfully
)

echo.
echo 🎉 Database fixes completed!
echo You can now start your development server with:
echo npm run start:dev
echo.
echo Or test with our comprehensive test suite:
echo npm run test:script all
