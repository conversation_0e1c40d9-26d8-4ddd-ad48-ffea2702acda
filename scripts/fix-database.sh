#!/bin/bash

# Fix Database Schema Script
# This script fixes the database schema issues and regenerates the Prisma client

echo "🔧 Fixing Database Schema Issues..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Step 1: Creating database migration for articleUrl field fix...${NC}"
npx prisma migrate dev --name make_news_articleUrl_optional

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Migration created successfully${NC}"
else
    echo -e "${RED}❌ Migration failed${NC}"
    exit 1
fi

echo -e "${BLUE}Step 2: Generating Prisma client...${NC}"
npx prisma generate

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Prisma client generated successfully${NC}"
else
    echo -e "${RED}❌ Prisma client generation failed${NC}"
    exit 1
fi

echo -e "${BLUE}Step 3: Checking database status...${NC}"
npx prisma db push --accept-data-loss

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Database schema updated successfully${NC}"
else
    echo -e "${YELLOW}⚠️  Database push had warnings (this might be normal)${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Database fixes completed!${NC}"
echo -e "${BLUE}You can now start your development server with:${NC}"
echo -e "${YELLOW}npm run start:dev${NC}"
echo ""
echo -e "${BLUE}Or test with our comprehensive test suite:${NC}"
echo -e "${YELLOW}npm run test:script all${NC}"
