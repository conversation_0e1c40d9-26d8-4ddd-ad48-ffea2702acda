import { PrismaClient } from '../generated/prisma';
import { generateSlug } from '../src/utils/slug.utils';

const prisma = new PrismaClient();

async function populateEntitySlugs() {
  console.log('Starting to populate entity slugs...');
  
  try {
    // Get all entities without slugs
    const entities = await prisma.entity.findMany({
      where: {
        slug: null
      },
      select: {
        id: true,
        name: true
      }
    });

    console.log(`Found ${entities.length} entities without slugs`);

    for (const entity of entities) {
      let slug = generateSlug(entity.name);
      let counter = 0;
      
      // Check for duplicates and append counter if needed
      while (await prisma.entity.findUnique({ where: { slug } })) {
        counter++;
        slug = `${generateSlug(entity.name)}-${counter}`;
      }
      
      // Update the entity with the unique slug
      await prisma.entity.update({
        where: { id: entity.id },
        data: { slug }
      });
      
      console.log(`Updated entity "${entity.name}" with slug: ${slug}`);
    }

    console.log('Successfully populated all entity slugs!');
  } catch (error) {
    console.error('Error populating entity slugs:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
populateEntitySlugs()
  .then(() => {
    console.log('Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
