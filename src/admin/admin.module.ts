import { Module, forwardRef } from '@nestjs/common';
import { AdminUsersController } from './users/admin-users.controller';
import { UsersModule } from '../users/users.module'; // To access UserService
import { AuthModule } from '../auth/auth.module'; // To access AdminGuard or other auth components
import { AdminSettingsModule } from './settings/admin-settings.module';

@Module({
  imports: [
    forwardRef(() => UsersModule), // Use forwardRef if UsersModule might depend on AdminModule later or for guards
    forwardRef(() => AuthModule),   // Use forwardRef if AuthModule needs AdminModule or for guards
    AdminSettingsModule,
  ],
  controllers: [AdminUsersController]
})
export class AdminModule {}