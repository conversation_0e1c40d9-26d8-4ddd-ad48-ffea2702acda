import { ApiProperty } from '@nestjs/swagger';
import { CategoryResponseDto } from '../../../categories/dto/category-response.dto'; // Adjust path as needed

export class AdminCategoryListResponseDto {
  @ApiProperty({
    description: 'Array of category objects.',
    type: [CategoryResponseDto],
  })
  data: CategoryResponseDto[];

  @ApiProperty({ description: 'Total number of categories matching the query.', example: 50 })
  total: number;

  @ApiProperty({ description: 'Current page number.', example: 1 })
  page: number;

  @ApiProperty({ description: 'Number of items per page.', example: 10 })
  limit: number;

  @ApiProperty({ description: 'Total number of pages.', example: 5 })
  totalPages: number;
} 