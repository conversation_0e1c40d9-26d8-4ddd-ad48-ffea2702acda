import { Controller, Patch, Param, Body, UseGuards, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../../auth/guards/admin.guard';
import { EntitiesService } from '../../entities/entities.service';
import { AdminUpdateEntityStatusDto } from './dto/admin-update-entity-status.dto';
import { Entity as EntityModel } from '../../../generated/prisma'; // Using Prisma model directly for now
// import { EntityResponseDto } from '../../entities/dto/entity-response.dto'; // Uncomment if you have this DTO

@ApiTags('Admin - Entities')
@Controller('admin/entities')
@UseGuards(JwtAuthGuard, AdminGuard)
@ApiBearerAuth() // Indicates that endpoints in this controller require a Bearer token
export class AdminEntitiesController {
  constructor(private readonly entitiesService: EntitiesService) {}

  @Patch(':id/status')
  @ApiOperation({ summary: 'Admin: Update entity status' })
  // @ApiResponse({ status: 200, description: 'Entity status updated successfully', type: EntityResponseDto }) // Uncomment if using EntityResponseDto
  // @ApiResponse({ status: 404, description: 'Entity not found' })
  // @ApiResponse({ status: 403, description: 'Forbidden - Admin role required' })
  // @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updateEntityStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() adminUpdateEntityStatusDto: AdminUpdateEntityStatusDto,
  ): Promise<EntityModel> { // Change to EntityResponseDto if you have it
    return this.entitiesService.adminSetStatus(id, adminUpdateEntityStatusDto.status);
  }
} 