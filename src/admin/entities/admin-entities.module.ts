import { Module } from '@nestjs/common';
import { AdminEntitiesController } from './admin-entities.controller';
import { EntitiesModule } from '../../entities/entities.module'; // Import EntitiesModule to access EntitiesService
// If EntitiesService is not exported from EntitiesModule or you want a more decoupled approach,
// you might need to provide EntitiesService directly or import a shared services module.

@Module({
  imports: [
    EntitiesModule, // Provides EntitiesService
  ],
  controllers: [AdminEntitiesController],
  // providers: [], // EntitiesService is provided by EntitiesModule
})
export class AdminEntitiesModule {} 