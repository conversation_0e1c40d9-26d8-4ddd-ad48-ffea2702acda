import { IsEnum, IsOptional, IsUUID } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { ReviewStatus } from '../../../../generated/prisma';
import { PaginatedRequestDto } from 'src/common/dto/paginated-request.dto';

export class ListAdminReviewsDto extends PaginatedRequestDto {
  @ApiPropertyOptional({
    enum: ReviewStatus,
    description: 'Filter reviews by status',
  })
  @IsOptional()
  @IsEnum(ReviewStatus)
  status?: ReviewStatus;

  @ApiPropertyOptional({
    description: 'Filter reviews by a specific user ID',
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  userId?: string;

  @ApiPropertyOptional({
    description: 'Filter reviews by a specific entity ID',
    format: 'uuid',
  })
  @IsOptional()
  @IsUUID()
  entityId?: string;
} 