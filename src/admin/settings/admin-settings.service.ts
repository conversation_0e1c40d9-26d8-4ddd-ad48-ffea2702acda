import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { AppSetting } from '../../../generated/prisma';
import { UpdateLlmProviderDto } from './dto/update-llm-provider.dto';

@Injectable()
export class AdminSettingsService {
  constructor(private readonly prisma: PrismaService) {}

  async getLlmProvider(): Promise<AppSetting> {
    const setting = await this.prisma.appSetting.findUnique({
      where: { key: 'CURRENT_LLM_PROVIDER' },
    });

    if (!setting) {
      throw new NotFoundException('LLM provider setting not found');
    }

    return setting;
  }

  async updateLlmProvider(updateLlmProviderDto: UpdateLlmProviderDto): Promise<AppSetting> {
    const { provider } = updateLlmProviderDto;

    const updatedSetting = await this.prisma.appSetting.upsert({
      where: { key: 'CURRENT_LLM_PROVIDER' },
      update: {
        value: provider,
        updatedAt: new Date(),
      },
      create: {
        key: 'CURRENT_LLM_PROVIDER',
        value: provider,
        description: 'The LLM provider to use for recommendations. Options: OPENAI, GOOGLE_GEMINI, ANTHROPIC',
      },
    });

    return updatedSetting;
  }

  async getAllSettings(): Promise<AppSetting[]> {
    return this.prisma.appSetting.findMany({
      orderBy: { key: 'asc' },
    });
  }

  async getSetting(key: string): Promise<AppSetting> {
    const setting = await this.prisma.appSetting.findUnique({
      where: { key },
    });

    if (!setting) {
      throw new NotFoundException(`Setting with key '${key}' not found`);
    }

    return setting;
  }

  async updateSetting(key: string, value: string, description?: string): Promise<AppSetting> {
    const updatedSetting = await this.prisma.appSetting.upsert({
      where: { key },
      update: {
        value,
        description: description || undefined,
        updatedAt: new Date(),
      },
      create: {
        key,
        value,
        description: description || null,
      },
    });

    return updatedSetting;
  }

  async deleteSetting(key: string): Promise<void> {
    try {
      await this.prisma.appSetting.delete({
        where: { key },
      });
    } catch (error) {
      throw new NotFoundException(`Setting with key '${key}' not found`);
    }
  }
}
