import { ApiProperty } from '@nestjs/swagger';

export class AppSettingResponseDto {
  @ApiProperty({
    description: 'The setting key',
    example: 'CURRENT_LLM_PROVIDER',
  })
  key: string;

  @ApiProperty({
    description: 'The setting value',
    example: 'OPENAI',
  })
  value: string;

  @ApiProperty({
    description: 'Description of the setting',
    example: 'The LLM provider to use for recommendations. Options: OPENAI, GOOGLE_GEMINI, ANTHROPIC',
    required: false,
  })
  description?: string;

  @ApiProperty({
    description: 'When the setting was created',
    example: '2024-06-20T01:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'When the setting was last updated',
    example: '2024-06-20T01:00:00.000Z',
  })
  updatedAt: Date;
}
