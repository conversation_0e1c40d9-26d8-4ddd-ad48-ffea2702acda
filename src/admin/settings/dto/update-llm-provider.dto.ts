import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';

export enum LlmProvider {
  OPENAI = 'OPENAI',
  GOOGLE_GEMINI = 'GOOGLE_GEMINI',
  ANTHROPIC = 'ANTHROPIC',
}

export class UpdateLlmProviderDto {
  @ApiProperty({
    description: 'The LLM provider to use for recommendations',
    enum: LlmProvider,
    example: LlmProvider.OPENAI,
  })
  @IsNotEmpty()
  @IsEnum(LlmProvider, {
    message: 'Provider must be one of: OPENAI, GOOGLE_GEMINI, ANTHROPIC',
  })
  provider: LlmProvider;
}
