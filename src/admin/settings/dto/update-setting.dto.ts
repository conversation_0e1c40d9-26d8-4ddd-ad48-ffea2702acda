import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class UpdateSettingDto {
  @ApiProperty({
    description: 'The setting value',
    example: 'OPENAI',
  })
  @IsNotEmpty()
  @IsString()
  value: string;

  @ApiProperty({
    description: 'Optional description of the setting',
    example: 'The LLM provider to use for recommendations',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;
}
