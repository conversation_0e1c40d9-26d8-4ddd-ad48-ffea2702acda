import { ApiProperty } from '@nestjs/swagger';
import { TagResponseDto } from '../../../tags/dto/tag-response.dto'; // Adjust path as needed

export class AdminTagListResponseDto {
  @ApiProperty({
    description: 'Array of tag objects.',
    type: [TagResponseDto],
  })
  data: TagResponseDto[];

  @ApiProperty({ description: 'Total number of tags matching the query.', example: 100 })
  total: number;

  @ApiProperty({ description: 'Current page number.', example: 1 })
  page: number;

  @ApiProperty({ description: 'Number of items per page.', example: 10 })
  limit: number;

  @ApiProperty({ description: 'Total number of pages.', example: 10 })
  totalPages: number;
} 