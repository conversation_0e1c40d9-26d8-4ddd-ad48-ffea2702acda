import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AdminUsersController } from './admin-users.controller';
import { UserService } from '../../users/users.service';
import { PrismaService } from '../../prisma/prisma.service';
import { SupabaseService } from '../../supabase/supabase.service';
import { AdminGuard } from '../../auth/guards/admin.guard';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { mockPrismaService } from '../../test/mocks/prisma.service.mock';
import { UpdateUserStatusDto } from './dto/update-user-status.dto';
import { UpdateUserRoleDto } from './dto/update-user-role.dto';
import { User as UserModel, UserRole, UserStatus } from '@generated-prisma';

describe('AdminUsersController (Integration)', () => {
  let app: INestApplication;
  let userService: UserService;

  const mockAdminUser: UserModel = {
    id: 'admin-123',
    authUserId: 'auth-admin-123',
    email: '<EMAIL>',
    username: 'admin',
    displayName: 'Admin User',
    profilePictureUrl: null,
    role: UserRole.ADMIN,
    status: UserStatus.ACTIVE,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    lastLogin: new Date('2023-01-01'),
    isActive: true,
    bio: null,
    location: null,
    website: null,
    socialLinks: null,
    preferences: null,
    emailVerified: true,
    emailVerifiedAt: new Date('2023-01-01'),
    twoFactorEnabled: false,
    lastPasswordChange: new Date('2023-01-01'),
    loginAttempts: 0,
    lockedUntil: null,
    passwordResetToken: null,
    passwordResetExpires: null,
    emailVerificationToken: null,
    emailVerificationExpires: null,
  };

  const mockRegularUser: UserModel = {
    id: 'user-123',
    authUserId: 'auth-123',
    email: '<EMAIL>',
    username: 'testuser',
    displayName: 'Test User',
    profilePictureUrl: null,
    role: UserRole.USER,
    status: UserStatus.ACTIVE,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    lastLogin: new Date('2023-01-01'),
    isActive: true,
    bio: null,
    location: null,
    website: null,
    socialLinks: null,
    preferences: null,
    emailVerified: true,
    emailVerifiedAt: new Date('2023-01-01'),
    twoFactorEnabled: false,
    lastPasswordChange: new Date('2023-01-01'),
    loginAttempts: 0,
    lockedUntil: null,
    passwordResetToken: null,
    passwordResetExpires: null,
    emailVerificationToken: null,
    emailVerificationExpires: null,
  };

  const mockSupabaseService = {
    updateUserMetadata: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdminUsersController],
      providers: [
        UserService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: SupabaseService,
          useValue: mockSupabaseService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn((context) => {
          const request = context.switchToHttp().getRequest();
          request.user = mockAdminUser;
          return true;
        }),
      })
      .overrideGuard(AdminGuard)
      .useValue({
        canActivate: jest.fn(() => true),
      })
      .compile();

    app = module.createNestApplication();
    
    // Apply global validation pipe like in main.ts
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }));

    userService = module.get<UserService>(UserService);
    
    await app.init();
  });

  afterEach(async () => {
    await app.close();
    jest.clearAllMocks();
  });

  describe('GET /admin/users', () => {
    const mockUsersList = {
      users: [
        {
          id: mockAdminUser.id,
          username: mockAdminUser.username,
          displayName: mockAdminUser.displayName,
          email: mockAdminUser.email,
          role: mockAdminUser.role,
          status: mockAdminUser.status,
          createdAt: mockAdminUser.createdAt,
          updatedAt: mockAdminUser.updatedAt,
          lastLogin: mockAdminUser.lastLogin,
        },
        {
          id: mockRegularUser.id,
          username: mockRegularUser.username,
          displayName: mockRegularUser.displayName,
          email: mockRegularUser.email,
          role: mockRegularUser.role,
          status: mockRegularUser.status,
          createdAt: mockRegularUser.createdAt,
          updatedAt: mockRegularUser.updatedAt,
          lastLogin: mockRegularUser.lastLogin,
        },
      ],
      total: 2,
      page: 1,
      limit: 10,
    };

    it('should return paginated list of users', async () => {
      jest.spyOn(userService, 'findAllUsers').mockResolvedValue(mockUsersList as any);

      const response = await request(app.getHttpServer())
        .get('/admin/users')
        .expect(200);

      expect(response.body).toHaveProperty('users');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('limit');
      expect(response.body.users).toHaveLength(2);
      expect(userService.findAllUsers).toHaveBeenCalledWith({});
    });

    it('should handle query parameters correctly', async () => {
      jest.spyOn(userService, 'findAllUsers').mockResolvedValue(mockUsersList as any);

      await request(app.getHttpServer())
        .get('/admin/users')
        .query({
          page: '2',
          limit: '5',
          status: UserStatus.ACTIVE,
          sortBy: 'username',
          sortOrder: 'asc',
        })
        .expect(200);

      expect(userService.findAllUsers).toHaveBeenCalledWith({
        page: 2,
        limit: 5,
        filterByStatus: UserStatus.ACTIVE,
        sortBy: 'username',
        sortOrder: 'asc',
      });
    });

    it('should validate query parameter types', async () => {
      await request(app.getHttpServer())
        .get('/admin/users')
        .query({
          page: 'invalid',
          limit: 'invalid',
        })
        .expect(400);
    });

    it('should require admin authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [AdminUsersController],
        providers: [
          UserService,
          { provide: PrismaService, useValue: mockPrismaService },
          { provide: SupabaseService, useValue: mockSupabaseService },
        ],
      })
        .overrideGuard(AdminGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .get('/admin/users')
        .expect(403);

      await testApp.close();
    });

    it('should filter by status when provided', async () => {
      const filteredResult = {
        ...mockUsersList,
        users: [mockUsersList.users[0]], // Only active users
        total: 1,
      };
      jest.spyOn(userService, 'findAllUsers').mockResolvedValue(filteredResult as any);

      await request(app.getHttpServer())
        .get('/admin/users')
        .query({ status: UserStatus.ACTIVE })
        .expect(200);

      expect(userService.findAllUsers).toHaveBeenCalledWith({
        filterByStatus: UserStatus.ACTIVE,
      });
    });
  });

  describe('GET /admin/users/:id', () => {
    it('should return a specific user by id', async () => {
      jest.spyOn(userService, 'findProfileById').mockResolvedValue(mockRegularUser);

      const response = await request(app.getHttpServer())
        .get(`/admin/users/${mockRegularUser.id}`)
        .expect(200);

      expect(response.body).toHaveProperty('id', mockRegularUser.id);
      expect(response.body).toHaveProperty('email', mockRegularUser.email);
      expect(response.body).toHaveProperty('role', mockRegularUser.role);
      expect(userService.findProfileById).toHaveBeenCalledWith(mockRegularUser.id);
    });

    it('should return 404 when user not found', async () => {
      jest.spyOn(userService, 'findProfileById').mockResolvedValue(null);

      await request(app.getHttpServer())
        .get('/admin/users/non-existent-id')
        .expect(404);
    });

    it('should return 400 for invalid UUID format', async () => {
      await request(app.getHttpServer())
        .get('/admin/users/invalid-uuid')
        .expect(400);
    });

    it('should require admin authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [AdminUsersController],
        providers: [
          UserService,
          { provide: PrismaService, useValue: mockPrismaService },
          { provide: SupabaseService, useValue: mockSupabaseService },
        ],
      })
        .overrideGuard(AdminGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .get(`/admin/users/${mockRegularUser.id}`)
        .expect(403);

      await testApp.close();
    });
  });

  describe('PUT /admin/users/:id/status', () => {
    const updateStatusDto: UpdateUserStatusDto = {
      status: UserStatus.SUSPENDED,
    };

    it('should update user status successfully', async () => {
      const updatedUser = { ...mockRegularUser, status: UserStatus.SUSPENDED };
      jest.spyOn(userService, 'updateUserStatus').mockResolvedValue(updatedUser);

      const response = await request(app.getHttpServer())
        .put(`/admin/users/${mockRegularUser.id}/status`)
        .send(updateStatusDto)
        .expect(200);

      expect(response.body.status).toBe(UserStatus.SUSPENDED);
      expect(userService.updateUserStatus).toHaveBeenCalledWith(
        mockAdminUser,
        mockRegularUser.id,
        UserStatus.SUSPENDED,
      );
    });

    it('should validate status enum', async () => {
      const invalidStatusDto = {
        status: 'INVALID_STATUS',
      };

      await request(app.getHttpServer())
        .put(`/admin/users/${mockRegularUser.id}/status`)
        .send(invalidStatusDto)
        .expect(400);
    });

    it('should return 400 for invalid UUID format', async () => {
      await request(app.getHttpServer())
        .put('/admin/users/invalid-uuid/status')
        .send(updateStatusDto)
        .expect(400);
    });

    it('should require admin authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [AdminUsersController],
        providers: [
          UserService,
          { provide: PrismaService, useValue: mockPrismaService },
          { provide: SupabaseService, useValue: mockSupabaseService },
        ],
      })
        .overrideGuard(AdminGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .put(`/admin/users/${mockRegularUser.id}/status`)
        .send(updateStatusDto)
        .expect(403);

      await testApp.close();
    });

    it('should handle service errors gracefully', async () => {
      jest.spyOn(userService, 'updateUserStatus').mockRejectedValue(
        new Error('Cannot update own status'),
      );

      await request(app.getHttpServer())
        .put(`/admin/users/${mockRegularUser.id}/status`)
        .send(updateStatusDto)
        .expect(500);
    });
  });

  describe('PUT /admin/users/:id/role', () => {
    const updateRoleDto: UpdateUserRoleDto = {
      role: UserRole.MODERATOR,
    };

    it('should update user role successfully', async () => {
      const updatedUser = { ...mockRegularUser, role: UserRole.MODERATOR };
      jest.spyOn(userService, 'updateUserRole').mockResolvedValue(updatedUser);

      const response = await request(app.getHttpServer())
        .put(`/admin/users/${mockRegularUser.id}/role`)
        .send(updateRoleDto)
        .expect(200);

      expect(response.body.role).toBe(UserRole.MODERATOR);
      expect(userService.updateUserRole).toHaveBeenCalledWith(
        mockAdminUser,
        mockRegularUser.id,
        UserRole.MODERATOR,
      );
    });

    it('should validate role enum', async () => {
      const invalidRoleDto = {
        role: 'INVALID_ROLE',
      };

      await request(app.getHttpServer())
        .put(`/admin/users/${mockRegularUser.id}/role`)
        .send(invalidRoleDto)
        .expect(400);
    });

    it('should return 400 for invalid UUID format', async () => {
      await request(app.getHttpServer())
        .put('/admin/users/invalid-uuid/role')
        .send(updateRoleDto)
        .expect(400);
    });

    it('should require admin authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [AdminUsersController],
        providers: [
          UserService,
          { provide: PrismaService, useValue: mockPrismaService },
          { provide: SupabaseService, useValue: mockSupabaseService },
        ],
      })
        .overrideGuard(AdminGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .put(`/admin/users/${mockRegularUser.id}/role`)
        .send(updateRoleDto)
        .expect(403);

      await testApp.close();
    });

    it('should handle forbidden operations', async () => {
      jest.spyOn(userService, 'updateUserRole').mockRejectedValue(
        new Error('Cannot remove the last admin'),
      );

      await request(app.getHttpServer())
        .put(`/admin/users/${mockRegularUser.id}/role`)
        .send(updateRoleDto)
        .expect(500);
    });

    it('should strip non-whitelisted properties', async () => {
      const dtoWithExtraFields = {
        ...updateRoleDto,
        status: UserStatus.SUSPENDED, // Should be stripped
        maliciousField: 'should be removed',
      };

      const updatedUser = { ...mockRegularUser, role: UserRole.MODERATOR };
      jest.spyOn(userService, 'updateUserRole').mockResolvedValue(updatedUser);

      await request(app.getHttpServer())
        .put(`/admin/users/${mockRegularUser.id}/role`)
        .send(dtoWithExtraFields)
        .expect(200);

      expect(userService.updateUserRole).toHaveBeenCalledWith(
        mockAdminUser,
        mockRegularUser.id,
        UserRole.MODERATOR,
      );
    });
  });
});
