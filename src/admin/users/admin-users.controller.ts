import { Controller, Get, Param, Put, Body, UseGuards, Query, ParseUUIDPipe, NotFoundException } from '@nestjs/common';
import { UserService } from '../../users/users.service'; // Adjust path as needed
import { AdminGuard } from '../../auth/guards/admin.guard'; // Adjust path as needed
import { User as PublicUserModel, UserStatus, UserRole } from '../../../generated/prisma'; // Adjust path as needed
import { GetUser } from '../../auth/decorators/get-user.decorator';
import { UpdateUserStatusDto } from './dto/update-user-status.dto'; // Corrected import path
import { UpdateUserRoleDto } from './dto/update-user-role.dto'; // Corrected import path
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery, ApiParam, ApiBody } from '@nestjs/swagger';
import { UpdateProfileDto } from '../../users/dto/update-profile.dto'; // Added import for UpdateProfileDto
import { UserProfileResponseDto } from '../../users/dto/user-profile-response.dto'; // Corrected import for UserProfileResponseDto
import { AdminUserListResponseDto } from './dto/admin-user-list-response.dto'; // Import the new DTO

@ApiTags('Admin - Users')
@ApiBearerAuth() // All admin endpoints require Bearer authentication
@Controller('admin/users')
@UseGuards(AdminGuard) // Apply AdminGuard to the whole controller
export class AdminUsersController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @ApiOperation({ summary: 'Get a paginated list of all users (Admin)' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number for pagination.', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of users per page.', example: 10 })
  @ApiQuery({ name: 'status', required: false, enum: UserStatus, description: 'Filter users by status.' })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Field to sort by.', example: 'createdAt', enum: ['createdAt', 'username', 'email', 'status', 'role', 'lastLogin', 'updatedAt', 'displayName'] })
  @ApiQuery({ name: 'sortOrder', required: false, enum: ['asc', 'desc'], description: "Sort order ('asc' or 'desc').", example: 'desc' })
  @ApiResponse({ status: 200, description: 'List of users retrieved successfully.', type: AdminUserListResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden resource. User is not an admin.' })
  async getAllUsers(
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('status') filterByStatus?: UserStatus,
    @Query('sortBy') sortBy?: string, // Will be validated/cast in service or use a more specific DTO
    @Query('sortOrder') sortOrder?: 'asc' | 'desc',
  ) {
    const pageNumber = page ? parseInt(page, 10) : undefined;
    const limitNumber = limit ? parseInt(limit, 10) : undefined;

    // Basic validation for sortBy if it's passed as a general string
    // More robust validation could involve a DTO with @IsEnum for sortBy
    const allowedSortByFields = ['createdAt', 'username', 'email', 'status', 'role', 'lastLogin', 'updatedAt', 'displayName'];
    const safeSortBy = sortBy && allowedSortByFields.includes(sortBy) ? sortBy : 'createdAt';

    return this.userService.findAllUsers({
      page: pageNumber,
      limit: limitNumber,
      filterByStatus,
      sortBy: safeSortBy as any, // Casting to any here, or ensure service handles general string
      sortOrder,
    });
  }

  @Get(':userId')
  @ApiOperation({ summary: 'Get a specific user by ID (Admin)' })
  @ApiParam({ name: 'userId', type: String, format: 'uuid', description: "The UUID of the user to retrieve." })
  @ApiResponse({ status: 200, description: 'User details retrieved successfully.', type: UserProfileResponseDto })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden resource.' })
  async getUserById(@Param('userId', ParseUUIDPipe) userId: string): Promise<UserProfileResponseDto> {
    const user = await this.userService.findProfileById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found.`);
    }
    // Map to UserProfileResponseDto
    return {
      id: user.id,
      authUserId: user.authUserId,
      email: user.email,
      username: user.username,
      displayName: user.displayName,
      profilePictureUrl: user.profilePictureUrl,
      bio: user.bio,
      status: user.status as UserStatus,
      role: user.role as UserRole,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLoginAt: user.lastLogin,
    };
  }

  @Put(':userId/status')
  @ApiOperation({ summary: "Update a user\'s status (Admin)" })
  @ApiParam({ name: 'userId', type: String, format: 'uuid', description: "The UUID of the user whose status is to be updated." })
  @ApiBody({ type: UpdateUserStatusDto })
  @ApiResponse({ status: 200, description: "User status updated successfully.", type: UserProfileResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid input data or invalid status transition.' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden resource (e.g., admin trying to change their own status).' })
  async updateUserStatus(
    @GetUser() adminUser: PublicUserModel, // The authenticated admin user
    @Param('userId', ParseUUIDPipe) targetUserId: string,
    @Body() updateUserStatusDto: UpdateUserStatusDto,
  ): Promise<UserProfileResponseDto> {
    const updatedUser = await this.userService.updateUserStatus(adminUser, targetUserId, updateUserStatusDto.status);
    // Map to UserProfileResponseDto
    return {
      id: updatedUser.id,
      authUserId: updatedUser.authUserId,
      email: updatedUser.email,
      username: updatedUser.username,
      displayName: updatedUser.displayName,
      profilePictureUrl: updatedUser.profilePictureUrl,
      bio: updatedUser.bio,
      status: updatedUser.status as UserStatus,
      role: updatedUser.role as UserRole,
      createdAt: updatedUser.createdAt,
      updatedAt: updatedUser.updatedAt,
      lastLoginAt: updatedUser.lastLogin,
    };
  }

  @Put(':userId/role')
  @ApiOperation({ summary: "Update a user\'s role (Admin)" })
  @ApiParam({ name: 'userId', type: String, format: 'uuid', description: "The UUID of the user whose role is to be updated." })
  @ApiBody({ type: UpdateUserRoleDto })
  @ApiResponse({ status: 200, description: "User role updated successfully.", type: UserProfileResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid input data.' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden resource (e.g., admin trying to change their own role or remove last admin).' })
  async updateUserRole(
    @GetUser() adminUser: PublicUserModel, // The authenticated admin user
    @Param('userId', ParseUUIDPipe) targetUserId: string,
    @Body() updateUserRoleDto: UpdateUserRoleDto,
  ): Promise<UserProfileResponseDto> {
    const updatedUser = await this.userService.updateUserRole(adminUser, targetUserId, updateUserRoleDto.role);
    // Map to UserProfileResponseDto
    return {
      id: updatedUser.id,
      authUserId: updatedUser.authUserId,
      email: updatedUser.email,
      username: updatedUser.username,
      displayName: updatedUser.displayName,
      profilePictureUrl: updatedUser.profilePictureUrl,
      bio: updatedUser.bio,
      status: updatedUser.status as UserStatus,
      role: updatedUser.role as UserRole,
      createdAt: updatedUser.createdAt,
      updatedAt: updatedUser.updatedAt,
      lastLoginAt: updatedUser.lastLogin,
    };
  }

  // Other admin endpoints will be added here

} 