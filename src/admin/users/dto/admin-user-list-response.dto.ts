import { ApiProperty } from '@nestjs/swagger';
import { UserProfileResponseDto } from '../../../users/dto/user-profile-response.dto'; // Adjust path as needed

export class AdminUserListResponseDto {
  @ApiProperty({
    description: 'Array of user profiles.',
    type: [UserProfileResponseDto],
  })
  data: UserProfileResponseDto[];

  @ApiProperty({ description: 'Total number of users matching the query.', example: 100 })
  total: number;

  @ApiProperty({ description: 'Current page number.', example: 1 })
  page: number;

  @ApiProperty({ description: 'Number of items per page.', example: 10 })
  limit: number;

  @ApiProperty({ description: 'Total number of pages.', example: 10 })
  totalPages: number;
} 