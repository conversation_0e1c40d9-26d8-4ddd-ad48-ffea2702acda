import { IsEnum, IsNotEmpty } from 'class-validator';
import { UserRole } from '../../../../generated/prisma'; // Adjust path as necessary
import { ApiProperty } from '@nestjs/swagger';
 
export class UpdateUserRoleDto {
  @ApiProperty({
    description: "The new role to assign to the user.",
    enum: UserRole,
    example: UserRole.USER,
  })
  @IsNotEmpty()
  @IsEnum(UserRole, { message: 'Invalid user role provided.' })
  role: UserRole;
} 