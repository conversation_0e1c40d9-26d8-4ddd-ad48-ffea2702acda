import { IsEnum, IsNotEmpty } from 'class-validator';
import { UserStatus } from '../../../../generated/prisma'; // Corrected path
import { ApiProperty } from '@nestjs/swagger';
 
export class UpdateUserStatusDto {
  @ApiProperty({
    description: "The new status to assign to the user.",
    enum: UserStatus,
    example: UserStatus.ACTIVE,
  })
  @IsNotEmpty()
  @IsEnum(UserStatus, { message: 'Invalid user status provided.' })
  status: UserStatus;
} 