import { Controller, Get, Res } from '@nestjs/common';
import { Response } from 'express';
import { AppService } from './app.service';
import { SupabaseService } from './supabase/supabase.service';

@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly supabaseService: SupabaseService,
  ) {}

  @Get()
  getHello(): string {
    return this.appService.getHello();
  }

  // Health check endpoint
  @Get('/healthz')
  healthCheck(@Res() res: Response) {
    res.status(200).send('OK');
  }

  @Get('test-connection')
  async testConnection() {
    try {
      const { data, error, count } = await this.supabaseService.getClient()
        .from('users') 
        .select('*', { count: 'exact', head: true }); 

      if (error) {
        console.error('Supabase connection test error:', error);
        return {
          status: 'error',
          message: 'Failed to connect to Supabase or query users table',
          details: error.message
        };
      }
      
      return {
        status: 'success',
        message: 'Successfully connected to Supabase and queried users table count.',
        userCount: count
      };
    } catch (error) {
      console.error('Supabase connection test exception:', error);
      return {
        status: 'error',
        message: 'Exception during Supabase connection test',
        details: error.message
      };
    }
  }
} 