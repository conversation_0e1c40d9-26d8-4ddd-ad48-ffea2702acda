import {
  MiddlewareConsumer,
  Module,
  NestModule,
  RequestMethod,
} from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { ThrottlerGuard, ThrottlerModule } from '@nestjs/throttler';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { SupabaseModule } from './supabase/supabase.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { PrismaModule } from './prisma/prisma.module';
import { TestModule } from './test/test.module';
import { AdminModule } from './admin/admin.module';
import { CategoriesModule } from './categories/categories.module';
import { TagsModule } from './tags/tags.module';
import { EntitiesModule } from './entities/entities.module';
import { BookmarksModule } from './bookmarks/bookmarks.module';
import { ReviewsModule } from './reviews/reviews.module';
import { RequestLoggerMiddleware } from './common/middleware/request-logger.middleware';
import { CorrelationIdMiddleware } from './common/middleware/correlation-id.middleware';
import { LoggerModule } from './common/logger/logger.module';
import { AdminEntitiesModule } from './admin/entities/admin-entities.module';
import { EntityTypesModule } from './entity-types/entity-types.module';
import { FeaturesModule } from './features/features.module';
import { RecommendationsModule } from './recommendations/recommendations.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    LoggerModule,
    ThrottlerModule.forRoot([{
      ttl: 60000,
      limit: 20,
    }]),
    SupabaseModule,
    AuthModule,
    UsersModule,
    PrismaModule,
    TestModule,
    AdminModule,
    CategoriesModule,
    TagsModule,
    EntitiesModule,
    EntityTypesModule,
    BookmarksModule,
    ReviewsModule,
    AdminEntitiesModule,
    FeaturesModule,
    RecommendationsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(CorrelationIdMiddleware, RequestLoggerMiddleware)
      .forRoutes({ path: '*' , method: RequestMethod.ALL });
  }
}