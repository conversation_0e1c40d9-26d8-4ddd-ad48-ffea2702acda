import { Injectable, ConflictException, InternalServerErrorException, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { SupabaseService } from '../supabase/supabase.service';
import { RegisterAuthDto } from './dto/register-auth.dto';
import { AuthError, Session, User } from '@supabase/supabase-js';
import { LoginAuthDto } from './dto/login-auth.dto';
import { ConfigService } from '@nestjs/config';
import { ForgotPasswordDto } from './dto/forgot-password.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { ResendConfirmationDto } from './dto/resend-confirmation.dto';
import { PrismaService } from '../prisma/prisma.service';
import { UserRole, UserStatus, User as PrismaUser } from '../../generated/prisma';

interface SyncProfilePayload {
  id: string; // This will be the auth_user_id (from JWT sub)
  email?: string;
  user_metadata?: Record<string, any>;
}

function extractUsernameFromEmail(email: string): string {
  const atIndex = email.indexOf('@');
  if (atIndex > 0) {
    return email.substring(0, atIndex);
  }
  return email; // Fallback or handle differently
}

@Injectable()
export class AuthService {
  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {}

  async signUp(
    registerAuthDto: RegisterAuthDto,
  ): Promise<{ message: string; user: User | null; /* NO session here based on last decision */ }> {
    const { email, password, display_name: displayName } = registerAuthDto; // Destructure display_name and rename to displayName
    const supabase = this.supabaseService.getClient();

    console.log('[AuthService.signUp] Attempting Supabase sign up for:', email);

    const { data: signUpAuthData, error: signUpAuthError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          display_name: displayName || extractUsernameFromEmail(email), 
        }
      }
    });

    console.log('[AuthService.signUp] Supabase signUp response - authData:', JSON.stringify(signUpAuthData, null, 2));
    console.log('[AuthService.signUp] Supabase signUp response - signUpError:', JSON.stringify(signUpAuthError, null, 2));

    if (signUpAuthError) {
      if (signUpAuthError.message.toLowerCase().includes('user already registered')) {
        throw new ConflictException('User with this email already exists.');
      }
      console.error('Supabase signUp error:', signUpAuthError);
      throw new InternalServerErrorException('Failed to register user with Supabase.');
    }

    if (!signUpAuthData.user) {
      throw new InternalServerErrorException('User registration did not return user data from Supabase.');
    }

    // Step 2: Create/Update profile in public.users table
    try {
      const profile = await this.prisma.user.upsert({ 
        where: { authUserId: signUpAuthData.user.id }, 
        update: { 
          email: signUpAuthData.user.email!,
          displayName: displayName || extractUsernameFromEmail(signUpAuthData.user.email!), 
        },
        create: {
          authUserId: signUpAuthData.user.id, 
          email: signUpAuthData.user.email!,
          displayName: displayName || extractUsernameFromEmail(signUpAuthData.user.email!), 
        role: UserRole.USER,
        status: UserStatus.ACTIVE,
        },
      });
      console.log(`Profile created/updated in public.users for authUserId: ${signUpAuthData.user.id}`);
    } catch (dbError: any) { 
      console.error(`CRITICAL: Failed to create/update profile in public.users for authUserId ${signUpAuthData.user.id}`, dbError);
      
      try {
        const adminClient = this.supabaseService.getAdminClient(); 
        const { error: deleteError } = await adminClient.auth.admin.deleteUser(signUpAuthData.user.id);
        if (deleteError) {
          console.error(`[COMPENSATION FAILED] Failed to delete Supabase auth user ${signUpAuthData.user.id} after profile creation failure:`, deleteError);
        } else {
          console.log(`[COMPENSATION SUCCEEDED] Successfully deleted Supabase auth user ${signUpAuthData.user.id} after profile creation failure.`);
        }
      } catch (adminDeleteError) {
        console.error(`[COMPENSATION EXCEPTION] Exception during attempt to delete Supabase auth user ${signUpAuthData.user.id}:`, adminDeleteError);
      }

      if (dbError.code === 'P2002') { 
        const target = dbError.meta?.target as string[] | undefined;
        if (target?.includes('email')) {
          throw new ConflictException('This email is already associated with another profile in our system.');
        } else if (target?.includes('authUserId')) { // Corrected to authUserId
            throw new ConflictException('This authentication ID is already linked to a profile.');
        } else if (target?.includes('username') && target !== undefined) { 
            throw new ConflictException('This username is already taken in our system.');
        }
        throw new ConflictException('A profile with some of this information already exists.');
      }
      
      throw new InternalServerErrorException('Registration succeeded with auth provider, but profile creation failed.');
    }
    
    const message = signUpAuthData.session 
      ? 'Registration successful and user logged in by Supabase!' 
      : 'Registration successful! Please check your email to confirm your account.';

    return {
      message,
      user: signUpAuthData.user, 
    };
  }

  async signIn(loginAuthDto: LoginAuthDto): Promise<{ user: User; session: Session | null }> {
    const supabase = this.supabaseService.getClient();
    const { data, error } = await supabase.auth.signInWithPassword({
      email: loginAuthDto.email,
      password: loginAuthDto.password,
    });

    if (error) {
      if (error.message === 'Invalid login credentials') {
        throw new UnauthorizedException('Invalid login credentials.');
      }
      console.error("Supabase signIn error:", error);
      throw new InternalServerErrorException(error.message || 'Error during sign in.');
    }
    if (!data || !data.user || !data.session) {
      console.error('Supabase signIn: No user or session data returned', data);
      throw new InternalServerErrorException('Sign in process failed to return complete user information.');
    }
    return { user: data.user, session: data.session };
  }

  async signOut() {
    const supabase = this.supabaseService.getClient();
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error("Error during sign out:", error);
      throw new InternalServerErrorException(error.message || 'Error during sign out.');
    }
    return { message: 'Successfully signed out.' };
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    const supabase = this.supabaseService.getClient();
    const frontendBaseUrl = this.configService.get<string>('FRONTEND_BASE_URL');
    if (!frontendBaseUrl) {
      throw new InternalServerErrorException('FRONTEND_BASE_URL is not configured.');
    }
    const { error } = await supabase.auth.resetPasswordForEmail(forgotPasswordDto.email, {
      redirectTo: `${frontendBaseUrl}/auth/update-password`,
    });

    if (error) {
      console.error("Error sending password reset email:", error);
    }
    return { message: 'If an account with this email exists, a password reset link has been sent.' };
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto, accessToken: string | undefined) {
    const supabase = this.supabaseService.getClient();
    
    if (accessToken) {
      const { error: sessionError } = await supabase.auth.setSession({
        access_token: accessToken,
        refresh_token: 'dummy'
      });
      if (sessionError) {
        console.error('Password reset: Error setting session with access token.', sessionError);
        throw new UnauthorizedException('Invalid or expired password reset link. Please request a new one.');
      }
    } else {
      console.warn('ResetPassword called without an explicit access token. Relying on implicit session state.');
    }

    const { error: updateError } = await supabase.auth.updateUser({
      password: resetPasswordDto.password,
    });

    if (updateError) {
      console.error("Error resetting password:", updateError);
      if (updateError.message.includes('weak password')) {
        throw new BadRequestException('Password is too weak. Please choose a stronger password.');
      }
      if (updateError.message.includes('same as the old password')) {
        throw new BadRequestException('New password cannot be the same as the old password.');
      }
      throw new InternalServerErrorException(updateError.message || 'Error resetting password.');
    }
    return { message: 'Password has been successfully reset.' };
  }

  async resendConfirmation(resendConfirmationDto: ResendConfirmationDto) {
    const supabase = this.supabaseService.getClient();
    const frontendBaseUrl = this.configService.get<string>('FRONTEND_BASE_URL');
    if (!frontendBaseUrl) {
      throw new InternalServerErrorException('FRONTEND_BASE_URL is not configured.');
    }

    const { error } = await supabase.auth.resend({
      type: 'signup',
      email: resendConfirmationDto.email,
      options: {
        emailRedirectTo: `${frontendBaseUrl}/auth/confirm-email`,
      }
    });

    if (error) {
      console.error("Error resending confirmation email:", error);
    }
    return { message: 'If an account with this email exists and requires confirmation, a new link has been sent.' };
  }

  private handleAuthError(error: AuthError): never {
    console.error('Supabase Auth Error:', error.name, error.message);
    if (error.message.includes('User already registered')) {
      throw new ConflictException('User with this email already exists.');
    }
    if (error.status === 400 || error.name === 'AuthWeakPasswordError') {
      throw new BadRequestException(error.message || 'Invalid registration data or weak password.');
    }
    throw new InternalServerErrorException(error.message || 'An unexpected error occurred during authentication.');
  }

  async syncUserProfile(authUserPayload: SyncProfilePayload): Promise<PrismaUser> {
    console.log('[AuthService] syncUserProfile - Received authUserPayload:', JSON.stringify(authUserPayload, null, 2)); 

    if (!authUserPayload || !authUserPayload.id || !authUserPayload.email) {
      console.error(
        `[AuthService] syncUserProfile - Validation FAILED. Received ID: ${authUserPayload?.id}, Received Email: ${authUserPayload?.email}`
      ); 
      throw new BadRequestException('Valid authenticated user data is required for profile sync.');
    }

    const displayNameFromMeta = authUserPayload.user_metadata?.full_name || 
                                authUserPayload.user_metadata?.name || 
                                authUserPayload.user_metadata?.display_name ||
                                extractUsernameFromEmail(authUserPayload.email);

    const upsertData = {
      where: { authUserId: authUserPayload.id },
      update: {
        email: authUserPayload.email,
        displayName: displayNameFromMeta,
        lastLogin: new Date(),
      },
      create: {
        authUserId: authUserPayload.id,
        email: authUserPayload.email,
        displayName: displayNameFromMeta,
        role: UserRole.USER, 
        status: UserStatus.ACTIVE,
        lastLogin: new Date(),
      },
    };

    console.log('[AuthService] Attempting to upsert user with the following data:', JSON.stringify(upsertData, null, 2));

    try {
      const profile = await this.prisma.user.upsert(upsertData);
      console.log(`Profile synced in public.users for authUserId: ${authUserPayload.id}`);
      return profile;
    } catch (dbError: any) {
      console.error(`CRITICAL: Failed to sync profile in public.users for authUserId ${authUserPayload.id}`, dbError);
      if (dbError.code === 'P2002') {
        const target = dbError.meta?.target as string[] | undefined;
        if (target?.includes('email')) {
          throw new ConflictException('Sync failed: This email is already associated with another profile.');
        } else if (target?.includes('authUserId')) {
            throw new ConflictException('Sync failed: This authentication ID is already linked to a profile.');
        } else if (target?.includes('username') && target !== undefined) { 
            throw new ConflictException('Sync failed: This username is already taken.');
        }
        throw new ConflictException('Sync failed: A profile conflict occurred.');
      }
      throw new InternalServerErrorException('Profile synchronization failed.');
    }
  }
}
