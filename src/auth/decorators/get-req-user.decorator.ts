import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { ReqUserObject } from '../strategies/jwt.strategy';

/**
 * Custom decorator to extract the entire ReqUserObject from the request.
 * This object is attached by the JwtAuthGuard and contains authData, dbProfile, and profileExistsInDb.
 * 
 * @example
 * someMethod(@GetReqUser() reqUser: ReqUserObject) {
 *   const jwtPayload = reqUser.authData;
 *   const userProfile = reqUser.dbProfile;
 * }
 */
export const GetReqUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): ReqUserObject => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  },
); 