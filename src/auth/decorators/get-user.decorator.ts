import { createParamDecorator, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ReqUserObject } from '../strategies/jwt.strategy';

export const GetUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    const reqUser = request.user as ReqUserObject;

    // Extract the database profile from the JWT strategy response
    if (!reqUser || !reqUser.dbProfile) {
      throw new UnauthorizedException('User profile not found. Please ensure your account is properly set up.');
    }

    return reqUser.dbProfile; // Return the actual user profile from the database
  },
);