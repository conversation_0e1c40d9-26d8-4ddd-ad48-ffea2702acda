import { Is<PERSON><PERSON>, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginAuthDto {
  @ApiProperty({
    description: 'User\'s email address for login.',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Please enter a valid email address.' })
  @IsNotEmpty({ message: 'Email should not be empty.' })
  email: string;

  @ApiProperty({
    description: 'User\'s password for login.',
    example: 'P@sswOrd123',
    minLength: 8,
  })
  @IsNotEmpty({ message: 'Password should not be empty.' })
  password: string;
} 