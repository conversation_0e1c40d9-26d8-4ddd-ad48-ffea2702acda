import { ApiProperty } from '@nestjs/swagger';
import { UserProfileMinimalDto } from './user-profile-minimal.dto';

// Based on Supabase Session object structure
export class SessionDto {
  @ApiProperty({ description: 'The access token (JWT).', example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' })
  access_token: string;

  @ApiProperty({ description: 'The refresh token.', example: 'long_refresh_token_string' })
  refresh_token: string;

  @ApiProperty({ description: 'The type of token.', example: 'bearer' })
  token_type: string;

  @ApiProperty({ description: 'The number of seconds until the access token expires.', example: 3600 })
  expires_in: number;

  @ApiProperty({ description: 'The Unix timestamp of when the access token expires.', example: 1678886400, required: false })
  expires_at?: number;

  @ApiProperty({ 
    description: 'Minimal user profile associated with this session.',
    type: UserProfileMinimalDto, // Re-use the minimal profile here as Supabase session also contains user info
  })
  user: UserProfileMinimalDto; // Supabase Session object actually nests a User object.
}

export class LoginResponseDto {
  @ApiProperty({
    description: 'A message confirming the outcome of the login attempt.',
    example: 'User logged in successfully.',
  })
  message: string;

  @ApiProperty({
    description: 'Full user profile information upon successful login.',
    type: UserProfileMinimalDto, // This is the main user object returned by our endpoint
  })
  user: UserProfileMinimalDto;

  @ApiProperty({
    description: 'The Supabase session object containing tokens and session details.',
    type: SessionDto,
    nullable: true, // Session can be null if e.g. MFA is required next
  })
  session: SessionDto | null;
} 