import { Is<PERSON><PERSON>, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, MaxLength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class RegisterAuthDto {
  @ApiProperty({
    description: 'User\'s email address for registration.',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Please enter a valid email address.' })
  email!: string;

  @ApiProperty({
    description: 'User\'s password for registration.',
    example: 'StrongP@sswOrd123',
    minLength: 8,
  })
  @IsString({ message: 'Password must be a string.' })
  @MinLength(8, { message: 'Password must be at least 8 characters long.' })
  // Add regex for password complexity if needed, e.g.,
  // @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, { message: 'Password too weak' })
  password!: string;

  @ApiProperty({
    description: 'Optional username for the user. Must be unique.',
    example: 'new_user_123',
    minLength: 3,
    maxLength: 30,
    pattern: '^[a-zA-Z0-9_]+$',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MinLength(3, { message: 'Username must be at least 3 characters long.' })
  @MaxLength(30, { message: 'Username cannot be longer than 30 characters.' })
  @Matches(/^[a-zA-Z0-9_]+$/, {
    message: 'Username can only contain alphanumeric characters and underscores.',
  })
  username?: string;

  @ApiPropertyOptional({
    description: 'Optional display name for the user.',
    example: 'New User',
    minLength: 1,
    maxLength: 50,
  })
  @IsOptional()
  @IsString()
  @MinLength(1, { message: 'Display name must be at least 1 character long.' })
  @MaxLength(50, { message: 'Display name cannot be longer than 50 characters.' })
  display_name?: string;
} 