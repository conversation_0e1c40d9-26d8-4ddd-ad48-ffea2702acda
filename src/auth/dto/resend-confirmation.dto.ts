import { IsEmail, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
 
export class ResendConfirmationDto {
  @ApiProperty({
    description: 'User\'s email address to resend confirmation link to.',
    example: '<EMAIL>',
  })
  @IsEmail({}, { message: 'Please enter a valid email address.' })
  @IsNotEmpty({ message: 'Email should not be empty.' })
  email: string;
} 