import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Validate, ValidatorConstraint, ValidatorConstraintInterface, ValidationArguments } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

@ValidatorConstraint({ name: 'matchPassword', async: false })
export class MatchPasswordConstraint implements ValidatorConstraintInterface {
  validate(value: any, args: ValidationArguments) {
    const [relatedPropertyName] = args.constraints;
    const relatedValue = (args.object as any)[relatedPropertyName];
    return value === relatedValue;
  }

  defaultMessage(args: ValidationArguments) {
    const [relatedPropertyName] = args.constraints;
    return `${args.property} must match ${relatedPropertyName}`;
  }
}

export class ResetPasswordDto {
  @ApiProperty({
    description: 'New password for the user account.',
    example: 'NewStr0ngP@ss!',
    minLength: 8,
  })
  @IsNotEmpty({ message: 'Password should not be empty.' })
  @MinLength(8, { message: 'Password must be at least 8 characters long.' })
  // Add other password complexity rules if desired (e.g., @Matches for regex)
  password: string;

  @ApiProperty({
    description: 'Confirmation of the new password.',
    example: 'NewStr0ngP@ss!',
  })
  @IsNotEmpty({ message: 'Password confirmation should not be empty.' })
  @Validate(MatchPasswordConstraint, ['password'], {
    message: 'Passwords do not match.'
  })
  confirmPassword: string;

  // As discussed, the token is usually handled by Supabase client context
  // If you were to pass it explicitly from frontend:
  // @IsNotEmpty({ message: 'Recovery token should not be empty.' })
  // recoveryToken: string; 
} 