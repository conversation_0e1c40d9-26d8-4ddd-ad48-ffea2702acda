import { ApiProperty } from '@nestjs/swagger';
import { UserProfileMinimalDto } from './user-profile-minimal.dto';

export class SignUpResponseDto {
  @ApiProperty({
    description: 'A message confirming the outcome of the registration attempt.',
    example: 'User registered successfully. Please check your email to confirm your registration.',
  })
  message: string;

  @ApiProperty({
    description: 'Basic profile information of the registered user.',
    type: UserProfileMinimalDto,
  })
  user: UserProfileMinimalDto;
} 