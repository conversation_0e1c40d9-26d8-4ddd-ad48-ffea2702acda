import { ApiProperty } from '@nestjs/swagger';

export class UserProfileMinimalDto {
  @ApiProperty({
    description: 'User ID (UUID)',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  id: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  email: string;

  @ApiProperty({
    description: 'Timestamp of user creation',
    example: '2023-01-15T10:30:00.000Z',
  })
  created_at: string;

  @ApiProperty({
    description: 'Timestamp of last user sign-in',
    example: '2023-01-16T12:00:00.000Z',
    required: false,
  })
  last_sign_in_at?: string | null;

  @ApiProperty({
    description: 'Application-specific metadata. Structure may vary.',
    example: { role: 'user', plan: 'free' },
    required: false,
  })
  app_metadata?: Record<string, any>;

  @ApiProperty({
    description: 'User-specific metadata. Structure may vary.',
    example: { username: 'john_doe', display_name: '<PERSON>' },
    required: false,
  })
  user_metadata?: Record<string, any>;
} 