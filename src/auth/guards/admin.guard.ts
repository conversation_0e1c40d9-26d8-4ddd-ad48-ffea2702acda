import { Injectable, CanActivate, ExecutionContext, ForbiddenException, UnauthorizedException } from '@nestjs/common';
import { JwtAuthGuard } from './jwt-auth.guard';
import { UserRole } from '../../../generated/prisma'; // Removed PublicUserModel import as it's part of ReqUserObject
import { ReqUserObject } from '../strategies/jwt.strategy'; // Import ReqUserObject

@Injectable()
export class AdminGuard extends JwtAuthGuard implements CanActivate {
  // We can call the parent's canActivate to ensure the user is authenticated via JWT first.
  // Then, we add our role check.

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // 1. Ensure user is authenticated via JWT (calls JwtAuthGuard's logic)
    const isAuthenticated = await super.canActivate(context);
    if (!isAuthenticated) {
      // This path should ideally not be hit if JwtAuthGuard throws, but good for robustness
      return false;
    }

    // 2. Get the user object attached by JwtStrategy (which is ReqUserObject)
    const request = context.switchToHttp().getRequest();
    const reqUser = request.user as ReqUserObject; // Cast to ReqUserObject

    // 3. Check if the user has the ADMIN role
    if (reqUser && reqUser.dbProfile && reqUser.dbProfile.role === UserRole.ADMIN) {
      return true;
    }
    
    // Log for debugging if the user is authenticated but not an admin or profile is missing
    if (reqUser && reqUser.dbProfile) {
      console.warn(`[AdminGuard] Access denied. User ${reqUser.dbProfile.id} (AuthID: ${reqUser.authData.sub}) has role: ${reqUser.dbProfile.role}, expected ADMIN.`);
    } else if (reqUser) {
      console.warn(`[AdminGuard] Access denied. User (AuthID: ${reqUser.authData.sub}) has no DB profile. Admin access requires a valid admin profile.`);
    } else {
      // This case should be caught by JwtAuthGuard if !isAuthenticated was true
      console.error('[AdminGuard] Access denied. No user object found on request after JWT authentication step.');
      throw new UnauthorizedException('Authentication failed or user object not found.');
    }

    // 4. If not ADMIN, deny access
    throw new ForbiddenException('You do not have permission to access this resource. Admin role required.');
  }
} 