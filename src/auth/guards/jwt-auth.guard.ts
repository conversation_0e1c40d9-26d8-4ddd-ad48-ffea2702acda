import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Observable } from 'rxjs';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  // Optional: You can override handleRequest to customize error handling or logging
  // By default, if authentication fails (e.g., no token, invalid token), 
  // AuthGuard('jwt') will throw an UnauthorizedException.

  // canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
  //   // Add your custom authentication logic here
  //   // for example, call super.logIn(request) to establish a session.
  //   return super.canActivate(context);
  // }

  // handleRequest(err, user, info, context: ExecutionContext) {
  //   // You can throw an exception based on either "info" or "err" arguments
  //   if (err || !user) {
  //     // Log the error or info for debugging
  //     // console.error('JwtAuthGuard Error:', err, 'Info:', info);
  //     throw err || new UnauthorizedException(info?.message || 'User is not authenticated');
  //   }
  //   return user; // This user object comes from JwtStrategy.validate()
  // }
} 