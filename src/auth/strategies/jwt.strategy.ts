import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
// import { SupabaseJwtPayload } from './supabase-jwt-payload.interface'; // Removed import
import { PrismaService } from '../../prisma/prisma.service'; // Import PrismaService
import { User as PublicUserModel } from '../../../generated/prisma'; // Import User model from public schema

// Define SupabaseJwtPayload interface directly here
export interface SupabaseJwtPayload {
  sub: string; // User ID (from auth.users.id)
  email?: string;
  phone?: string;
  role?: string; // Often 'authenticated'
  aud?: string; // Often 'authenticated'
  exp?: number; // Expiration timestamp (seconds)
  iat?: number; // Issued at timestamp (seconds)
  app_metadata?: { [key: string]: any };
  user_metadata?: { [key: string]: any };
  // Add any other custom claims if configured in Supabase
}

// Define the new ReqUserObject interface
export interface ReqUserObject {
  authData: SupabaseJwtPayload; // The raw Supabase JWT payload
  dbProfile: PublicUserModel | null;   // The profile from public.users (or null)
  profileExistsInDb: boolean;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly configService: ConfigService,
    private readonly prismaService: PrismaService, // Inject PrismaService
  ) {
    const jwtSecret = configService.get<string>('JWT_SECRET');
    if (!jwtSecret) {
      throw new Error('JWT_SECRET environment variable is not set.');
    }
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: jwtSecret,
    });
  }

  async validate(payload: SupabaseJwtPayload): Promise<ReqUserObject> { 
    // console.log('[JwtStrategy] Validating JWT payload:', JSON.stringify(payload, null, 2)); // REMOVE THIS

    const authUserId = payload.sub;
    // console.log(`[JwtStrategy] Validating JWT for authUserId: ${authUserId}`); // Keep this or make less verbose?
    // Keeping a light log for authUserId seems reasonable.

    if (!authUserId) {
      // console.error('[JwtStrategy] Invalid token: Missing subject (sub). Payload:', JSON.stringify(payload, null, 2)); // REMOVE THIS
      console.error('[JwtStrategy] Invalid token: Missing subject (sub).'); // Keep a simpler error log
      throw new UnauthorizedException('Invalid token: missing user identifier.');
    }

    // Find the user in our public.users table using the authUserId
    const userProfile = await this.prismaService.user.findUnique({
      where: { authUserId: authUserId }, // Use the unique authUserId field
    });

    if (userProfile) {
      console.log(`[JwtStrategy] User profile found in DB for authUserId: ${authUserId}, public.users ID: ${userProfile.id}`);
    } else {
      console.log(`[JwtStrategy] User profile NOT found in DB for authUserId: ${authUserId}. This is okay for initial sync.`);
    }

    return {
      authData: payload,
      dbProfile: userProfile, 
      profileExistsInDb: !!userProfile,
    };
  }
} 