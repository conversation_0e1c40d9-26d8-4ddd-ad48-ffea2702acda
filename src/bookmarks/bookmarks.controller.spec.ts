import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { BookmarksController } from './bookmarks.controller';
import { BookmarksService } from './bookmarks.service';
import { PrismaService } from '../prisma/prisma.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { mockPrismaService } from '../test/mocks/prisma.service.mock';
import { CreateBookmarkDto } from './dto/create-bookmark.dto';
import { ListBookmarksDto } from './dto/list-bookmarks.dto';
import { User as UserModel, UserRole, UserStatus, Entity, UserSavedEntity } from '@generated-prisma';

describe('BookmarksController (Integration)', () => {
  let app: INestApplication;
  let bookmarksService: BookmarksService;

  const mockUser: UserModel = {
    id: 'user-123',
    authUserId: 'auth-123',
    email: '<EMAIL>',
    username: 'testuser',
    displayName: 'Test User',
    profilePictureUrl: null,
    role: UserRole.USER,
    status: UserStatus.ACTIVE,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    lastLogin: new Date('2023-01-01'),
    isActive: true,
    bio: null,
    location: null,
    website: null,
    socialLinks: null,
    preferences: null,
    emailVerified: true,
    emailVerifiedAt: new Date('2023-01-01'),
    twoFactorEnabled: false,
    lastPasswordChange: new Date('2023-01-01'),
    loginAttempts: 0,
    lockedUntil: null,
    passwordResetToken: null,
    passwordResetExpires: null,
    emailVerificationToken: null,
    emailVerificationExpires: null,
  };

  const mockEntity: Entity = {
    id: 'entity-123',
    name: 'Test Entity',
    websiteUrl: 'https://example.com',
    entityTypeId: 'type-123',
    shortDescription: 'A test entity',
    description: 'A longer description',
    logoUrl: 'https://example.com/logo.png',
    documentationUrl: 'https://example.com/docs',
    contactUrl: 'https://example.com/contact',
    privacyPolicyUrl: 'https://example.com/privacy',
    foundedYear: 2023,
    status: 'APPROVED' as any,
    socialLinks: { twitter: 'https://twitter.com/test' },
    submitterId: 'user-123',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    metaTitle: null,
    metaDescription: null,
    employeeCountRange: null,
    fundingStage: null,
    locationSummary: null,
    refLink: null,
    affiliateStatus: null,
    scrapedReviewSentimentLabel: null,
    scrapedReviewSentimentScore: null,
    scrapedReviewCount: null,
    averageRating: null,
    totalReviews: 0,
    lastReviewedAt: null,
    featuredScore: null,
    trendingScore: null,
    qualityScore: null,
    popularityScore: null,
    lastScrapedAt: null,
    scrapingEnabled: false,
    adminNotes: null,
    rejectionReason: null,
    approvedAt: null,
    approvedBy: null,
    lastModifiedBy: null,
    slug: 'test-entity',
    searchVector: null,
  };

  const mockBookmark: UserSavedEntity = {
    userId: 'user-123',
    entityId: 'entity-123',
    createdAt: new Date('2023-01-01'),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [BookmarksController],
      providers: [
        BookmarksService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn((context) => {
          const request = context.switchToHttp().getRequest();
          request.user = mockUser;
          return true;
        }),
      })
      .compile();

    app = module.createNestApplication();
    
    // Apply global validation pipe like in main.ts
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }));

    bookmarksService = module.get<BookmarksService>(BookmarksService);
    
    await app.init();
  });

  afterEach(async () => {
    await app.close();
    jest.clearAllMocks();
  });

  describe('POST /bookmarks', () => {
    const createBookmarkDto: CreateBookmarkDto = {
      entity_id: 'entity-123',
    };

    it('should create a bookmark successfully', async () => {
      jest.spyOn(bookmarksService, 'saveBookmark').mockResolvedValue(mockBookmark);

      const response = await request(app.getHttpServer())
        .post('/bookmarks')
        .send(createBookmarkDto)
        .expect(201);

      expect(response.body).toHaveProperty('userId', mockUser.id);
      expect(response.body).toHaveProperty('entityId', createBookmarkDto.entity_id);
      expect(response.body).toHaveProperty('createdAt');
      expect(bookmarksService.saveBookmark).toHaveBeenCalledWith(mockUser.id, createBookmarkDto);
    });

    it('should validate entity_id as UUID', async () => {
      const invalidDto = {
        entity_id: 'invalid-uuid',
      };

      await request(app.getHttpServer())
        .post('/bookmarks')
        .send(invalidDto)
        .expect(400);
    });

    it('should require entity_id field', async () => {
      const emptyDto = {};

      await request(app.getHttpServer())
        .post('/bookmarks')
        .send(emptyDto)
        .expect(400);
    });

    it('should require authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [BookmarksController],
        providers: [
          BookmarksService,
          { provide: PrismaService, useValue: mockPrismaService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .post('/bookmarks')
        .send(createBookmarkDto)
        .expect(403);

      await testApp.close();
    });

    it('should strip non-whitelisted properties', async () => {
      const dtoWithExtraFields = {
        ...createBookmarkDto,
        userId: 'different-user', // Should be stripped
        maliciousField: 'should be removed',
      };

      jest.spyOn(bookmarksService, 'saveBookmark').mockResolvedValue(mockBookmark);

      await request(app.getHttpServer())
        .post('/bookmarks')
        .send(dtoWithExtraFields)
        .expect(201);

      expect(bookmarksService.saveBookmark).toHaveBeenCalledWith(
        mockUser.id,
        expect.not.objectContaining({
          userId: expect.anything(),
          maliciousField: expect.anything(),
        }),
      );
    });

    it('should handle service errors gracefully', async () => {
      jest.spyOn(bookmarksService, 'saveBookmark').mockRejectedValue(
        new Error('Entity not found'),
      );

      await request(app.getHttpServer())
        .post('/bookmarks')
        .send(createBookmarkDto)
        .expect(500);
    });
  });

  describe('DELETE /bookmarks/:entity_id', () => {
    const entityId = 'entity-123';

    it('should remove a bookmark successfully', async () => {
      jest.spyOn(bookmarksService, 'unsaveBookmark').mockResolvedValue(undefined);

      await request(app.getHttpServer())
        .delete(`/bookmarks/${entityId}`)
        .expect(204);

      expect(bookmarksService.unsaveBookmark).toHaveBeenCalledWith(mockUser.id, entityId);
    });

    it('should return 400 for invalid UUID format', async () => {
      await request(app.getHttpServer())
        .delete('/bookmarks/invalid-uuid')
        .expect(400);
    });

    it('should require authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [BookmarksController],
        providers: [
          BookmarksService,
          { provide: PrismaService, useValue: mockPrismaService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .delete(`/bookmarks/${entityId}`)
        .expect(403);

      await testApp.close();
    });

    it('should handle service errors gracefully', async () => {
      jest.spyOn(bookmarksService, 'unsaveBookmark').mockRejectedValue(
        new Error('Bookmark not found'),
      );

      await request(app.getHttpServer())
        .delete(`/bookmarks/${entityId}`)
        .expect(500);
    });
  });

  describe('GET /bookmarks', () => {
    const mockEntityWithRelations = {
      ...mockEntity,
      entityType: {
        id: 'type-123',
        name: 'AI Tool',
        slug: 'ai-tool',
      },
      submitter: {
        id: mockUser.id,
        username: mockUser.username,
        profilePictureUrl: mockUser.profilePictureUrl,
      },
    };

    const mockPaginatedResult = {
      data: [mockEntityWithRelations],
      total: 1,
      page: 1,
      limit: 10,
    };

    it('should return paginated list of bookmarked entities', async () => {
      jest.spyOn(bookmarksService, 'listSavedBookmarks').mockResolvedValue(mockPaginatedResult as any);

      const response = await request(app.getHttpServer())
        .get('/bookmarks')
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('limit');
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0]).toHaveProperty('id', mockEntity.id);
      expect(bookmarksService.listSavedBookmarks).toHaveBeenCalledWith(mockUser.id, expect.any(Object));
    });

    it('should handle query parameters correctly', async () => {
      jest.spyOn(bookmarksService, 'listSavedBookmarks').mockResolvedValue(mockPaginatedResult as any);

      await request(app.getHttpServer())
        .get('/bookmarks')
        .query({
          page: 2,
          limit: 5,
        })
        .expect(200);

      expect(bookmarksService.listSavedBookmarks).toHaveBeenCalledWith(
        mockUser.id,
        expect.objectContaining({
          page: 2,
          limit: 5,
        }),
      );
    });

    it('should validate query parameter types', async () => {
      await request(app.getHttpServer())
        .get('/bookmarks')
        .query({
          page: 'invalid',
          limit: 'invalid',
        })
        .expect(400);
    });

    it('should validate limit maximum value', async () => {
      await request(app.getHttpServer())
        .get('/bookmarks')
        .query({
          limit: 101, // Exceeds maximum of 100
        })
        .expect(400);
    });

    it('should validate minimum values for pagination', async () => {
      await request(app.getHttpServer())
        .get('/bookmarks')
        .query({
          page: 0, // Below minimum of 1
          limit: 0, // Below minimum of 1
        })
        .expect(400);
    });

    it('should return empty results when no bookmarks exist', async () => {
      const emptyResult = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
      };
      jest.spyOn(bookmarksService, 'listSavedBookmarks').mockResolvedValue(emptyResult as any);

      const response = await request(app.getHttpServer())
        .get('/bookmarks')
        .expect(200);

      expect(response.body.data).toHaveLength(0);
      expect(response.body.total).toBe(0);
    });

    it('should require authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [BookmarksController],
        providers: [
          BookmarksService,
          { provide: PrismaService, useValue: mockPrismaService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .get('/bookmarks')
        .expect(403);

      await testApp.close();
    });

    it('should handle service errors gracefully', async () => {
      jest.spyOn(bookmarksService, 'listSavedBookmarks').mockRejectedValue(
        new Error('Database error'),
      );

      await request(app.getHttpServer())
        .get('/bookmarks')
        .expect(500);
    });

    it('should use default pagination values when not provided', async () => {
      jest.spyOn(bookmarksService, 'listSavedBookmarks').mockResolvedValue(mockPaginatedResult as any);

      await request(app.getHttpServer())
        .get('/bookmarks')
        .expect(200);

      expect(bookmarksService.listSavedBookmarks).toHaveBeenCalledWith(
        mockUser.id,
        expect.objectContaining({
          page: 1,
          limit: 10,
        }),
      );
    });
  });
});
