import { Controller, Post, Delete, Get, Body, Param, UseGuards, HttpCode, HttpStatus, Query, ParseUUIDPipe } from '@nestjs/common';
import { BookmarksService } from './bookmarks.service';
import { CreateBookmarkDto } from './dto/create-bookmark.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetUser } from '../auth/decorators/get-user.decorator';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ListBookmarksDto } from './dto/list-bookmarks.dto';
import { User as UserModel } from 'generated/prisma'; // For req.user typing

@ApiTags('Bookmarks')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('bookmarks')
export class BookmarksController {
  constructor(private readonly bookmarksService: BookmarksService) {}

  @Post()
  @ApiOperation({ summary: 'Save (bookmark) an entity' })
  @ApiResponse({ status: 201, description: 'Entity bookmarked successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid input.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Entity not found.' })
  async saveBookmark(
    @GetUser() user: UserModel,
    @Body() createBookmarkDto: CreateBookmarkDto
  ) {
    return this.bookmarksService.saveBookmark(user.id, createBookmarkDto);
  }

  @Delete(':entity_id')
  @ApiOperation({ summary: 'Unsave (unbookmark) an entity' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiResponse({ status: 204, description: 'Entity unbookmarked successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Bookmark or Entity not found.' })
  async unsaveBookmark(
    @GetUser() user: UserModel,
    @Param('entity_id', ParseUUIDPipe) entityId: string
  ) {
    return this.bookmarksService.unsaveBookmark(user.id, entityId);
  }

  @Get()
  @ApiOperation({ summary: 'List all entities bookmarked by the current user' })
  @ApiResponse({ status: 200, description: 'List of bookmarked entities.' /* Consider adding type to response */ })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async listSavedBookmarks(
    @GetUser() user: UserModel,
    @Query() listBookmarksDto: ListBookmarksDto
  ) {
    return this.bookmarksService.listSavedBookmarks(user.id, listBookmarksDto);
  }
} 