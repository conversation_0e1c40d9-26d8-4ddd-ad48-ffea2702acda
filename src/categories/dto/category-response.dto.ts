import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CategoryResponseDto {
  @ApiProperty({ description: 'Category ID (UUID)', example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' })
  id: string;

  @ApiProperty({ description: 'Name of the category', example: 'AI Development Tools' })
  name: string;

  @ApiProperty({ description: 'URL-friendly slug for the category', example: 'ai-development-tools' })
  slug: string;

  @ApiPropertyOptional({ description: 'Detailed description of the category', example: 'Tools and libraries for building AI applications.' })
  description?: string | null;

  @ApiPropertyOptional({ description: 'URL of an icon representing the category', example: 'https://example.com/icons/ai-tools.png' })
  iconUrl?: string | null;

  @ApiPropertyOptional({ description: 'ID of the parent category, if this is a subcategory', example: 'e0f1g2h3-i4j5-6789-0123-456789abcdef' })
  parentCategoryId?: string | null;

  @ApiProperty({ description: 'Timestamp of when the category was created', example: '2023-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the category', example: '2023-01-10T10:00:00.000Z' })
  updatedAt: Date;
} 