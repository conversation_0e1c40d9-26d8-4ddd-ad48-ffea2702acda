import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsInt, IsUrl, MaxLength, MinLength, Matches, IsUUID } from 'class-validator';

export class CreateCategoryDto {
  @ApiProperty({
    description: 'The name of the category.',
    example: 'AI Development Tools',
    minLength: 3,
    maxLength: 100,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(3)
  @MaxLength(100)
  name: string;

  @ApiProperty({
    description: 'The URL-friendly slug for the category. Should be unique. If not provided, it might be generated from the name.',
    example: 'ai-development-tools',
    pattern: '^[a-z0-9]+(?:-[a-z0-9]+)*$',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @Matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, {
    message: 'Slug must be lowercase alphanumeric with hyphens as separators.',
  })
  @MaxLength(100)
  slug: string;

  @ApiPropertyOptional({
    description: 'A brief description of the category.',
    example: 'Tools and frameworks for building AI applications.',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiPropertyOptional({
    description: 'The ID of the parent category (UUID), if this is a sub-category.',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
    type: String,
  })
  @IsOptional()
  @IsUUID('4')
  parent_category_id?: string;

  @ApiPropertyOptional({
    description: 'URL of an icon representing the category.',
    example: 'https://example.com/icons/ai-dev.png',
  })
  @IsOptional()
  @IsUrl()
  icon_url?: string;

  @ApiPropertyOptional({
    description: 'The display order of the category, for sorting purposes.',
    example: 10,
    type: Number,
  })
  @IsOptional()
  @IsInt()
  display_order?: number;
} 