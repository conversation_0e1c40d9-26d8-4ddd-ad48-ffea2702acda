export interface LlmRecommendation {
  recommendedEntityIds: string[];
  explanation: string;
}

export interface CandidateEntity {
  id: string;
  name: string;
  shortDescription: string | null;
  description: string | null;
  entityType: {
    name: string;
    slug: string;
  };
  categories: Array<{
    category: {
      name: string;
      slug: string;
    };
  }>;
  tags: Array<{
    tag: {
      name: string;
      slug: string;
    };
  }>;
  features: Array<{
    feature: {
      name: string;
      slug: string;
    };
  }>;
  // Additional details that might be useful for LLM context
  websiteUrl?: string | null;
  logoUrl?: string | null;
  avgRating?: number;
  reviewCount?: number;
}

export interface ILlmService {
  /**
   * Get AI-powered recommendations based on a problem description and candidate entities
   * @param problemDescription - The user's problem description
   * @param candidateEntities - Array of entities found through vector search
   * @returns Promise containing recommended entity IDs and explanation
   */
  getRecommendation(
    problemDescription: string,
    candidateEntities: CandidateEntity[],
  ): Promise<LlmRecommendation>;
}
