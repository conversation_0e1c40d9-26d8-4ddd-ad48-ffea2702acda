import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class BookDetailsResponseDto {
  @ApiProperty({ description: 'Entity ID (UUID) this detail record is associated with.', example: 'n1o2p3q4-r5s6-7890-1234-567890abcdef' })
  entityId: string;

  @ApiPropertyOptional({
    description: 'Author of the book.',
    example: 'Dr. AI Scholar',
  })
  author?: string | null;

  @ApiPropertyOptional({ description: 'Publisher of the book.', example: 'AI Press International' })
  publisher?: string | null;

  @ApiPropertyOptional({
    description: 'The publication date of the book.',
    example: '2023-10-26T00:00:00.000Z',
    type: String,
    format: 'date-time',
  })
  publicationDate?: Date | null;

  @ApiPropertyOptional({ description: 'ISBN of the book.', example: '978-3-16-148410-0' })
  isbn?: string | null;

  @ApiPropertyOptional({ description: 'Number of pages in the book.', example: 350, type: 'integer' })
  pageCount?: number | null;

  @ApiPropertyOptional({
    description: "The format of the book (e.g., 'hardcover', 'paperback', 'ebook').",
    example: 'hardcover',
  })
  format?: string | null;

  @ApiPropertyOptional({
    description: 'The language the book is written in.',
    example: 'English',
  })
  language?: string | null;

  @ApiProperty({ description: 'Timestamp of when the book details were created', example: '2024-02-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the book details', example: '2024-02-10T10:00:00.000Z' })
  updatedAt: Date;
} 