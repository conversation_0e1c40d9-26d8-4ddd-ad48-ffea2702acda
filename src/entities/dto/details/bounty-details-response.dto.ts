import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class BountyDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'The reward amount for the bounty as a string.',
    example: '1000',
  })
  rewardAmount?: string | null;

  @ApiPropertyOptional({
    description: 'The currency of the reward.',
    example: 'USD',
  })
  rewardCurrency?: string | null;

  @ApiPropertyOptional({
    description: 'The current status of the bounty (e.g., open, in-progress, closed).',
    example: 'open',
  })
  status?: string | null;

  @ApiPropertyOptional({
    description: 'The submission deadline for the bounty.',
    example: '2024-12-31T23:59:59.999Z',
    type: String,
    format: 'date-time',
  })
  deadline?: Date | null;

  @ApiPropertyOptional({
    description: 'The requirements for completing the bounty.',
    type: 'array',
    items: { type: 'string' },
    example: ['Fix a critical security vulnerability.', 'Provide a proof of concept.'],
  })
  requirements?: any | null; // Prisma Json type

  @ApiPropertyOptional({
    description: 'The platform where the bounty is hosted (e.g., HackerOne, Bugcrowd).',
    example: 'HackerOne',
  })
  platform?: string | null;

  @ApiProperty({
    description: 'Timestamp of when the bounty details were created',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the bounty details',
    example: '2024-01-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 