import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CommunityDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'b2c3d4e5-f6g7-8901-2345-678901abcdef',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'The platform where the community is hosted.',
    example: 'Discord',
  })
  platform?: string | null;

  @ApiPropertyOptional({
    description: 'The number of members in the community.',
    example: 15000,
    type: 'integer',
  })
  memberCount?: number | null;

  @ApiPropertyOptional({
    description: 'The main topics of focus for the community.',
    type: [String],
    example: ['AI Development', 'Machine Learning', 'Data Science'],
  })
  focusTopics?: any | null;

  @ApiPropertyOptional({
    description: 'URL to the community rules.',
    example: 'https://example.com/community/rules',
  })
  rulesUrl?: string | null;

  @ApiPropertyOptional({
    description: 'URL to invite new members to the community.',
    example: 'https://discord.gg/example',
  })
  inviteUrl?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the main channel or forum of the community.',
    example: 'https://example.com/community/general',
  })
  mainChannelUrl?: string | null;
} 