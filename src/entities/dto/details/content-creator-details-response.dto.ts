import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ContentCreatorDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'c3d4e5f6-g7h8-9012-3456-789012abcdef',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: "The name of the content creator, if it's a person.",
    example: '<PERSON>',
  })
  creatorName?: string | null;

  @ApiPropertyOptional({
    description: 'The primary platform where the creator publishes content.',
    example: 'YouTube',
  })
  primaryPlatform?: string | null;

  @ApiPropertyOptional({
    description: 'The main focus areas or topics of the content creator.',
    type: [String],
    example: ['AI Tutorials', 'Machine Learning Explained', 'Tech Reviews'],
  })
  focusAreas?: any | null;

  @ApiPropertyOptional({
    description: 'The number of followers the creator has.',
    example: 100000,
    type: 'integer',
  })
  followerCount?: number | null;

  @ApiPropertyOptional({
    description: 'URL to an example of the creator-s content.',
    example: 'https://youtube.com/watch?v=example',
  })
  exampleContentUrl?: string | null;
} 