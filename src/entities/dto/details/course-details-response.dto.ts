import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SkillLevel } from '@generated-prisma';

export class CourseDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'd4e5f6g7-h8i9-0j1k-2l3m-4n5o6p7q8r9s',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'Name of the course instructor.',
    example: 'Dr. <PERSON>',
  })
  instructorName?: string | null;

  @ApiPropertyOptional({
    description: 'The duration of the course in a human-readable format.',
    example: 'Approx. 20 hours',
  })
  durationText?: string | null;

  @ApiPropertyOptional({
    enum: SkillLevel,
    description: 'The skill level required for the course.',
    example: SkillLevel.BEGINNER,
  })
  skillLevel?: SkillLevel | null;

  @ApiPropertyOptional({
    description: 'Any prerequisites for taking the course.',
    example: 'Basic understanding of Python.',
  })
  prerequisites?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the course syllabus.',
    example: 'https://example.com/course/syllabus',
  })
  syllabusUrl?: string | null;

  @ApiPropertyOptional({
    description: 'The number of students enrolled in the course.',
    example: 5000,
    type: 'integer',
  })
  enrollmentCount?: number | null;

  @ApiPropertyOptional({
    description: 'Indicates if a certificate is available upon completion.',
    example: true,
  })
  certificateAvailable?: boolean | null;
} 