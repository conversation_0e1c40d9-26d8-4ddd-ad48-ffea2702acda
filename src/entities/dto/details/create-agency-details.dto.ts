import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsUrl,
  IsArray,
  IsString,
  ArrayMinSize,
} from 'class-validator';

export class CreateAgencyDetailsDto {
  @ApiPropertyOptional({ description: 'Services offered by the agency', type: [String], example: ['AI Strategy', 'ML Model Development'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  services_offered?: string[];

  @ApiPropertyOptional({ description: 'Industries the agency focuses on', type: [String], example: ['Healthcare', 'Finance'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  industry_focus?: string[];

  @ApiPropertyOptional({ description: 'Target client sizes (e.g., Startup, SME, Enterprise)', type: [String], example: ['Startup', 'Enterprise'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  target_client_size?: string[];

  @ApiPropertyOptional({ description: 'Target audience for the agency services', type: [String], example: ['CTOs', 'Product Managers'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  target_audience?: string[];

  @ApiPropertyOptional({ description: 'Summary of agency location(s)', example: 'New York, Remote' })
  @IsOptional()
  @IsString()
  location_summary?: string;

  @ApiPropertyOptional({ description: 'URL to the agency portfolio', example: 'https://agency.com/portfolio' })
  @IsOptional()
  @IsUrl()
  portfolio_url?: string;

  @ApiPropertyOptional({ description: 'General information about agency pricing or engagement models', example: 'Project-based, Retainer options available' })
  @IsOptional()
  @IsString()
  pricing_info?: string;
} 