import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUrl,
  IsArray,
  <PERSON>Int,
  Min,
  Max, // Potentially for publicationYear
} from 'class-validator';

export class CreateBookDetailsDto {
  @ApiPropertyOptional({
    description: 'List of author names',
    example: ['Author A', 'Author B'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  author_names?: string[];

  @ApiPropertyOptional({
    description: 'ISBN of the book',
    example: '978-3-16-148410-0',
  })
  @IsOptional()
  @IsString() // Consider @IsISBN() if available and strict validation is needed
  isbn?: string;

  @ApiPropertyOptional({
    description: 'Publisher of the book',
    example: 'Tech Publishing House',
  })
  @IsOptional()
  @IsString()
  publisher?: string;

  @ApiPropertyOptional({
    description: 'Year the book was published',
    example: 2023,
    type: Number,
  })
  @IsOptional()
  @IsInt()
  @Min(1000) // Assuming books weren't published before year 1000
  @Max(new Date().getFullYear() + 5) // Allow some future years
  publication_year?: number;

  @ApiPropertyOptional({
    description: 'Number of pages in the book',
    example: 350,
    type: Number,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  page_count?: number;

  @ApiPropertyOptional({
    description: 'Summary or abstract of the book',
    example: 'An in-depth look at the future of artificial intelligence...',
  })
  @IsOptional()
  @IsString()
  summary?: string;

  @ApiPropertyOptional({
    description: 'URL where the book can be purchased',
    example: 'https://amazon.com/book-title',
  })
  @IsOptional()
  @IsUrl()
  purchase_url?: string;
} 