import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, IsDateString } from 'class-validator';

export class CreateBountyDetailsDto {
  @ApiPropertyOptional({
    description: 'Issuer of the bounty',
    example: 'OpenAI',
  })
  @IsOptional()
  @IsString()
  bounty_issuer?: string;

  @ApiPropertyOptional({
    description: 'Reward amount for the bounty (e.g., "1000 USD", "0.5 ETH")',
    example: '1000 USD',
  })
  @IsOptional()
  @IsString()
  reward_amount?: string;

  @ApiPropertyOptional({
    description: 'Requirements for completing the bounty',
    example: 'Develop a plugin for X, must meet performance Y.',
  })
  @IsOptional()
  @IsString()
  requirements?: string;

  @ApiPropertyOptional({
    description: 'Submission deadline for the bounty (YYYY-MM-DD)',
    example: '2024-11-30',
    type: String,
  })
  @IsOptional()
  @IsDateString()
  submission_deadline?: Date;

  @ApiPropertyOptional({
    description: 'URL to the platform hosting the bounty (e.g., Gitcoin, HackerOne)',
    example: 'https://gitcoin.co/bounty/123',
  })
  @IsOptional()
  @IsUrl()
  platform_url?: string;

  @ApiPropertyOptional({
    description: 'Difficulty level of the bounty (e.g., Easy, Medium, Hard)',
    example: 'Medium',
  })
  @IsOptional()
  @IsString()
  difficulty_level?: string;
} 