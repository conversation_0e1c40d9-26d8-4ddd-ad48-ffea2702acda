import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsUrl,
  IsArray,
  IsInt,
  Min,
  ArrayMinSize,
} from 'class-validator';

export class CreateCommunityDetailsDto {
  @ApiPropertyOptional({ description: 'Platform where the community is hosted (e.g., Discord, Slack, Forum)', example: 'Discord' })
  @IsOptional()
  @IsString()
  platform?: string;

  @ApiPropertyOptional({ description: 'Number of members in the community', type: Number, minimum: 0, example: 1500 })
  @IsOptional()
  @IsInt()
  @Min(0)
  member_count?: number;

  @ApiPropertyOptional({ description: 'Main focus topics of the community', type: [String], example: ['AI Safety', 'Large Language Models'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  focus_topics?: string[];

  @ApiPropertyOptional({ description: 'URL to the community rules or guidelines', example: 'https://community.com/rules' })
  @IsOptional()
  @IsUrl()
  rules_url?: string;

  @ApiPropertyOptional({ description: 'URL to join the community (invite link)', example: 'https://discord.gg/invitecode' })
  @IsOptional()
  @IsUrl()
  invite_url?: string;

  @ApiPropertyOptional({ description: 'URL to the main channel or landing page of the community', example: 'https://community.com/general' })
  @IsOptional()
  @IsUrl()
  main_channel_url?: string;
} 