import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsUrl,
  IsArray,
  IsInt,
  Min,
  ArrayMinSize,
} from 'class-validator';

export class CreateContentCreatorDetailsDto {
  @ApiPropertyOptional({ description: 'Name of the content creator', example: '<PERSON>' })
  @IsOptional()
  @IsString()
  creator_name?: string;

  @ApiPropertyOptional({ description: 'Primary platform where the creator publishes (e.g., YouTube, Twitch, Blog)', example: 'YouTube' })
  @IsOptional()
  @IsString()
  primary_platform?: string;

  @ApiPropertyOptional({ description: 'Main focus areas or types of content created', type: [String], example: ['AI Tutorials', 'Tech Reviews'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  focus_areas?: string[];

  @ApiPropertyOptional({ description: 'Number of followers or subscribers', type: Number, minimum: 0, example: 100000 })
  @IsOptional()
  @IsInt()
  @Min(0)
  follower_count?: number;

  @ApiPropertyOptional({ description: 'URL to an example piece of content (e.g., a popular video or article)', example: 'https://youtube.com/watch?v=example' })
  @IsOptional()
  @IsUrl()
  example_content_url?: string;
} 