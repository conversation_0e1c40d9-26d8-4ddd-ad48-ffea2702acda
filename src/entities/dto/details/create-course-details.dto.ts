import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  IsBoolean,
  IsInt,
  IsUrl,
  Min,
} from 'class-validator';
import { SkillLevel } from 'generated/prisma';

export class CreateCourseDetailsDto {
  @ApiPropertyOptional({ description: 'Instructor name' })
  @IsOptional()
  @IsString()
  instructor_name?: string;

  @ApiPropertyOptional({ description: 'Duration of the course as text (e.g., "10 hours", "3 weeks")' })
  @IsOptional()
  @IsString()
  duration_text?: string;

  @ApiPropertyOptional({ description: 'Skill level targeted by the course', enum: SkillLevel })
  @IsOptional()
  @IsEnum(SkillLevel)
  skill_level?: SkillLevel;

  @ApiPropertyOptional({ description: 'Prerequisites for the course as a text description' })
  @IsOptional()
  @IsString()
  prerequisites?: string;

  @ApiPropertyOptional({ description: 'URL to the course syllabus' })
  @IsOptional()
  @IsUrl()
  syllabus_url?: string;

  @ApiPropertyOptional({ description: 'Number of students enrolled', type: Number, minimum: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  enrollment_count?: number;

  @ApiPropertyOptional({ description: 'Indicates if a certificate is available upon completion', type: Boolean })
  @IsOptional()
  @IsBoolean()
  certificate_available?: boolean;
} 