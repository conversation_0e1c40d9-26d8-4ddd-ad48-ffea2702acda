import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, IsN<PERSON>ber, IsInt, Min } from 'class-validator';

export class CreateDatasetDetailsDto {
  @ApiPropertyOptional({
    description: 'Format of the dataset (e.g., CSV, JSON, Parquet)',
    example: 'CSV',
  })
  @IsOptional()
  @IsString()
  format?: string;

  @ApiPropertyOptional({
    description: 'URL to the source of the dataset',
    example: 'https://example.com/dataset.zip',
  })
  @IsOptional()
  @IsUrl()
  source_url?: string;

  @ApiPropertyOptional({
    description: 'License of the dataset (e.g., MIT, CC BY 4.0)',
    example: 'CC BY 4.0',
  })
  @IsOptional()
  @IsString()
  license?: string;

  @ApiPropertyOptional({
    description: 'Size of the dataset in bytes',
    example: 104857600, // 100MB
    type: Number,
  })
  @IsOptional()
  @IsNumber() // Using IsNumber to allow for potentially large numbers, IsInt for whole numbers.
  @Min(0)
  size_in_bytes?: number; // Prisma BigInt typically maps to number or string in DTOs

  @ApiPropertyOptional({
    description: 'Detailed description of the dataset',
    example: 'A comprehensive dataset of...',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Notes on how to access or use the dataset',
    example: 'Requires registration. Download link will be emailed.',
  })
  @IsOptional()
  @IsString()
  access_notes?: string;
} 