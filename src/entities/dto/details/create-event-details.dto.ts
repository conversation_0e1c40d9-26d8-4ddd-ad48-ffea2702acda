import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUrl,
  IsArray,
  IsDateString,
  // IsDate, // Alternatively, if Date objects are passed
} from 'class-validator';
// import { Type } from 'class-transformer'; // Needed for @IsDate with @Type

export class CreateEventDetailsDto {
  @ApiPropertyOptional({
    description: 'Type of the event (e.g., Conference, Webinar, Workshop)',
    example: 'Conference',
  })
  @IsOptional()
  @IsString()
  event_type?: string;

  @ApiPropertyOptional({
    description: 'Start date of the event (YYYY-MM-DD or ISO8601 DateTime)',
    example: '2024-09-15T09:00:00Z',
    type: String,
  })
  @IsOptional()
  @IsDateString()
  // @Type(() => Date) // Uncomment if input is a Date object
  // @IsDate()        // Uncomment if input is a Date object
  start_date?: Date;

  @ApiPropertyOptional({
    description: 'End date of the event (YYYY-MM-DD or ISO8601 DateTime)',
    example: '2024-09-17T17:00:00Z',
    type: String,
  })
  @IsOptional()
  @IsDateString()
  // @Type(() => Date) // Uncomment if input is a Date object
  // @IsDate()        // Uncomment if input is a Date object
  end_date?: Date;

  @ApiPropertyOptional({
    description: 'Location of the event (physical address or "Online")',
    example: 'Online',
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({
    description: 'URL for event registration',
    example: 'https://eventbrite.com/event/123',
  })
  @IsOptional()
  @IsUrl()
  registration_url?: string;

  @ApiPropertyOptional({
    description: 'List of speakers (names or objects)',
    example: ['Dr. AI Expert', { name: 'Jane Innovations', topic: 'Future of ML' }],
    type: Array,
  })
  @IsOptional()
  @IsArray()
  speaker_list?: any[];

  @ApiPropertyOptional({
    description: 'URL to the event agenda',
    example: 'https://event.com/agenda',
  })
  @IsOptional()
  @IsUrl()
  agenda_url?: string;

  @ApiPropertyOptional({
    description: 'Price of the event (e.g., "Free", "$99")',
    example: '$99',
  })
  @IsOptional()
  @IsString()
  price?: string;
} 