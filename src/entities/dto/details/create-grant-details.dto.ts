import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, IsDateString } from 'class-validator';

export class CreateGrantDetailsDto {
  @ApiPropertyOptional({
    description: 'Name of the institution providing the grant',
    example: 'AI Research Foundation',
  })
  @IsOptional()
  @IsString()
  granting_institution?: string;

  @ApiPropertyOptional({
    description: 'Criteria for eligibility for the grant',
    example: 'Must be a non-profit organization focused on AI safety.',
  })
  @IsOptional()
  @IsString()
  eligibility_criteria?: string;

  @ApiPropertyOptional({
    description: 'Application deadline for the grant (YYYY-MM-DD)',
    example: '2024-12-31',
    type: String,
  })
  @IsOptional()
  @IsDateString()
  application_deadline?: Date;

  @ApiPropertyOptional({
    description: 'Funding amount or range (e.g., "$10,000 - $50,000")',
    example: '$10,000 - $50,000',
  })
  @IsOptional()
  @IsString()
  funding_amount?: string;

  @ApiPropertyOptional({
    description: 'URL to the grant application page',
    example: 'https://foundation.example/apply-grant',
  })
  @IsOptional()
  @IsUrl()
  application_url?: string;

  @ApiPropertyOptional({
    description: 'Main focus area of the grant (e.g., "AI Ethics Research")',
    example: 'AI Ethics Research',
  })
  @IsOptional()
  @IsString()
  grant_focus_area?: string;
} 