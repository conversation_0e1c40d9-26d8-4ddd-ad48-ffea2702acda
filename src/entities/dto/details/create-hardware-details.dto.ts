import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUrl,
  IsObject,
  IsDateString,
} from 'class-validator';

export class CreateHardwareDetailsDto {
  @ApiPropertyOptional({
    description: 'Type of hardware (e.g., GPU, FPGA, ASIC, TPU)',
    example: 'GPU',
  })
  @IsOptional()
  @IsString()
  hardware_type?: string;

  @ApiPropertyOptional({
    description: 'Technical specifications of the hardware',
    example: { memory: '24GB GDDR6X', cuda_cores: 10496, tflops: 35.6 },
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  specifications?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Manufacturer of the hardware',
    example: 'NVIDIA',
  })
  @IsOptional()
  @IsString()
  manufacturer?: string;

  @ApiPropertyOptional({
    description: 'Release date of the hardware (YYYY-MM-DD)',
    example: '2023-09-20',
    type: String,
  })
  @IsOptional()
  @IsDateString()
  release_date?: Date;

  @ApiPropertyOptional({
    description: 'Price range of the hardware (e.g., "$500 - $1000")',
    example: '$699 - $799',
  })
  @IsOptional()
  @IsString()
  price_range?: string;

  @ApiPropertyOptional({
    description: 'URL to the hardware datasheet',
    example: 'https://nvidia.com/datasheet/rtx4070.pdf',
  })
  @IsOptional()
  @IsUrl()
  datasheet_url?: string;
} 