import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, IsArray, IsEmail } from 'class-validator';

export class CreateInvestorDetailsDto {
  @ApiPropertyOptional({
    description: 'Areas of investment focus',
    example: ['Seed Stage AI', 'Healthcare Tech', 'SaaS'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  investment_focus_areas?: string[];

  @ApiPropertyOptional({
    description: 'URL to the investor portfolio',
    example: 'https://investor.com/portfolio',
  })
  @IsOptional()
  @IsUrl()
  portfolio_url?: string;

  @ApiPropertyOptional({
    description: 'Typical investment size (e.g., "$100k - $1M")',
    example: '$100k - $1M',
  })
  @IsOptional()
  @IsString()
  typical_investment_size?: string;

  @ApiPropertyOptional({
    description: 'Investment stages (e.g., Pre-seed, Seed, Series A)',
    example: ['Pre-seed', 'Seed', 'Series A'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  investment_stages?: string[];

  @ApiPropertyOptional({
    description: 'Contact email for the investor',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsEmail()
  contact_email?: string;

  @ApiPropertyOptional({
    description: 'Preferred method of communication',
    example: 'Email, LinkedIn',
  })
  @IsOptional()
  @IsString()
  preferred_communication?: string;
} 