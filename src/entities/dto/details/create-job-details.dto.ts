import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl } from 'class-validator';

export class CreateJobDetailsDto {
  @ApiPropertyOptional({ description: 'Title of the job', example: 'AI Engineer' })
  @IsOptional()
  @IsString()
  job_title?: string;

  @ApiPropertyOptional({ description: 'Name of the hiring company', example: 'Tech Solutions Inc.' })
  @IsOptional()
  @IsString()
  company_name?: string;

  @ApiPropertyOptional({
    description: 'Location type of the job (e.g., Remote, On-site, Hybrid)',
    example: 'Remote',
  })
  @IsOptional()
  @IsString()
  location_type?: string;

  @ApiPropertyOptional({
    description: 'Salary range for the job (e.g., "$80k - $120k", "Competitive")',
    example: '$80k - $120k',
  })
  @IsOptional()
  @IsString()
  salary_range?: string;

  @ApiPropertyOptional({
    description: 'URL to the job application page',
    example: 'https://jobs.example.com/apply/123',
  })
  @IsOptional()
  @IsUrl()
  application_url?: string;

  @ApiPropertyOptional({
    description: 'Full description of the job responsibilities and requirements',
    example: 'Seeking an experienced AI engineer to develop cutting-edge solutions...',
  })
  @IsOptional()
  @IsString()
  job_description?: string;

  @ApiPropertyOptional({
    description: 'Required experience level (e.g., Entry, Mid, Senior)',
    example: 'Mid-Senior level',
  })
  @IsOptional()
  @IsString()
  experience_level?: string;

  @ApiPropertyOptional({
    description: 'Type of employment (e.g., Full-time, Part-time, Contract)',
    example: 'Full-time',
  })
  @IsOptional()
  @IsString()
  employment_type?: string;
} 