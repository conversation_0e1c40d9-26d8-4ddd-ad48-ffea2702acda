import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, IsN<PERSON>ber, IsObject, Min } from 'class-validator';

export class CreateModelDetailsDto {
  @ApiPropertyOptional({
    description: 'Architecture of the model (e.g., Transformer, CNN, RNN)',
    example: 'Transformer',
  })
  @IsOptional()
  @IsString()
  model_architecture?: string;

  @ApiPropertyOptional({
    description: 'Number of parameters in the model',
    example: 175000000000, // 175 Billion
    type: Number,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  parameters_count?: number; // Prisma BigInt maps to number or string in DTOs

  @ApiPropertyOptional({
    description: 'Information about the training dataset used for the model',
    example: 'Trained on a large corpus of text and code.',
  })
  @IsOptional()
  @IsString()
  training_dataset?: string;

  @ApiPropertyOptional({
    description: 'Performance metrics of the model',
    example: { accuracy: 0.95, f1_score: 0.92 },
    type: 'object',
    additionalProperties: true,
  })
  @IsOptional()
  @IsObject()
  performance_metrics?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'URL to download or access the model (e.g., Hugging Face model hub)',
    example: 'https://huggingface.co/openai-gpt',
  })
  @IsOptional()
  @IsUrl()
  model_url?: string;

  @ApiPropertyOptional({
    description: 'License of the model (e.g., MIT, Apache 2.0)',
    example: 'MIT',
  })
  @IsOptional()
  @IsString()
  license?: string;
} 