import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, IsDateString, IsNotEmpty, MaxLength, IsEnum } from 'class-validator';
import { EntityStatus } from '@generated-prisma';

export class CreateNewsDetailsDto {
  @ApiPropertyOptional({
    description: 'Publication date of the news article (YYYY-MM-DD)',
    example: '2024-03-20',
    type: String,
  })
  @IsOptional()
  @IsDateString()
  publication_date?: Date;

  @ApiPropertyOptional({
    description: 'Name of the source of the news (e.g., TechCrunch, Reuters)',
    example: 'TechCrunch',
  })
  @IsOptional()
  @IsString()
  source_name?: string;

  @ApiPropertyOptional({
    description: 'URL to the news article',
    example: 'https://techcrunch.com/ai-breakthrough',
  })
  @IsOptional()
  @IsUrl()
  articleUrl?: string;

  @ApiPropertyOptional({
    description: 'Author of the news article',
    example: '<PERSON>',
  })
  @IsOptional()
  @IsString()
  author?: string;

  @ApiPropertyOptional({
    description: 'Brief summary of the news article',
    example: 'A new AI model has achieved state-of-the-art results in...',
  })
  @IsOptional()
  @IsString()
  summary?: string;

  @ApiPropertyOptional({
    description: 'Status of the news item.',
    enum: EntityStatus,
    example: EntityStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EntityStatus)
  status?: EntityStatus;
} 