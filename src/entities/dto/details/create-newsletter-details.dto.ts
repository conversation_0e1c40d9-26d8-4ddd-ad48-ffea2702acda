import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsUrl,
  IsArray,
  IsInt,
  Min,
  ArrayMinSize,
} from 'class-validator';

export class CreateNewsletterDetailsDto {
  @ApiPropertyOptional({ description: 'Publishing frequency (e.g., Weekly, Monthly, Bi-weekly)', example: 'Weekly' })
  @IsOptional()
  @IsString()
  frequency?: string;

  @ApiPropertyOptional({ description: 'Main topics covered in the newsletter', type: [String], example: ['AI Research', 'Tech Industry News'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  main_topics?: string[];

  @ApiPropertyOptional({ description: 'URL to the newsletter archive', example: 'https://newsletter.com/archive' })
  @IsOptional()
  @IsUrl()
  archive_url?: string;

  @ApiPropertyOptional({ description: 'URL to subscribe to the newsletter', example: 'https://newsletter.com/subscribe' })
  @IsOptional()
  @IsUrl()
  subscribe_url?: string;

  @ApiPropertyOptional({ description: 'Name of the author or publisher', example: 'AI Insights Team' })
  @IsOptional()
  @IsString()
  author_name?: string;

  @ApiPropertyOptional({ description: 'Number of subscribers', type: Number, minimum: 0, example: 5000 })
  @IsOptional()
  @IsInt()
  @Min(0)
  subscriber_count?: number;
} 