import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUrl,
  IsEnum,
  IsArray,
  IsBoolean,
  IsEmail,
  ArrayMinSize,
} from 'class-validator';
import { PricingModel, PriceRange } from 'generated/prisma';

export class CreatePlatformDetailsDto {
  @ApiPropertyOptional({ description: 'Type of the platform (e.g., PaaS, SaaS, IaaS)', example: 'SaaS' })
  @IsOptional()
  @IsString()
  platform_type?: string;

  @ApiPropertyOptional({ description: 'Key services offered by the platform', type: [String], example: ['Model Training', 'Data Annotation'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  key_services?: string[];

  @ApiPropertyOptional({ description: 'URL to the platform documentation', example: 'https://platform.com/docs' })
  @IsOptional()
  @IsUrl()
  documentation_url?: string;

  @ApiPropertyOptional({ description: 'Pricing model of the platform', enum: PricingModel, example: PricingModel.SUBSCRIPTION })
  @IsOptional()
  @IsEnum(PricingModel)
  pricing_model?: PricingModel;

  @ApiPropertyOptional({ description: 'URL to the Service Level Agreement (SLA)', example: 'https://platform.com/sla' })
  @IsOptional()
  @IsUrl()
  sla_url?: string;

  @ApiPropertyOptional({ description: 'Supported regions or availability zones', type: [String], example: ['us-east-1', 'eu-west-2'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  supported_regions?: string[];

  // Shared fields
  @ApiPropertyOptional({ description: 'Does the platform offer a free tier?', example: true })
  @IsOptional()
  @IsBoolean()
  has_free_tier?: boolean;

  @ApiPropertyOptional({ description: 'Potential use cases for the platform', type: [String], example: ['Enterprise AI Solutions', 'Startup AI Development'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  use_cases?: string[];

  @ApiPropertyOptional({ description: 'General price range', enum: PriceRange, example: PriceRange.MEDIUM })
  @IsOptional()
  @IsEnum(PriceRange)
  price_range?: PriceRange;

  @ApiPropertyOptional({ description: 'Specific details about pricing tiers or structure', example: 'Basic: $99/month, Pro: $299/month' })
  @IsOptional()
  @IsString()
  pricing_details?: string;

  @ApiPropertyOptional({ description: 'URL to the pricing page', example: 'https://platform.com/pricing' })
  @IsOptional()
  @IsUrl()
  pricing_url?: string;

  @ApiPropertyOptional({ description: 'List of integrations with other tools or platforms', type: [String], example: ['Slack', 'GitHub'] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  integrations?: string[];

  @ApiPropertyOptional({ description: 'Email address for support inquiries', example: '<EMAIL>' })
  @IsOptional()
  @IsEmail()
  support_email?: string;

  @ApiPropertyOptional({ description: 'Does the platform offer live chat support?', example: false })
  @IsOptional()
  @IsBoolean()
  has_live_chat?: boolean;

  @ApiPropertyOptional({ description: 'URL to the community forum or support page', example: 'https://community.platform.com' })
  @IsOptional()
  @IsUrl()
  community_url?: string;
} 