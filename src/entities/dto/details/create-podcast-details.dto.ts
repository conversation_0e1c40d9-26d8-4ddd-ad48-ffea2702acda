import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, IsArray } from 'class-validator';

export class CreatePodcastDetailsDto {
  @ApiPropertyOptional({
    description: 'List of host names for the podcast',
    example: ['Host One', 'Host Two'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  host_names?: string[];

  @ApiPropertyOptional({
    description: 'Average length of an episode (e.g., "45 minutes")',
    example: '45 minutes',
  })
  @IsOptional()
  @IsString()
  average_episode_length?: string;

  @ApiPropertyOptional({
    description: 'Main topics covered by the podcast',
    example: ['AI Ethics', 'ML Research', 'Tech News'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  main_topics?: string[];

  @ApiPropertyOptional({
    description: 'URL where the podcast can be listened to (e.g., Spotify, Apple Podcasts)',
    example: 'https://open.spotify.com/show/podcastid',
  })
  @IsOptional()
  @IsUrl()
  listen_url?: string;

  @ApiPropertyOptional({
    description: 'Frequency of new episodes (e.g., "Weekly", "Bi-weekly")',
    example: 'Weekly',
  })
  @IsOptional()
  @IsString()
  frequency?: string;

  @ApiPropertyOptional({
    description: 'Primary language of the podcast',
    example: 'English',
    default: 'English',
  })
  @IsOptional()
  @IsString()
  primary_language?: string;
} 