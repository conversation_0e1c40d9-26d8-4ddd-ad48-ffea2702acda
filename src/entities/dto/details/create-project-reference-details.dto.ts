import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, IsArray } from 'class-validator';

export class CreateProjectReferenceDetailsDto {
  @ApiPropertyOptional({
    description: 'Status of the project (e.g., active, completed, archived)',
    example: 'active',
  })
  @IsOptional()
  @IsString()
  project_status?: string;

  @ApiPropertyOptional({
    description: 'URL to the source code repository',
    example: 'https://github.com/user/project',
  })
  @IsOptional()
  @IsUrl()
  source_code_url?: string;

  @ApiPropertyOptional({
    description: 'URL to the live demo of the project',
    example: 'https://project-demo.com',
  })
  @IsOptional()
  @IsUrl()
  live_demo_url?: string;

  @ApiPropertyOptional({
    description: 'List of technologies used in the project',
    example: ['React', 'Node.js', 'PostgreSQL'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  technologies?: string[];

  @ApiPropertyOptional({
    description: 'Goals of the project',
    example: 'To demonstrate advanced AI capabilities in X domain.',
  })
  @IsOptional()
  @IsString()
  project_goals?: string;

  @ApiPropertyOptional({
    description: 'List of contributors (names or objects)',
    example: ['Jane Doe', { name: 'John Smith', role: 'Lead Developer' }],
    type: Array, // Can be string[] or object[]
  })
  @IsOptional()
  @IsArray()
  contributors?: any[]; // Allows for array of strings or objects
} 