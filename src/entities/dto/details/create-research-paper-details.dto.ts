import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUrl,
  IsInt,
  Min,
  IsArray,
  IsDateString, // Suitable for YYYY-MM-DD date string input
  // IsDate, // Alternatively, if Date objects are passed
} from 'class-validator';
// import { Type } from 'class-transformer'; // Needed if using @IsDate with @Type(() => Date)

export class CreateResearchPaperDetailsDto {
  @ApiPropertyOptional({
    description: 'Publication date of the research paper (YYYY-MM-DD)',
    example: '2023-03-15',
    type: String, // Or Date if using @IsDate
  })
  @IsOptional()
  @IsDateString() // Validates ISO8601 date string e.g. YYYY-MM-DD
  // @Type(() => Date) // Uncomment if input is a Date object
  // @IsDate()        // Uncomment if input is a Date object
  publication_date?: Date; // Or string if IsDateString is preferred for input

  @ApiPropertyOptional({
    description: 'Digital Object Identifier (DOI) of the paper',
    example: '10.1000/xyz123',
  })
  @IsOptional()
  @IsString()
  doi?: string;

  @ApiPropertyOptional({
    description: 'List of authors',
    example: ['Author One', 'Author Two'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  authors?: string[];

  @ApiPropertyOptional({
    description: 'Abstract of the research paper',
    example: 'This paper explores...',
  })
  @IsOptional()
  @IsString()
  abstract?: string;

  @ApiPropertyOptional({
    description: 'Name of the journal or conference',
    example: 'Journal of AI Research',
  })
  @IsOptional()
  @IsString()
  journal_or_conference?: string;

  @ApiPropertyOptional({
    description: 'URL to the publication',
    example: 'https://arxiv.org/abs/1234.56789',
  })
  @IsOptional()
  @IsUrl()
  publication_url?: string;

  @ApiPropertyOptional({
    description: 'Number of citations',
    example: 150,
    type: Number,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  citation_count?: number;
} 