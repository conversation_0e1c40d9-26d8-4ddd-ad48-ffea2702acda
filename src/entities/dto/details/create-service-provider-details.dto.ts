import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, IsArray } from 'class-validator';

export class CreateServiceProviderDetailsDto {
  @ApiPropertyOptional({
    description: 'Areas of service provided',
    example: ['AI Development', 'Data Science Consulting', 'MLOps'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  service_areas?: string[];

  @ApiPropertyOptional({
    description: 'URL to case studies',
    example: 'https://serviceprovider.com/case-studies',
  })
  @IsOptional()
  @IsUrl()
  case_studies_url?: string;

  @ApiPropertyOptional({
    description: 'URL for booking a consultation',
    example: 'https://serviceprovider.com/book-consultation',
  })
  @IsOptional()
  @IsUrl()
  consultation_booking_url?: string;

  @ApiPropertyOptional({
    description: 'Industry specializations',
    example: ['Healthcare', 'Finance'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  industry_specializations?: string[];

  @ApiPropertyOptional({
    description: 'Target company size (e.g., Startups, SMEs, Enterprise)',
    example: 'SMEs',
  })
  @IsOptional()
  @IsString()
  company_size_focus?: string;

  @ApiPropertyOptional({
    description: 'Typical hourly rate range (e.g., "$100-$200")',
    example: '$100-$200',
  })
  @IsOptional()
  @IsString()
  hourly_rate_range?: string;
} 