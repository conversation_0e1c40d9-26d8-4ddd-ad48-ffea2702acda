import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUrl,
  IsArray,
  IsBoolean,
  IsEnum,
  IsDateString,
} from 'class-validator';
import { <PERSON><PERSON>ingModel, PriceRange } from 'generated/prisma';

export class CreateSoftwareDetailsDto {
  @ApiPropertyOptional({ description: 'URL of the software repository' })
  @IsOptional()
  @IsUrl()
  repository_url?: string;

  @ApiPropertyOptional({ description: 'Type of license (e.g., MIT, GPL, Proprietary)' })
  @IsOptional()
  @IsString()
  license_type?: string;

  @ApiPropertyOptional({ description: 'List of programming languages used', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  programming_languages?: string[];

  @ApiPropertyOptional({ description: 'List of compatible platforms', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  platform_compatibility?: string[];

  @ApiPropertyOptional({ description: 'Current version of the software' })
  @IsOptional()
  @IsString()
  current_version?: string;

  @ApiPropertyOptional({ description: 'Release date of the software (YYYY-MM-DD)', type: String })
  @IsOptional()
  @IsDateString()
  release_date?: Date; // Or string

  // Shared fields
  @ApiPropertyOptional({ description: 'Does the software have a free tier?', type: Boolean })
  @IsOptional()
  @IsBoolean()
  has_free_tier?: boolean;

  @ApiPropertyOptional({ description: 'List of use cases for the software', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  use_cases?: string[];

  @ApiPropertyOptional({ description: 'Pricing model', enum: PricingModel })
  @IsOptional()
  @IsEnum(PricingModel)
  pricing_model?: PricingModel;

  @ApiPropertyOptional({ description: 'Price range', enum: PriceRange })
  @IsOptional()
  @IsEnum(PriceRange)
  price_range?: PriceRange;

  @ApiPropertyOptional({ description: 'Detailed pricing information' })
  @IsOptional()
  @IsString()
  pricing_details?: string;

  @ApiPropertyOptional({ description: 'URL to the pricing page' })
  @IsOptional()
  @IsUrl()
  pricing_url?: string;

  @ApiPropertyOptional({ description: 'List of integrations', type: [String] })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  integrations?: string[];

  @ApiPropertyOptional({ description: 'Support email address' })
  @IsOptional()
  @IsString() // Could be @IsEmail() if strictly an email
  support_email?: string;

  @ApiPropertyOptional({ description: 'Does the software offer live chat support?', type: Boolean })
  @IsOptional()
  @IsBoolean()
  has_live_chat?: boolean;

  @ApiPropertyOptional({ description: 'URL to the community forum or support page' })
  @IsOptional()
  @IsUrl()
  community_url?: string;
} 