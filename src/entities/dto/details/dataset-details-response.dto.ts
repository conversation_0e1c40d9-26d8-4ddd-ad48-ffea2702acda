import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class DatasetDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'e5f6g7h8-i9j0-k1l2-m3n4-o5p6q7r8s9t0',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: "The format of the dataset (e.g., 'CSV', 'JSON', 'Parquet').",
    example: 'CSV',
  })
  format?: string | null;

  @ApiPropertyOptional({
    description: "The size of the dataset in a human-readable format (e.g., '1.2 GB', '1M rows').",
    example: '1.2 GB',
  })
  size?: string | null;

  @ApiPropertyOptional({
    description: 'The number of records or rows in the dataset.',
    example: 1000000,
    type: 'integer',
  })
  numRecords?: number | null;

  @ApiPropertyOptional({
    description: 'URL to access the dataset, perhaps an API endpoint or landing page.',
    example: 'https://example.com/dataset/access',
  })
  accessUrl?: string | null;

  @ApiPropertyOptional({
    description: 'Direct URL to download the dataset file.',
    example: 'https://example.com/dataset.csv',
  })
  downloadUrl?: string | null;

  @ApiPropertyOptional({
    description: "The license of the dataset (e.g., 'MIT', 'CC BY 4.0').",
    example: 'MIT',
  })
  license?: string | null;

  @ApiPropertyOptional({
    description: "How frequently the dataset is updated (e.g., 'daily', 'monthly', 'not updated').",
    example: 'monthly',
  })
  updateFrequency?: string | null;

  @ApiProperty({
    description: 'Timestamp of when the dataset details were created',
    example: '2024-03-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the dataset details',
    example: '2024-03-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 