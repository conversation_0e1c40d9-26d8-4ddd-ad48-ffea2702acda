import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class EventDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'f6g7h8i9-j0k1-l2m3-n4o5-p6q7r8s9t0u1',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: "The format of the event (e.g., 'virtual', 'in-person', 'hybrid').",
    example: 'virtual',
  })
  format?: string | null;

  @ApiPropertyOptional({
    description: 'The start date of the event.',
    example: '2024-09-01T09:00:00.000Z',
    type: String,
    format: 'date-time',
  })
  startDate?: Date | null;

  @ApiPropertyOptional({
    description: 'The end date of the event.',
    example: '2024-09-03T17:00:00.000Z',
    type: String,
    format: 'date-time',
  })
  endDate?: Date | null;

  @ApiPropertyOptional({
    description: 'The location of the event (can be a physical address or "Online").',
    example: 'Online',
  })
  location?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the event registration page.',
    example: 'https://example.com/event/register',
  })
  registrationUrl?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the event schedule or agenda.',
    example: 'https://example.com/event/schedule',
  })
  scheduleUrl?: string | null;

  @ApiPropertyOptional({
    description: 'Information about the speakers at the event.',
    example: [{ name: 'John Doe', topic: 'AI in 2025' }, 'Jane Smith'],
  })
  speakerInfo?: any | null;

  @ApiPropertyOptional({
    description: 'The price of the event.',
    example: '$99',
  })
  price?: string | null;

  @ApiProperty({
    description: 'Timestamp of when the event details were created',
    example: '2024-04-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the event details',
    example: '2024-04-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 