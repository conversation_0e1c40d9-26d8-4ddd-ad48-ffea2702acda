import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class GrantDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'g7h8i9j0-k1l2-m3n4-o5p6-q7r8s9t0u1v2',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'The funding amount of the grant.',
    example: '$10,000 - $50,000',
  })
  fundingAmount?: string | null;

  @ApiPropertyOptional({
    description: "The type of funding (e.g., 'equity-free', 'research').",
    example: 'research',
  })
  fundingType?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the grant application page.',
    example: 'https://example.com/grant/apply',
  })
  applicationUrl?: string | null;

  @ApiPropertyOptional({
    description: 'The application deadline for the grant.',
    example: '2025-01-15T23:59:59.999Z',
    type: String,
    format: 'date-time',
  })
  deadline?: Date | null;

  @ApiPropertyOptional({
    description: 'The eligibility criteria for the grant.',
    type: 'array',
    items: { type: 'string' },
    example: ['Must be a non-profit organization.', 'Project must be open-source.'],
  })
  eligibilityCriteria?: any | null;

  @ApiPropertyOptional({
    description: 'The geographic regions the grant focuses on.',
    type: 'array',
    items: { type: 'string' },
    example: ['Global', 'North America'],
  })
  regionFocus?: any | null;

  @ApiProperty({
    description: 'Timestamp of when the grant details were created',
    example: '2024-05-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the grant details',
    example: '2024-05-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 