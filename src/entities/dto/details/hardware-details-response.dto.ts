import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class HardwareDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'h8i9j0k1-l2m3-n4o5-p6q7-r8s9t0u1v2w3',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'The main processor or CPU of the hardware.',
    example: 'Intel Core i9-13900K',
  })
  processor?: string | null;

  @ApiPropertyOptional({
    description: 'The amount and type of memory (RAM).',
    example: '32GB DDR5',
  })
  memory?: string | null;

  @ApiPropertyOptional({
    description: 'The storage capacity and type.',
    example: '2TB NVMe SSD',
  })
  storage?: string | null;

  @ApiPropertyOptional({
    description: 'The graphics processing unit (GPU).',
    example: 'NVIDIA GeForce RTX 4090',
  })
  gpu?: string | null;

  @ApiPropertyOptional({
    description: 'Available ports on the hardware.',
    type: 'array',
    items: { type: 'string' },
    example: ['USB-C', 'HDMI 2.1', 'DisplayPort 1.4'],
  })
  ports?: any | null;

  @ApiPropertyOptional({
    description: 'The power consumption of the hardware.',
    example: '450W',
  })
  powerConsumption?: string | null;

  @ApiPropertyOptional({
    description: 'The physical dimensions of the hardware.',
    example: '304mm x 137mm x 61mm',
  })
  dimensions?: string | null;

  @ApiPropertyOptional({
    description: 'The weight of the hardware.',
    example: '2.5kg',
  })
  weight?: string | null;

  @ApiPropertyOptional({
    description: 'The release date of the hardware.',
    example: '2022-09-20T00:00:00.000Z',
    type: String,
    format: 'date-time',
  })
  releaseDate?: Date | null;

  @ApiProperty({
    description: 'Timestamp of when the hardware details were created',
    example: '2024-06-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the hardware details',
    example: '2024-06-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 