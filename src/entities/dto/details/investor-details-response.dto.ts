import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class InvestorDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'i9j0k1l2-m3n4-o5p6-q7r8-s9t0u1v2w3x4',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: "The investment stages the investor focuses on (e.g., 'seed', 'series-a').",
    type: 'array',
    items: { type: 'string' },
    example: ['Seed', 'Series A'],
  })
  investmentStage?: any | null;

  @ApiPropertyOptional({
    description: 'The number of companies in the investor portfolio.',
    example: 50,
    type: 'integer',
  })
  portfolioSize?: number | null;

  @ApiPropertyOptional({
    description: 'The typical check size for investments.',
    example: '$100k - $1M',
  })
  checkSize?: string | null;

  @ApiPropertyOptional({
    description: 'The focus areas of the investor.',
    type: 'array',
    items: { type: 'string' },
    example: ['AI/ML', 'SaaS', 'Fintech'],
  })
  focusAreas?: any | null;

  @ApiPropertyOptional({
    description: 'Notable investments made by the investor.',
    type: 'array',
    items: { type: 'string' },
    example: ['OpenAI', 'Acme Corp'],
  })
  notableInvestments?: any | null;

  @ApiPropertyOptional({
    description: 'The primary contact person at the investment firm.',
    example: 'Jane Doe',
  })
  contactPerson?: string | null;

  @ApiPropertyOptional({
    description: 'The contact email of the investor.',
    example: '<EMAIL>',
  })
  contactEmail?: string | null;

  @ApiProperty({
    description: 'Timestamp of when the investor details were created',
    example: '2024-07-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the investor details',
    example: '2024-07-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 