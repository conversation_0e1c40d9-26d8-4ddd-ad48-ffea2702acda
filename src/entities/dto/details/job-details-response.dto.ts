import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class JobDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'j1k2l3m4-n5o6-p7q8-r9s0-t1u2v3w4x5y6',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'The name of the company.',
    example: 'Awesome AI Inc.',
  })
  companyName?: string | null;

  @ApiPropertyOptional({
    description: 'The location of the job (e.g., "Remote", "New York, NY").',
    example: 'Remote',
  })
  location?: string | null;

  @ApiPropertyOptional({
    description: 'The salary range for the job.',
    example: '$80k - $120k',
  })
  salaryRange?: string | null;

  @ApiPropertyOptional({
    description: "The type of job (e.g., 'full-time', 'part-time', 'contract').",
    example: 'Full-time',
  })
  jobType?: string | null;

  @ApiPropertyOptional({
    description: 'The experience level required for the job.',
    example: 'Mid-Senior',
  })
  experienceLevel?: string | null;

  @ApiPropertyOptional({
    description: 'The responsibilities of the job.',
    type: 'array',
    items: { type: 'string' },
    example: ['Developing and deploying AI models.', 'Collaborating with product teams.'],
  })
  responsibilities?: any | null;

  @ApiPropertyOptional({
    description: 'The qualifications for the job.',
    type: 'array',
    items: { type: 'string' },
    example: ['3+ years of experience with Python.', 'Experience with TensorFlow or PyTorch.'],
  })
  qualifications?: any | null;

  @ApiPropertyOptional({
    description: 'URL to the job application page.',
    example: 'https://example.com/job/apply',
  })
  applyUrl?: string | null;

  @ApiProperty({
    description: 'Timestamp of when the job details were created',
    example: '2024-08-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the job details',
    example: '2024-08-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 