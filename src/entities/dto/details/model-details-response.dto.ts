import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ModelDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'k2l3m4n5-o6p7-q8r9-s0t1-u2v3w4x5y6z7',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'The architecture of the model.',
    example: 'Transformer',
  })
  architecture?: string | null;

  @ApiPropertyOptional({
    description: 'The context length of the model.',
    example: '8192 tokens',
  })
  contextLength?: string | null;

  @ApiPropertyOptional({
    description: 'The developer or organization behind the model.',
    example: 'OpenAI',
  })
  developer?: string | null;

  @ApiPropertyOptional({
    description: 'The dataset used to train the model.',
    example: 'Common Crawl, WebText2',
  })
  trainingData?: string | null;

  @ApiPropertyOptional({
    description: 'The number of parameters in the model.',
    example: '175B',
  })
  parameters?: string | null;

  @ApiPropertyOptional({
    description: 'Common use cases for the model.',
    type: 'array',
    items: { type: 'string' },
    example: ['Text generation', 'Translation', 'Summarization'],
  })
  useCases?: any | null;

  @ApiPropertyOptional({
    description: 'URL to download or access the model.',
    example: 'https://huggingface.co/openai/gpt-3',
  })
  accessUrl?: string | null;

  @ApiPropertyOptional({
    description: 'The license of the model.',
    example: 'MIT',
  })
  license?: string | null;

  @ApiProperty({
    description: 'Timestamp of when the model details were created',
    example: '2024-09-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the model details',
    example: '2024-09-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 