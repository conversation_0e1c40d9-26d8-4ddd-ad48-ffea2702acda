import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class NewsDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'l3m4n5o6-p7q8-r9s0-t1u2-v3w4x5y6z7a8',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'The author of the news article.',
    example: '<PERSON>',
  })
  author?: string | null;

  @ApiPropertyOptional({
    description: 'The name of the source of the news.',
    example: 'TechCrunch',
  })
  source?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the news article.',
    example: 'https://techcrunch.com/2024/10/01/ai-breakthrough/',
  })
  articleUrl?: string | null;

  @ApiPropertyOptional({
    description: 'The publication date of the news.',
    example: '2024-10-01T00:00:00.000Z',
    type: String,
    format: 'date-time',
  })
  publishedDate?: Date | null;

  @ApiProperty({
    description: 'Timestamp of when the news details were created',
    example: '2024-10-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the news details',
    example: '2024-10-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 