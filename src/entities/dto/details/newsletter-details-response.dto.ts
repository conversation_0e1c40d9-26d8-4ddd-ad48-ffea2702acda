import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class NewsletterDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'n4o5p6q7-r8s9-t0u1-v2w3-x4y5z6a7b8c9',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'The frequency of the newsletter.',
    example: 'Weekly',
  })
  frequency?: string | null;

  @ApiPropertyOptional({
    description: 'The main topics of the newsletter.',
    type: [String],
    example: ['AI News', 'Machine Learning', 'Data Science'],
  })
  mainTopics?: any | null;

  @ApiPropertyOptional({
    description: 'URL to the archive of the newsletter.',
    example: 'https://example.com/newsletter/archive',
  })
  archiveUrl?: string | null;

  @ApiPropertyOptional({
    description: 'URL to subscribe to the newsletter.',
    example: 'https://example.com/newsletter/subscribe',
  })
  subscribeUrl?: string | null;

  @ApiPropertyOptional({
    description: 'The name of the author of the newsletter.',
    example: '<PERSON>',
  })
  authorName?: string | null;

  @ApiPropertyOptional({
    description: 'The number of subscribers to the newsletter.',
    example: 10000,
    type: 'integer',
  })
  subscriberCount?: number | null;
} 