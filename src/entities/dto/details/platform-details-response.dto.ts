import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PricingModel, PriceRange } from '@generated-prisma';

export class PlatformDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'o5p6q7r8-s9t0-u1v2-w3x4-y5z6a7b8c9d0',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'Key features of the platform.',
    type: 'array',
    items: { type: 'string' },
    example: ['Model Training', 'Data Annotation', 'Deployment'],
  })
  keyFeatures?: any | null;

  @ApiPropertyOptional({
    description: 'Common use cases for the platform.',
    type: 'array',
    items: { type: 'string' },
    example: ['Building and deploying AI applications', 'Managing ML workflows'],
  })
  useCases?: any | null;

  @ApiPropertyOptional({
    description: 'Known integrations with other tools or services.',
    type: 'array',
    items: { type: 'string' },
    example: ['GitHub', 'GitLab', 'Bitbucket'],
  })
  integrations?: any | null;

  @ApiPropertyOptional({
    description: 'Target audience for the platform.',
    type: 'array',
    items: { type: 'string' },
    example: ['Data Scientists', 'ML Engineers'],
  })
  targetAudience?: any | null;

  @ApiPropertyOptional({
    description: 'Options for deploying applications on the platform.',
    type: 'array',
    items: { type: 'string' },
    example: ['Cloud', 'On-premise'],
  })
  deploymentOptions?: any | null;

  @ApiPropertyOptional({
    description: 'Operating systems supported by the platform or its tools.',
    type: 'array',
    items: { type: 'string' },
    example: ['Linux', 'Windows', 'macOS'],
  })
  supportedOs?: any | null;

  @ApiPropertyOptional({
    description: 'Indicates if the platform has mobile support.',
    example: false,
  })
  mobileSupport?: boolean | null;

  @ApiPropertyOptional({
    description: 'Indicates if the platform provides API access.',
    example: true,
  })
  apiAccess?: boolean | null;

  @ApiPropertyOptional({
    description: 'Indicates if the platform has a free tier.',
    example: true,
  })
  hasFreeTier?: boolean | null;

  @ApiPropertyOptional({
    enum: PricingModel,
    description: 'The pricing model of the platform.',
    example: PricingModel.PAY_PER_USE,
  })
  pricingModel?: PricingModel | null;

  @ApiPropertyOptional({
    enum: PriceRange,
    description: 'The price range of the platform.',
    example: PriceRange.MEDIUM,
  })
  priceRange?: PriceRange | null;

  @ApiPropertyOptional({
    description: 'Specific details about pricing.',
    example: 'Based on usage, starting at $0.01 per API call.',
  })
  pricingDetails?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the pricing page of the platform.',
    example: 'https://example.com/platform/pricing',
  })
  pricingUrl?: string | null;

  @ApiPropertyOptional({
    description: 'Available support channels.',
    type: 'array',
    items: { type: 'string' },
    example: ['Email', 'Community Forum'],
  })
  supportChannels?: any | null;

  @ApiPropertyOptional({
    description: 'Contact email for support.',
    example: '<EMAIL>',
  })
  supportEmail?: string | null;

  @ApiPropertyOptional({
    description: 'Indicates if live chat support is available.',
    example: false,
  })
  hasLiveChat?: boolean | null;

  @ApiPropertyOptional({
    description: 'URL to the community forum or Discord.',
    example: 'https://example.com/platform/community',
  })
  communityUrl?: string | null;

  @ApiProperty({
    description: 'Timestamp of when the platform details were created',
    example: '2024-11-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the platform details',
    example: '2024-11-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 