import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ProjectReferenceDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'q7r8s9t0-u1v2-w3x4-y5z6-a7b8c9d0e1f2',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'The problem statement the project addresses.',
    example: 'Existing solutions for X are too slow.',
  })
  problemStatement?: string | null;

  @ApiPropertyOptional({
    description: 'An overview of the solution provided by the project.',
    example: 'We built a new pipeline using Y that improves performance by 50%.',
  })
  solutionOverview?: string | null;

  @ApiPropertyOptional({
    description: 'The goals of the project.',
    type: 'array',
    items: { type: 'string' },
    example: ['Demonstrate new AI model capabilities', 'Achieve 99% accuracy'],
  })
  projectGoals?: any | null;

  @ApiPropertyOptional({
    description: 'Key features of the project.',
    type: 'array',
    items: { type: 'string' },
    example: ['Real-time processing', 'Scalable architecture'],
  })
  keyFeatures?: any | null;

  @ApiPropertyOptional({
    description: 'The technologies used in the project.',
    type: 'array',
    items: { type: 'string' },
    example: ['React', 'Node.js', 'PostgreSQL'],
  })
  technologiesUsed?: any | null;

  @ApiPropertyOptional({
    description: 'URL to a live version of the project.',
    example: 'https://example.com/project/live',
  })
  liveUrl?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the source code repository of the project.',
    example: 'https://github.com/example/project',
  })
  repoUrl?: string | null;

  @ApiProperty({
    description: 'Timestamp of when the project reference details were created',
    example: '2025-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the project reference details',
    example: '2025-01-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 