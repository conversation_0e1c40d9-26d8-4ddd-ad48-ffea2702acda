import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ResearchPaperDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 'r8s9t0u1-v2w3-x4y5-z6a7-b8c9d0e1f2g3',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'The authors of the research paper.',
    type: 'array',
    items: { type: 'string' },
    example: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'],
  })
  authors?: any | null;

  @ApiPropertyOptional({
    description: 'The journal or conference where the research paper was published.',
    example: 'Nature',
  })
  journal?: string | null;

  @ApiPropertyOptional({
    description: 'The publication date of the research paper.',
    example: '2023-11-15T00:00:00.000Z',
    type: String,
    format: 'date-time',
  })
  publicationDate?: Date | null;

  @ApiPropertyOptional({
    description: 'The DOI of the research paper.',
    example: '10.1109/5.771073',
  })
  doi?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the PDF of the research paper.',
    example: 'https://www.nature.com/articles/nature14539.pdf',
  })
  pdfUrl?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the code repository for the research paper.',
    example: 'https://github.com/example/paper-code',
  })
  codeRepoUrl?: string | null;

  @ApiPropertyOptional({
    description: 'The abstract of the research paper.',
    example: 'Deep learning is a class of machine learning algorithms that...',
  })
  abstract?: string | null;

  @ApiPropertyOptional({
    description: 'The citation count of the research paper.',
    example: 45000,
    type: 'integer',
  })
  citations?: number | null;

  @ApiProperty({
    description: 'Timestamp of when the research paper details were created',
    example: '2025-02-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the research paper details',
    example: '2025-02-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 