import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ServiceProviderDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 's9t0u1v2-w3x4-y5z6-a7b8-c9d0e1f2g3h4',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'The services offered by the provider.',
    type: 'array',
    items: { type: 'string' },
    example: ['AI Development', 'Data Science Consulting', 'MLOps'],
  })
  servicesOffered?: any | null;

  @ApiPropertyOptional({
    description: 'Information about pricing.',
    example: 'Project-based, hourly rates available upon request.',
  })
  pricingInfo?: string | null;

  @ApiPropertyOptional({
    description: 'The primary contact person.',
    example: 'Jane Doe',
  })
  contactPerson?: string | null;

  @ApiPropertyOptional({
    description: 'The contact email for inquiries.',
    example: '<EMAIL>',
  })
  contactEmail?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the provider portfolio or case studies.',
    example: 'https://example.com/portfolio',
  })
  portfolioUrl?: string | null;

  @ApiPropertyOptional({
    description: 'The physical location or service area of the provider.',
    example: 'San Francisco, CA',
  })
  location?: string | null;

  @ApiPropertyOptional({
    description: 'Languages spoken by the service provider.',
    type: 'array',
    items: { type: 'string' },
    example: ['English', 'Spanish'],
  })
  languagesSpoken?: any | null;

  @ApiProperty({
    description: 'Timestamp of when the service provider details were created',
    example: '2025-03-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the service provider details',
    example: '2025-03-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 