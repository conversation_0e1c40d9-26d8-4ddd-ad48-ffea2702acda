import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PricingModel, PriceRange } from '@generated-prisma';

export class SoftwareDetailsResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID) this detail record is associated with.',
    example: 't0u1v2w3-x4y5-z6a7-b8c9-d0e1f2g3h4i5',
  })
  entityId: string;

  @ApiPropertyOptional({
    description: 'Key features of the software.',
    type: 'array',
    items: { type: 'string' },
    example: ['Data analysis', 'Automation', 'Reporting'],
  })
  keyFeatures?: any | null;

  @ApiPropertyOptional({
    description: 'Common use cases for the software.',
    type: 'array',
    items: { type: 'string' },
    example: ['Data analysis', 'Automation'],
  })
  useCases?: any | null;

  @ApiPropertyOptional({
    description: 'Known integrations with other tools or services.',
    type: 'array',
    items: { type: 'string' },
    example: ['Slack', 'Google Drive'],
  })
  integrations?: any | null;

  @ApiPropertyOptional({
    description: 'Target audience for the software.',
    type: 'array',
    items: { type: 'string' },
    example: ['Business Analysts', 'Developers'],
  })
  targetAudience?: any | null;

  @ApiPropertyOptional({
    description: 'Options for deploying the software.',
    type: 'array',
    items: { type: 'string' },
    example: ['Cloud', 'On-premise', 'Desktop'],
  })
  deploymentOptions?: any | null;

  @ApiPropertyOptional({
    description: 'Operating systems supported by the software.',
    type: 'array',
    items: { type: 'string' },
    example: ['Windows', 'macOS', 'Linux'],
  })
  supportedOs?: any | null;

  @ApiPropertyOptional({
    description: 'Indicates if the software has mobile support.',
    example: true,
  })
  mobileSupport?: boolean | null;

  @ApiPropertyOptional({
    description: 'Indicates if the software provides API access.',
    example: false,
  })
  apiAccess?: boolean | null;

  @ApiPropertyOptional({
    description: 'Indicates if the software has a free tier.',
    example: true,
  })
  hasFreeTier?: boolean | null;

  @ApiPropertyOptional({
    enum: PricingModel,
    description: 'The pricing model of the software.',
    example: PricingModel.SUBSCRIPTION,
  })
  pricingModel?: PricingModel | null;

  @ApiPropertyOptional({
    enum: PriceRange,
    description: 'The price range of the software.',
    example: PriceRange.LOW,
  })
  priceRange?: PriceRange | null;

  @ApiPropertyOptional({
    description: 'Specific details about pricing.',
    example: 'Starts at $10/month.',
  })
  pricingDetails?: string | null;

  @ApiPropertyOptional({
    description: 'URL to the pricing page of the software.',
    example: 'https://example.com/software/pricing',
  })
  pricingUrl?: string | null;

  @ApiPropertyOptional({
    description: 'Available support channels.',
    type: 'array',
    items: { type: 'string' },
    example: ['Email', 'Helpdesk', 'Community Forum'],
  })
  supportChannels?: any | null;

  @ApiPropertyOptional({
    description: 'Contact email for support.',
    example: '<EMAIL>',
  })
  supportEmail?: string | null;

  @ApiPropertyOptional({
    description: 'Indicates if live chat support is available.',
    example: true,
  })
  hasLiveChat?: boolean | null;

  @ApiPropertyOptional({
    description: 'URL to the community forum or Discord.',
    example: 'https://example.com/software/community',
  })
  communityUrl?: string | null;

  @ApiProperty({
    description: 'Timestamp of when the software details were created',
    example: '2025-04-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of the last update to the software details',
    example: '2025-04-10T10:00:00.000Z',
  })
  updatedAt: Date;
} 