import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

class EntityTypeMinimalResponseDto {
  @ApiProperty({ example: 'Tool' })
  name: string;

  @ApiProperty({ example: 'tool' })
  slug: string;
}

export class EntityListItemResponseDto {
  @ApiProperty({
    description: 'Entity ID (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  @ApiProperty({ description: 'Name of the entity', example: 'Super AI Tool' })
  name: string;

  @ApiProperty({ description: 'URL-friendly slug for the entity', example: 'super-ai-tool' })
  slug: string;

  @ApiPropertyOptional({
    description: "URL of the entity's logo",
    example: 'https://superaitool.com/logo.png',
  })
  logoUrl?: string | null;

  @ApiPropertyOptional({
    description: 'Short description of the entity',
    example: 'A tool that revolutionizes AI development.',
  })
  shortDescription?: string | null;

  @ApiPropertyOptional({
    description: 'Website URL of the entity',
    example: 'https://superaitool.com',
  })
  websiteUrl?: string | null;

  @ApiProperty({
    description: 'Type of the entity',
    type: () => EntityTypeMinimalResponseDto,
  })
  entityType: EntityTypeMinimalResponseDto;

  @ApiProperty({
    description: 'Average rating for the entity',
    example: 4.5,
    default: 0,
    type: 'number',
    format: 'float',
  })
  avgRating: number;

  @ApiProperty({
    description: 'Number of reviews for the entity',
    example: 42,
    default: 0,
    type: 'integer',
  })
  reviewCount: number;

  @ApiProperty({
    description: 'Number of users who have saved this entity',
    example: 123,
    default: 0,
    type: 'integer',
  })
  saveCount: number;

  @ApiPropertyOptional({
    description: 'Indicates if the tool has a free tier (only for tools).',
    example: true,
  })
  hasFreeTier?: boolean;
} 