import { ApiProperty } from '@nestjs/swagger';
import { EntityListItemResponseDto } from './entity-list-item-response.dto';
import { PaginatedResponseDto } from '../../common/dto/paginated-response.dto';

export class PaginatedEntityResponseDto extends PaginatedResponseDto<EntityListItemResponseDto> {
  @ApiProperty({
    type: () => [EntityListItemResponseDto],
    description: 'List of entities for the current page.',
  })
  data: EntityListItemResponseDto[];
} 