import { PartialType, ApiPropertyOptional } from '@nestjs/swagger';
import { CreateEntityDto } from './create-entity.dto';
import { IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

// Import or define Update<Type>DetailsDto classes
import { UpdateToolDetailsDto } from './details/update-tool-details.dto';
import { UpdateCourseDetailsDto } from './details/update-course-details.dto';
import { UpdateAgencyDetailsDto } from './details/update-agency-details.dto';
import { UpdateContentCreatorDetailsDto } from './details/update-content-creator-details.dto';
import { UpdateCommunityDetailsDto } from './details/update-community-details.dto';
import { UpdateNewsletterDetailsDto } from './details/update-newsletter-details.dto';

// Add imports for new Update<Type>DetailsDto
import { UpdateDatasetDetailsDto } from './details/update-dataset-details.dto';
import { UpdateResearchPaperDetailsDto } from './details/update-research-paper-details.dto';
import { UpdateSoftwareDetailsDto } from './details/update-software-details.dto';
import { UpdateModelDetailsDto } from './details/update-model-details.dto';
import { UpdateProjectReferenceDetailsDto } from './details/update-project-reference-details.dto';
import { UpdateServiceProviderDetailsDto } from './details/update-service-provider-details.dto';
import { UpdateInvestorDetailsDto } from './details/update-investor-details.dto';
import { UpdateEventDetailsDto } from './details/update-event-details.dto';
import { UpdateJobDetailsDto } from './details/update-job-details.dto';
import { UpdateGrantDetailsDto } from './details/update-grant-details.dto';
import { UpdateBountyDetailsDto } from './details/update-bounty-details.dto';
import { UpdateHardwareDetailsDto } from './details/update-hardware-details.dto';
import { UpdateNewsDetailsDto } from './details/update-news-details.dto';
import { UpdateBookDetailsDto } from './details/update-book-details.dto';
import { UpdatePodcastDetailsDto } from './details/update-podcast-details.dto';
import { UpdatePlatformDetailsDto } from './details/update-platform-details.dto';

export class UpdateEntityDto extends PartialType(CreateEntityDto) {
  // Override nested DTOs to use their respective Update<Type>DetailsDto
  @ApiPropertyOptional({ description: 'Details for an AI Tool' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateToolDetailsDto)
  tool_details?: UpdateToolDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Course' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateCourseDetailsDto)
  course_details?: UpdateCourseDetailsDto;

  @ApiPropertyOptional({ description: 'Details for an Agency' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateAgencyDetailsDto)
  agency_details?: UpdateAgencyDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Content Creator' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateContentCreatorDetailsDto)
  content_creator_details?: UpdateContentCreatorDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Community' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateCommunityDetailsDto)
  community_details?: UpdateCommunityDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Newsletter' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateNewsletterDetailsDto)
  newsletter_details?: UpdateNewsletterDetailsDto;

  // Add overrides for new nested Update<Type>DetailsDto
  @ApiPropertyOptional({ description: 'Details for a Dataset' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateDatasetDetailsDto)
  dataset_details?: UpdateDatasetDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Research Paper' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateResearchPaperDetailsDto)
  research_paper_details?: UpdateResearchPaperDetailsDto;

  @ApiPropertyOptional({ description: 'Details for Software' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateSoftwareDetailsDto)
  software_details?: UpdateSoftwareDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Model' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateModelDetailsDto)
  model_details?: UpdateModelDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Project Reference' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateProjectReferenceDetailsDto)
  project_reference_details?: UpdateProjectReferenceDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Service Provider' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateServiceProviderDetailsDto)
  service_provider_details?: UpdateServiceProviderDetailsDto;

  @ApiPropertyOptional({ description: 'Details for an Investor' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateInvestorDetailsDto)
  investor_details?: UpdateInvestorDetailsDto;

  @ApiPropertyOptional({ description: 'Details for an Event' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateEventDetailsDto)
  event_details?: UpdateEventDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Job' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateJobDetailsDto)
  job_details?: UpdateJobDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Grant' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateGrantDetailsDto)
  grant_details?: UpdateGrantDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Bounty' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateBountyDetailsDto)
  bounty_details?: UpdateBountyDetailsDto;

  @ApiPropertyOptional({ description: 'Details for Hardware' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateHardwareDetailsDto)
  hardware_details?: UpdateHardwareDetailsDto;

  @ApiPropertyOptional({ description: 'Details for News' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateNewsDetailsDto)
  news_details?: UpdateNewsDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Book' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdateBookDetailsDto)
  book_details?: UpdateBookDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Podcast' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdatePodcastDetailsDto)
  podcast_details?: UpdatePodcastDetailsDto;

  @ApiPropertyOptional({ description: 'Details for a Platform' })
  @IsOptional()
  @ValidateNested()
  @Type(() => UpdatePlatformDetailsDto)
  platform_details?: UpdatePlatformDetailsDto;
} 