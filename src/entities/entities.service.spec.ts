import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, ForbiddenException, ConflictException } from '@nestjs/common';
import { EntitiesService } from './entities.service';
import { PrismaService } from '../prisma/prisma.service';
import { mockPrismaService, resetPrismaMocks, mockPrismaErrors } from '../test/mocks/prisma.service.mock';
import { CreateEntityDto } from './dto/create-entity.dto';
import { UpdateEntityDto } from './dto/update-entity.dto';
import { ListEntitiesDto } from './dto/list-entities.dto';
import { Entity, EntityStatus, EntityType, UserRole, User as UserModel, Prisma } from '@generated-prisma';

describe('EntitiesService', () => {
  let service: EntitiesService;
  let prisma: typeof mockPrismaService;

  const mockUser: UserModel = {
    id: 'user-123',
    authUserId: 'auth-123',
    email: '<EMAIL>',
    username: 'testuser',
    displayName: 'Test User',
    profilePictureUrl: null,
    role: UserRole.USER,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    lastLogin: new Date('2023-01-01'),
    isActive: true,
    bio: null,
    location: null,
    website: null,
    socialLinks: null,
    preferences: null,
    emailVerified: true,
    emailVerifiedAt: new Date('2023-01-01'),
    twoFactorEnabled: false,
    lastPasswordChange: new Date('2023-01-01'),
    loginAttempts: 0,
    lockedUntil: null,
    passwordResetToken: null,
    passwordResetExpires: null,
    emailVerificationToken: null,
    emailVerificationExpires: null,
  };

  const mockAdminUser: UserModel = {
    ...mockUser,
    id: 'admin-123',
    role: UserRole.ADMIN,
  };

  const mockEntityType: EntityType = {
    id: 'type-123',
    name: 'AI Tool',
    description: 'AI Tool type',
    slug: 'ai-tool',
    iconUrl: 'https://example.com/icon.png',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  const mockEntity: Entity = {
    id: 'entity-123',
    name: 'Test Entity',
    websiteUrl: 'https://example.com',
    entityTypeId: 'type-123',
    shortDescription: 'A test entity',
    description: 'A longer description',
    logoUrl: 'https://example.com/logo.png',
    documentationUrl: 'https://example.com/docs',
    contactUrl: 'https://example.com/contact',
    privacyPolicyUrl: 'https://example.com/privacy',
    foundedYear: 2023,
    status: EntityStatus.PENDING,
    socialLinks: { twitter: 'https://twitter.com/test' },
    submitterId: 'user-123',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    metaTitle: null,
    metaDescription: null,
    employeeCountRange: null,
    fundingStage: null,
    locationSummary: null,
    refLink: null,
    affiliateStatus: null,
    scrapedReviewSentimentLabel: null,
    scrapedReviewSentimentScore: null,
    scrapedReviewCount: null,
    averageRating: null,
    totalReviews: 0,
    lastReviewedAt: null,
    featuredScore: null,
    trendingScore: null,
    qualityScore: null,
    popularityScore: null,
    lastScrapedAt: null,
    scrapingEnabled: false,
    adminNotes: null,
    rejectionReason: null,
    approvedAt: null,
    approvedBy: null,
    lastModifiedBy: null,
    slug: 'test-entity',
    searchVector: null,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        EntitiesService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<EntitiesService>(EntitiesService);
    prisma = module.get(PrismaService);

    // Reset all mocks before each test
    resetPrismaMocks();

    // Mock the entity type loading
    prisma.entityType.findMany.mockResolvedValue([mockEntityType]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('onModuleInit', () => {
    it('should load entity types on module initialization', async () => {
      await service.onModuleInit();

      expect(prisma.entityType.findMany).toHaveBeenCalledWith({
        select: { id: true, name: true, slug: true },
      });
    });
  });

  describe('create', () => {
    const createEntityDto: CreateEntityDto = {
      name: 'Test Entity',
      website_url: 'https://example.com',
      entity_type_id: 'type-123',
      short_description: 'A test entity',
      description: 'A longer description',
      logo_url: 'https://example.com/logo.png',
      category_ids: ['cat-1', 'cat-2'],
      tag_ids: ['tag-1', 'tag-2'],
      feature_ids: ['feat-1'],
    };

    it('should create an entity successfully', async () => {
      // Mock transaction
      prisma.$transaction.mockImplementation(async (callback) => {
        return await callback(prisma);
      });

      // Mock entity creation
      prisma.entity.create.mockResolvedValue({
        ...mockEntity,
        entityType: mockEntityType,
        submitter: mockUser,
        entityCategories: [],
        entityTags: [],
        entityFeatures: [],
        reviews: [],
      });

      const result = await service.create(createEntityDto, mockUser);

      expect(prisma.$transaction).toHaveBeenCalled();
      expect(result).toBeDefined();
      expect(result.name).toBe('Test Entity');
    });

    it('should throw ConflictException when entity name already exists', async () => {
      prisma.$transaction.mockImplementation(async (callback) => {
        return await callback(prisma);
      });

      // Mock existing entity check
      prisma.entity.findFirst.mockResolvedValue(mockEntity);

      await expect(service.create(createEntityDto, mockUser)).rejects.toThrow(
        ConflictException,
      );
    });
  });

  describe('findAll', () => {
    const listEntitiesDto: ListEntitiesDto = {
      page: 1,
      limit: 10,
      status: EntityStatus.APPROVED,
      categoryIds: ['cat-1'],
      tagIds: ['tag-1'],
      featureIds: ['feat-1'],
      searchTerm: 'test',
      sortBy: 'createdAt',
      sortOrder: Prisma.SortOrder.desc,
    };

    it('should return paginated entities with filters', async () => {
      const mockEntities = [mockEntity];
      const totalCount = 1;

      prisma.$transaction.mockResolvedValue([mockEntities, totalCount]);

      const result = await service.findAll(listEntitiesDto);

      expect(result).toEqual({
        data: mockEntities,
        meta: {
          total: totalCount,
          page: 1,
          limit: 10,
          totalPages: 1,
        },
      });

      expect(prisma.$transaction).toHaveBeenCalledWith([
        expect.objectContaining({
          where: expect.objectContaining({
            status: EntityStatus.APPROVED,
            entityCategories: {
              some: {
                categoryId: {
                  in: ['cat-1'],
                },
              },
            },
            entityTags: {
              some: {
                tagId: {
                  in: ['tag-1'],
                },
              },
            },
            entityFeatures: {
              some: {
                featureId: {
                  in: ['feat-1'],
                },
              },
            },
            OR: [
              { name: { contains: 'test', mode: 'insensitive' } },
              { shortDescription: { contains: 'test', mode: 'insensitive' } },
              { description: { contains: 'test', mode: 'insensitive' } },
            ],
          }),
          skip: 0,
          take: 10,
          orderBy: { createdAt: 'desc' },
        }),
        expect.any(Object), // count query
      ]);
    });

    it('should handle empty results', async () => {
      prisma.$transaction.mockResolvedValue([[], 0]);

      const result = await service.findAll({ page: 1, limit: 10 });

      expect(result).toEqual({
        data: [],
        meta: {
          total: 0,
          page: 1,
          limit: 10,
          totalPages: 0,
        },
      });
    });

    it('should build correct where clause for date filters', async () => {
      const dtoWithDates: ListEntitiesDto = {
        page: 1,
        limit: 10,
        createdAtFrom: new Date('2023-01-01'),
        createdAtTo: new Date('2023-12-31'),
      };

      prisma.$transaction.mockResolvedValue([[], 0]);

      await service.findAll(dtoWithDates);

      expect(prisma.$transaction).toHaveBeenCalledWith([
        expect.objectContaining({
          where: expect.objectContaining({
            createdAt: {
              gte: new Date('2023-01-01'),
              lte: new Date('2023-12-31'),
            },
          }),
        }),
        expect.any(Object),
      ]);
    });
  });

  describe('findOne', () => {
    it('should return an entity by id', async () => {
      const entityWithRelations = {
        ...mockEntity,
        entityType: mockEntityType,
        submitter: mockUser,
        entityCategories: [],
        entityTags: [],
        entityFeatures: [],
        reviews: [],
      };

      prisma.entity.findUnique.mockResolvedValue(entityWithRelations);

      const result = await service.findOne(mockEntity.id);

      expect(prisma.entity.findUnique).toHaveBeenCalledWith({
        where: { id: mockEntity.id },
        include: expect.objectContaining({
          entityType: true,
          submitter: expect.any(Object),
          entityCategories: expect.any(Object),
          entityTags: expect.any(Object),
          entityFeatures: expect.any(Object),
          reviews: expect.any(Object),
        }),
      });
      expect(result).toEqual(entityWithRelations);
    });

    it('should return null when entity not found', async () => {
      prisma.entity.findUnique.mockResolvedValue(null);

      const result = await service.findOne('non-existent-id');

      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    const updateEntityDto: UpdateEntityDto = {
      name: 'Updated Entity',
      short_description: 'Updated description',
      category_ids: ['cat-3'],
    };

    it('should update an entity successfully', async () => {
      const updatedEntity = { ...mockEntity, ...updateEntityDto };

      // Mock transaction
      prisma.$transaction.mockImplementation(async (callback) => {
        return await callback(prisma);
      });

      // Mock entity existence check
      prisma.entity.findUnique.mockResolvedValue(mockEntity);

      // Mock update operation
      prisma.entity.update.mockResolvedValue(updatedEntity);

      const result = await service.update(mockEntity.id, updateEntityDto, mockUser);

      expect(prisma.$transaction).toHaveBeenCalled();
      expect(result).toEqual(updatedEntity);
    });

    it('should throw NotFoundException when entity does not exist', async () => {
      prisma.$transaction.mockImplementation(async (callback) => {
        return await callback(prisma);
      });

      prisma.entity.findUnique.mockResolvedValue(null);

      await expect(service.update('non-existent-id', updateEntityDto, mockUser)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should throw ForbiddenException when user is not submitter or admin', async () => {
      const otherUser = { ...mockUser, id: 'other-user-123' };

      prisma.$transaction.mockImplementation(async (callback) => {
        return await callback(prisma);
      });

      prisma.entity.findUnique.mockResolvedValue(mockEntity);

      await expect(service.update(mockEntity.id, updateEntityDto, otherUser)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should allow admin to update any entity', async () => {
      const updatedEntity = { ...mockEntity, ...updateEntityDto };

      prisma.$transaction.mockImplementation(async (callback) => {
        return await callback(prisma);
      });

      prisma.entity.findUnique.mockResolvedValue(mockEntity);
      prisma.entity.update.mockResolvedValue(updatedEntity);

      const result = await service.update(mockEntity.id, updateEntityDto, mockAdminUser);

      expect(result).toEqual(updatedEntity);
    });
  });

  describe('adminSetStatus', () => {
    it('should update entity status as admin', async () => {
      const updatedEntity = { ...mockEntity, status: EntityStatus.APPROVED };

      prisma.entity.findUnique.mockResolvedValue(mockEntity);
      prisma.entity.update.mockResolvedValue(updatedEntity);

      const result = await service.adminSetStatus(mockEntity.id, EntityStatus.APPROVED);

      expect(prisma.entity.update).toHaveBeenCalledWith({
        where: { id: mockEntity.id },
        data: { status: EntityStatus.APPROVED },
        include: expect.any(Object),
      });
      expect(result).toEqual(updatedEntity);
    });

    it('should throw NotFoundException when entity does not exist', async () => {
      prisma.entity.findUnique.mockResolvedValue(null);

      await expect(service.adminSetStatus('non-existent-id', EntityStatus.APPROVED)).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('remove', () => {
    it('should archive an entity as admin', async () => {
      prisma.entity.findUnique.mockResolvedValue(mockEntity);
      prisma.entity.update.mockResolvedValue({ ...mockEntity, status: EntityStatus.ARCHIVED });

      await service.remove(mockEntity.id, mockAdminUser);

      expect(prisma.entity.update).toHaveBeenCalledWith({
        where: { id: mockEntity.id },
        data: { status: EntityStatus.ARCHIVED },
      });
    });

    it('should throw ForbiddenException when user is not admin', async () => {
      await expect(service.remove(mockEntity.id, mockUser)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw NotFoundException when entity does not exist', async () => {
      prisma.entity.findUnique.mockResolvedValue(null);

      await expect(service.remove('non-existent-id', mockAdminUser)).rejects.toThrow(
        NotFoundException,
      );
    });
  });
});