import { generateSlug } from '../utils/slug.utils';

describe('Slug Generation', () => {
  it('should generate a slug from entity name', () => {
    const name = 'Super AI Tool';
    const slug = generateSlug(name);
    expect(slug).toBe('super-ai-tool');
  });

  it('should handle special characters', () => {
    const name = 'AI Tool & Analytics Platform!';
    const slug = generateSlug(name);
    expect(slug).toBe('ai-tool-analytics-platform');
  });

  it('should handle multiple spaces', () => {
    const name = 'AI    Tool   Platform';
    const slug = generateSlug(name);
    expect(slug).toBe('ai-tool-platform');
  });

  it('should handle empty string', () => {
    const name = '';
    const slug = generateSlug(name);
    expect(slug).toBe('');
  });
});
