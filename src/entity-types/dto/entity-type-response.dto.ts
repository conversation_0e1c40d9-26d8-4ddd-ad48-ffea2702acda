import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class EntityTypeResponseDto {
  @ApiProperty({ description: 'Entity Type ID (UUID)', example: 'c3d4e5f6-g7h8-9012-3456-789012abcdef' })
  id: string;

  @ApiProperty({ description: 'Name of the entity type', example: 'Software Tool' })
  name: string;

  @ApiProperty({ description: 'URL-friendly slug for the entity type', example: 'software-tool' })
  slug: string;

  @ApiPropertyOptional({ description: 'Detailed description of the entity type', example: 'Represents software applications and tools.' })
  description?: string | null;

  @ApiPropertyOptional({ description: 'URL of an icon representing the entity type', example: 'https://example.com/icons/software.png' })
  iconUrl?: string | null;

  @ApiProperty({ description: 'Timestamp of when the entity type was created', example: '2023-03-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the entity type', example: '2023-03-10T10:00:00.000Z' })
  updatedAt: Date;
} 