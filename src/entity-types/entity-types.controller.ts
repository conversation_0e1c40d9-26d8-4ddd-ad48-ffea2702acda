import { Controller, Get } from '@nestjs/common';
import { EntityTypesService } from './entity-types.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { EntityTypeResponseDto } from '../entity-types/dto/entity-type-response.dto';
import { EntityType as EntityTypeModel } from '../../generated/prisma';

@ApiTags('Public - Entity Types')
@Controller('entity-types')
export class EntityTypesController {
  constructor(private readonly entityTypesService: EntityTypesService) {}

  private mapToResponseDto(entityType: EntityTypeModel): EntityTypeResponseDto {
    return {
      id: entityType.id,
      name: entityType.name,
      slug: entityType.slug,
      description: entityType.description,
      iconUrl: entityType.iconUrl,
      createdAt: entityType.createdAt,
      updatedAt: entityType.updatedAt,
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all public entity types' })
  @ApiResponse({ status: 200, description: 'List of all public entity types.', type: [EntityTypeResponseDto] })
  async findAllPublic(): Promise<EntityTypeResponseDto[]> {
    const entityTypes = await this.entityTypesService.findAll();

    if (entityTypes && Array.isArray(entityTypes)) {
      return entityTypes.map((et: EntityTypeModel) => this.mapToResponseDto(et));
    }
    
    console.warn('[EntityTypesController] entityTypesService.findAll() returned an unexpected structure or no data:', entityTypes);
    return []; 
  }
} 