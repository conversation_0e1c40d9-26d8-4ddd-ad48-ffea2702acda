import { Module } from '@nestjs/common';
import { EntityTypesService } from './entity-types.service';
import { EntityTypesController } from './entity-types.controller';
import { PrismaModule } from '../prisma/prisma.module';
// We will move PublicCategoriesController here later
// import { PublicCategoriesController } from '../categories/public-categories.controller';

@Module({
  imports: [PrismaModule],
  providers: [EntityTypesService],
  controllers: [EntityTypesController],
  // controllers: [PublicCategoriesController], // Will add controller here
  exports: [EntityTypesService],
})
export class EntityTypesModule {} 