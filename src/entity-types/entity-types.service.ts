import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service'; // Adjust path if needed
import { EntityType } from '../../generated/prisma';    // Adjust path if needed

@Injectable()
export class EntityTypesService {
  constructor(private readonly prisma: PrismaService) {}

  async findAll(): Promise<EntityType[]> {
    return this.prisma.entityType.findMany({
      orderBy: {
        name: 'asc',
      },
    });
  }
} 