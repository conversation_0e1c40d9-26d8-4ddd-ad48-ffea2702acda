import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsUrl, MaxLength } from 'class-validator';

export class CreateFeatureDto {
  @ApiProperty({
    description: 'The name of the feature.',
    example: 'Dark Mode',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional({
    description: 'A brief description of the feature.',
    example: 'Enables a dark theme for the user interface.',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'URL of an icon representing the feature.',
    example: 'https://example.com/icons/dark-mode.png',
  })
  @IsOptional()
  @IsUrl()
  @MaxLength(2048) // Standard URL max length
  iconUrl?: string;
} 