import { ApiProperty } from '@nestjs/swagger';

export class FeatureResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the feature.',
    example: 'clxrb20vf0000tpol0b1g2c3d',
  })
  id: string;

  @ApiProperty({
    description: 'The name of the feature.',
    example: 'Dark Mode',
  })
  name: string;

  @ApiProperty({
    description: 'URL-friendly slug for the feature.',
    example: 'dark-mode',
  })
  slug: string;

  @ApiProperty({
    description: 'A brief description of the feature.',
    example: 'Enables a dark theme for the user interface.',
    nullable: true,
  })
  description?: string | null;

  @ApiProperty({
    description: 'URL of an icon representing the feature.',
    example: 'https://example.com/icons/dark-mode.png',
    nullable: true,
  })
  iconUrl?: string | null;

  @ApiProperty({
    description: 'Timestamp of when the feature was created.',
    example: '2023-01-01T12:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Timestamp of when the feature was last updated.',
    example: '2023-01-02T12:00:00.000Z',
  })
  updatedAt: Date;
} 