import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  ValidationPipe,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { FeaturesService } from './features.service';
import { CreateFeatureDto } from './dto/create-feature.dto';
import { UpdateFeatureDto } from './dto/update-feature.dto';
import { FeatureResponseDto } from './dto/feature-response.dto';
import { AdminGuard } from '../auth/guards/admin.guard';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { Feature } from '@generated-prisma';

@ApiTags('Features')
@Controller('features')
export class FeaturesController {
  constructor(private readonly featuresService: FeaturesService) {}

  // Public endpoint to get all features
  @Get()
  @ApiOperation({ summary: 'Get all features' })
  @ApiResponse({
    status: 200,
    description: 'List of all features',
    type: [FeatureResponseDto],
  })
  async findAllPublic(): Promise<FeatureResponseDto[]> {
    const features = await this.featuresService.findAll();
    return features.map(this.mapToResponseDto);
  }

  // Admin endpoint to create a feature
  @Post('/admin') // Actual path will be /admin/features due to prefix
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new feature (Admin)' })
  @ApiResponse({
    status: 201,
    description: 'The feature has been successfully created.',
    type: FeatureResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async createAdmin(
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    createFeatureDto: CreateFeatureDto,
  ): Promise<FeatureResponseDto> {
    const feature = await this.featuresService.create(createFeatureDto);
    return this.mapToResponseDto(feature);
  }

  // Admin endpoint to get all features
  @Get('/admin')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all features (Admin)' })
  @ApiResponse({
    status: 200,
    description: 'List of all features for admin dashboard',
    type: [FeatureResponseDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async findAllAdmin(): Promise<FeatureResponseDto[]> {
    const features = await this.featuresService.findAll();
    return features.map(this.mapToResponseDto);
  }

  // Admin endpoint to get a specific feature by ID or Slug
  @Get('/admin/:idOrSlug')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a specific feature by ID or Slug (Admin)' })
  @ApiResponse({ status: 200, description: 'Feature details', type: FeatureResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Feature not found' })
  @ApiParam({ name: 'idOrSlug', description: 'Feature ID (UUID) or slug', type: String })
  async findOneAdmin(@Param('idOrSlug') idOrSlug: string): Promise<FeatureResponseDto> {
    let feature: Feature;
    // Check if idOrSlug is a UUID, if so, search by ID, otherwise by slug
    if (this.isUUID(idOrSlug)) {
      feature = await this.featuresService.findOne(idOrSlug);
    } else {
      feature = await this.featuresService.findBySlug(idOrSlug);
    }
    return this.mapToResponseDto(feature);
  }

  // Admin endpoint to update a feature
  @Patch('/admin/:id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a feature (Admin)' })
  @ApiResponse({
    status: 200,
    description: 'The feature has been successfully updated.',
    type: FeatureResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Feature not found' })
  @ApiParam({ name: 'id', description: 'Feature ID (UUID)', type: String })
  async updateAdmin(
    @Param('id', ParseUUIDPipe) id: string,
    @Body(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
    updateFeatureDto: UpdateFeatureDto,
  ): Promise<FeatureResponseDto> {
    const feature = await this.featuresService.update(id, updateFeatureDto);
    return this.mapToResponseDto(feature);
  }

  // Admin endpoint to delete a feature
  @Delete('/admin/:id')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a feature (Admin)' })
  @ApiResponse({ status: 204, description: 'The feature has been successfully deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Feature not found' })
  @ApiParam({ name: 'id', description: 'Feature ID (UUID)', type: String })
  async removeAdmin(@Param('id', ParseUUIDPipe) id: string): Promise<void> {
    await this.featuresService.remove(id);
  }

  // Helper to map Prisma Feature to FeatureResponseDto
  private mapToResponseDto(feature: Feature): FeatureResponseDto {
    return {
      id: feature.id,
      name: feature.name,
      slug: feature.slug,
      description: feature.description,
      iconUrl: feature.iconUrl,
      createdAt: feature.createdAt,
      updatedAt: feature.updatedAt,
    };
  }

  // Helper to check if a string is a UUID
  private isUUID(str: string): boolean {
    const uuidRegex =
      /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/;
    return uuidRegex.test(str);
  }
}

// It seems like the admin controller prefixing might be better handled at the module level
// or by creating a separate AdminFeaturesController. 
// For now, I've used sub-paths like '/admin' under the main 'features' controller.
// If you have a global prefix for '/admin' this might lead to '/admin/admin/features'.
// Let's adjust if necessary based on your existing admin routing setup. 