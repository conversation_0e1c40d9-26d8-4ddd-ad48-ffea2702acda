import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';

@Injectable()
export class OpenaiService {
  private readonly openai: OpenAI;
  private readonly logger = new Logger(OpenaiService.name);

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is not set in environment variables.');
    }
    this.openai = new OpenAI({ apiKey });
  }

  async generateEmbedding(text: string): Promise<number[] | null> {
    if (!text) {
      this.logger.warn('generateEmbedding called with empty text.');
      return null;
    }

    try {
      const response = await this.openai.embeddings.create({
        model: 'text-embedding-3-small',
        input: text.trim(),
      });

      if (response.data && response.data.length > 0 && response.data[0].embedding) {
        return response.data[0].embedding;
      } else {
        this.logger.warn('OpenAI API returned no embedding for the provided text.');
        return null;
      }
    } catch (error) {
      this.logger.error('Error generating embedding from OpenAI', error.stack);
      // Re-throwing the error as the user wants embedding failures to fail the transaction
      throw error;
    }
  }
} 