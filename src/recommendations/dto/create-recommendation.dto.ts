import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { RecommendationFiltersDto } from './recommendation-filters.dto';

export class CreateRecommendationDto {
  @ApiProperty({
    description: 'Description of the problem or need for which recommendations are requested',
    example: 'I need an AI tool to help me generate code documentation automatically',
  })
  @IsNotEmpty()
  @IsString()
  problem_description: string;

  @ApiProperty({
    description: 'Optional filters to apply to the search',
    required: false,
    type: RecommendationFiltersDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => RecommendationFiltersDto)
  filters?: RecommendationFiltersDto;
}
