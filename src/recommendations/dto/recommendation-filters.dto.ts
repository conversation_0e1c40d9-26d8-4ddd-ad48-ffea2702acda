import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsArray, IsUUID, IsEnum, <PERSON>Int, Min, Max, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { EntityStatus, EmployeeCountRange, FundingStage, PricingModel, PriceRange } from '../../../generated/prisma';

export class RecommendationFiltersDto {
  @ApiProperty({
    description: 'Filter by entity type IDs',
    example: ['10000000-0000-0000-0000-000000000001'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  entity_type_ids?: string[];

  @ApiProperty({
    description: 'Filter by category IDs',
    example: ['*************-0000-0000-000000000001'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  category_ids?: string[];

  @ApiProperty({
    description: 'Filter by tag IDs',
    example: ['*************-0000-0000-000000000001'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  tag_ids?: string[];

  @ApiProperty({
    description: 'Filter by feature IDs',
    example: ['*************-0000-0000-000000000001'],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsUUID(4, { each: true })
  feature_ids?: string[];

  @ApiProperty({
    description: 'Filter by entity status',
    enum: EntityStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(EntityStatus)
  status?: EntityStatus;

  @ApiProperty({
    description: 'Filter by whether the entity has a free tier',
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  has_free_tier?: boolean;

  @ApiProperty({
    description: 'Filter by employee count ranges',
    enum: EmployeeCountRange,
    isArray: true,
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(EmployeeCountRange, { each: true })
  employee_count_ranges?: EmployeeCountRange[];

  @ApiProperty({
    description: 'Filter by funding stages',
    enum: FundingStage,
    isArray: true,
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(FundingStage, { each: true })
  funding_stages?: FundingStage[];

  @ApiProperty({
    description: 'Filter by pricing models',
    enum: PricingModel,
    isArray: true,
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(PricingModel, { each: true })
  pricing_models?: PricingModel[];

  @ApiProperty({
    description: 'Filter by price ranges',
    enum: PriceRange,
    isArray: true,
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsEnum(PriceRange, { each: true })
  price_ranges?: PriceRange[];

  @ApiProperty({
    description: 'Maximum number of candidate entities to consider for LLM analysis',
    example: 20,
    minimum: 5,
    maximum: 50,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(5)
  @Max(50)
  max_candidates?: number = 20;
}
