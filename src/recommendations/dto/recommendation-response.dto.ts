import { ApiProperty } from '@nestjs/swagger';
import { EntityListItemResponseDto } from '../../entities/dto/entity-list-item-response.dto';

export class RecommendationResponseDto {
  @ApiProperty({
    description: 'List of recommended entities',
    type: [EntityListItemResponseDto],
  })
  recommended_entities: EntityListItemResponseDto[];

  @ApiProperty({
    description: 'AI-generated explanation of why these entities were recommended',
    example: 'Based on your need for automated code documentation, I recommend: 1) CodePal AI because it offers advanced code analysis and documentation generation features...',
  })
  explanation: string;

  @ApiProperty({
    description: 'The problem description that was analyzed',
    example: 'I need an AI tool to help me generate code documentation automatically',
  })
  problem_description: string;

  @ApiProperty({
    description: 'Number of candidate entities that were analyzed',
    example: 15,
  })
  candidates_analyzed: number;

  @ApiProperty({
    description: 'The LLM provider that generated the recommendation',
    example: 'OPENAI',
  })
  llm_provider: string;

  @ApiProperty({
    description: 'Timestamp when the recommendation was generated',
    example: '2024-06-20T01:00:00.000Z',
  })
  generated_at: Date;
}
