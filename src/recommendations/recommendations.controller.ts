import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { Throttle } from '@nestjs/throttler';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RecommendationsService } from './recommendations.service';
import { CreateRecommendationDto } from './dto/create-recommendation.dto';
import { RecommendationResponseDto } from './dto/recommendation-response.dto';

@ApiTags('Recommendations')
@Controller('recommendations')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class RecommendationsController {
  constructor(private readonly recommendationsService: RecommendationsService) {}

  @Post()
  @Throttle({ default: { limit: 10, ttl: 60000 } }) // 10 requests per minute
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get AI-powered recommendations',
    description: `
    Get personalized AI recommendations based on a problem description.
    
    This endpoint:
    1. Uses vector search to find semantically relevant entities
    2. Applies optional filters to narrow down candidates
    3. Uses the configured LLM provider to analyze and recommend the best options
    4. Returns detailed recommendations with explanations
    
    The LLM provider can be configured by admins via the admin settings API.
    `,
  })
  @ApiBody({
    type: CreateRecommendationDto,
    description: 'Problem description and optional filters for recommendations',
    examples: {
      'Code Documentation Tool': {
        summary: 'Looking for code documentation tools',
        value: {
          problem_description: 'I need an AI tool to help me generate code documentation automatically for my Python projects',
          filters: {
            entity_type_ids: ['10000000-0000-0000-0000-000000000001'], // AI Tool
            category_ids: ['20000000-0000-0000-0000-000000000001'], // Developer Tools
            has_free_tier: true,
            max_candidates: 15,
          },
        },
      },
      'Machine Learning Platform': {
        summary: 'Looking for ML platforms',
        value: {
          problem_description: 'I want to build and deploy machine learning models for my startup',
          filters: {
            category_ids: ['20000000-0000-0000-0000-000000000004'], // ML Platforms
            pricing_models: ['FREEMIUM', 'SUBSCRIPTION'],
            employee_count_ranges: ['C1_10', 'C11_50'],
            max_candidates: 20,
          },
        },
      },
      'AI Learning Resources': {
        summary: 'Looking for AI education',
        value: {
          problem_description: 'I am a beginner and want to learn about artificial intelligence and machine learning',
          filters: {
            entity_type_ids: ['10000000-0000-0000-0000-000000000002'], // Course
            category_ids: ['20000000-0000-0000-0000-000000000008'], // AI Education
            max_candidates: 10,
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'AI recommendations generated successfully',
    type: RecommendationResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data',
  })
  @ApiResponse({
    status: 401,
    description: 'Authentication required',
  })
  @ApiResponse({
    status: 429,
    description: 'Rate limit exceeded',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error during recommendation generation',
  })
  async getRecommendations(
    @Body(new ValidationPipe({ transform: true, whitelist: true, forbidNonWhitelisted: true }))
    createRecommendationDto: CreateRecommendationDto,
  ): Promise<RecommendationResponseDto> {
    return this.recommendationsService.getRecommendations(createRecommendationDto);
  }
}
