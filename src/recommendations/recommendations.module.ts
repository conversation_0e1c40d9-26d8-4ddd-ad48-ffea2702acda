import { Module } from '@nestjs/common';
import { RecommendationsController } from './recommendations.controller';
import { RecommendationsService } from './recommendations.service';
import { EntitiesModule } from '../entities/entities.module';
import { LlmModule } from '../common/llm/llm.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    EntitiesModule, // For EntitiesService to perform vector search
    LlmModule,      // For ILlmService and LlmFactoryService
    AuthModule,     // For authentication guards
  ],
  controllers: [RecommendationsController],
  providers: [RecommendationsService],
  exports: [RecommendationsService],
})
export class RecommendationsModule {}
