import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsInt,
  Min,
  Max,
  IsString,
  IsOptional,
  Length,
} from 'class-validator';

export class CreateReviewDto {
  @ApiProperty({
    description: 'Rating for the entity (1-5 stars).',
    example: 5,
    minimum: 1,
    maximum: 5,
  })
  @IsInt()
  @Min(1)
  @Max(5)
  @IsNotEmpty()
  rating: number;

  @ApiPropertyOptional({
    description: 'Optional title for the review.',
    example: 'Excellent Tool!',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @Length(5, 100)
  title?: string;

  @ApiProperty({
    description: 'Main comment/text of the review.',
    example: 'This tool has greatly improved my workflow...',
    minLength: 10,
    maxLength: 2000,
  })
  @IsString()
  @IsNotEmpty()
  @Length(10, 2000)
  reviewText: string;
} 