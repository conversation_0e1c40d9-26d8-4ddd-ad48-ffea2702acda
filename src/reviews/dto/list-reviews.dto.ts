import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsInt, IsOptional, IsString, Max, Min } from 'class-validator';
import { Prisma } from 'generated/prisma';

export class ListReviewsDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination.',
    default: 1,
    type: Number,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page.',
    default: 10,
    type: Number,
    maximum: 50, // Max 50 reviews per page
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(50)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Field to sort by',
    example: 'createdAt',
    enum: ['createdAt', 'rating'], // Allowed sort fields for reviews
  })
  @IsOptional()
  @IsString()
  @IsEnum(['createdAt', 'rating'])
  sortBy?: 'createdAt' | 'rating' = 'createdAt';

  @ApiPropertyOptional({
    description: 'Sort order',
    enum: Prisma.SortOrder,
    default: Prisma.SortOrder.desc,
  })
  @IsOptional()
  @IsEnum(Prisma.SortOrder)
  sortOrder?: Prisma.SortOrder = Prisma.SortOrder.desc;
} 