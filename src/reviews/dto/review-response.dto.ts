import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { ReviewStatus } from '../../../generated/prisma';

// A simplified version of UserResponseDto for nested display
class ReviewUserResponseDto {
  @ApiProperty({ description: 'User ID' })
  id: string;

  @ApiPropertyOptional({ description: 'Username' })
  username?: string;

  @ApiPropertyOptional({ description: "User's display name" })
  displayName?: string;

  @ApiPropertyOptional({ description: "URL of the user's profile picture" })
  profilePictureUrl?: string;
}

export class ReviewResponseDto {
  @ApiProperty({ description: 'Review ID' })
  id: string;

  @ApiProperty({ description: 'Rating given (1-5)' })
  rating: number;

  @ApiPropertyOptional({ description: 'Title of the review' })
  title?: string;

  @ApiPropertyOptional({ description: 'Main comment of the review' })
  comment?: string;

  @ApiPropertyOptional({ description: 'Pros mentioned in the review' })
  pros?: string;

  @ApiPropertyOptional({ description: 'Cons mentioned in the review' })
  cons?: string;

  @ApiProperty({ description: 'Status of the review', enum: ReviewStatus })
  status: ReviewStatus;

  @ApiProperty({ description: 'Date the review was created' })
  createdAt: Date;

  @ApiProperty({ description: 'Date the review was last updated' })
  updatedAt: Date;

  @ApiProperty({ description: 'ID of the user who submitted the review' })
  userId: string;

  @ApiPropertyOptional({ type: () => ReviewUserResponseDto, description: 'User who submitted the review' })
  user?: ReviewUserResponseDto; // Use the simplified DTO

  @ApiProperty({ description: 'ID of the entity being reviewed' })
  entityId: string;

  @ApiPropertyOptional({ description: 'Number of upvotes for the review' })
  upvotes?: number;

  @ApiPropertyOptional({ description: 'Number of downvotes for the review' })
  downvotes?: number;

  // You might also include fields like 'isHelpfulCount' or similar from ReviewVote relations
} 