import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsInt,
  Min,
  Max,
  IsString,
  IsOptional,
  Length,
} from 'class-validator';

// We can reuse parts of CreateReviewDto or define separately if there are significant differences.
// For now, let's assume updates are partial and similar fields can be updated.

export class UpdateReviewDto {
  @ApiPropertyOptional({
    description: 'New rating for the entity (1-5 stars).',
    example: 4,
    minimum: 1,
    maximum: 5,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(5)
  rating?: number;

  @ApiPropertyOptional({
    description: 'New optional title for the review.',
    example: 'Actually, pretty good!',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @Length(5, 100)
  title?: string;

  @ApiPropertyOptional({
    description: 'New main comment/text of the review.',
    example: 'After further use, I found it very helpful for... ',
    minLength: 10,
    maxLength: 2000,
  })
  @IsOptional()
  @IsString()
  @Length(10, 2000)
  reviewText?: string;
} 