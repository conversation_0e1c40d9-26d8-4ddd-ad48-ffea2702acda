import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { ReviewsController } from './reviews.controller';
import { ReviewsService } from './reviews.service';
import { PrismaService } from '../prisma/prisma.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { mockPrismaService } from '../test/mocks/prisma.service.mock';
import { CreateReviewDto } from './dto/create-review.dto';
import { UpdateReviewDto } from './dto/update-review.dto';
import { ListReviewsDto } from './dto/list-reviews.dto';
import { Review, ReviewStatus, User as UserModel, UserRole, UserStatus, Prisma } from '@generated-prisma';

describe('ReviewsController (Integration)', () => {
  let app: INestApplication;
  let reviewsService: ReviewsService;

  const mockUser: UserModel = {
    id: 'user-123',
    authUserId: 'auth-123',
    email: '<EMAIL>',
    username: 'testuser',
    displayName: 'Test User',
    profilePictureUrl: null,
    role: UserRole.USER,
    status: UserStatus.ACTIVE,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    lastLogin: new Date('2023-01-01'),
    isActive: true,
    bio: null,
    location: null,
    website: null,
    socialLinks: null,
    preferences: null,
    emailVerified: true,
    emailVerifiedAt: new Date('2023-01-01'),
    twoFactorEnabled: false,
    lastPasswordChange: new Date('2023-01-01'),
    loginAttempts: 0,
    lockedUntil: null,
    passwordResetToken: null,
    passwordResetExpires: null,
    emailVerificationToken: null,
    emailVerificationExpires: null,
  };

  const mockReview: Review = {
    id: 'review-123',
    userId: 'user-123',
    entityId: 'entity-123',
    rating: 5,
    title: 'Great tool!',
    reviewText: 'This is an excellent tool for AI development.',
    status: ReviewStatus.PENDING,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    helpfulCount: 0,
    reportedCount: 0,
    moderatorNotes: null,
    moderatedAt: null,
    moderatedBy: null,
  };

  const mockReviewWithUser = {
    ...mockReview,
    user: {
      id: mockUser.id,
      username: mockUser.username,
      profilePictureUrl: mockUser.profilePictureUrl,
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ReviewsController],
      providers: [
        ReviewsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn((context) => {
          const request = context.switchToHttp().getRequest();
          request.user = mockUser;
          return true;
        }),
      })
      .compile();

    app = module.createNestApplication();
    
    // Apply global validation pipe like in main.ts
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }));

    reviewsService = module.get<ReviewsService>(ReviewsService);
    
    await app.init();
  });

  afterEach(async () => {
    await app.close();
    jest.clearAllMocks();
  });

  describe('POST /entities/:entityId/reviews', () => {
    const entityId = 'entity-123';
    const createReviewDto: CreateReviewDto = {
      rating: 5,
      title: 'Great tool!',
      reviewText: 'This is an excellent tool for AI development.',
    };

    it('should create a review successfully', async () => {
      jest.spyOn(reviewsService, 'createReview').mockResolvedValue(mockReview);

      const response = await request(app.getHttpServer())
        .post(`/entities/${entityId}/reviews`)
        .send(createReviewDto)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.rating).toBe(createReviewDto.rating);
      expect(response.body.title).toBe(createReviewDto.title);
      expect(response.body.reviewText).toBe(createReviewDto.reviewText);
      expect(reviewsService.createReview).toHaveBeenCalledWith(mockUser.id, entityId, createReviewDto);
    });

    it('should validate rating range (1-5)', async () => {
      const invalidRatingDto = {
        ...createReviewDto,
        rating: 6, // Invalid rating
      };

      await request(app.getHttpServer())
        .post(`/entities/${entityId}/reviews`)
        .send(invalidRatingDto)
        .expect(400);
    });

    it('should validate required fields', async () => {
      const incompleteDto = {
        rating: 5,
        // Missing reviewText
      };

      await request(app.getHttpServer())
        .post(`/entities/${entityId}/reviews`)
        .send(incompleteDto)
        .expect(400);
    });

    it('should validate review text length', async () => {
      const shortTextDto = {
        ...createReviewDto,
        reviewText: 'Too short', // Less than 10 characters
      };

      await request(app.getHttpServer())
        .post(`/entities/${entityId}/reviews`)
        .send(shortTextDto)
        .expect(400);
    });

    it('should validate title length if provided', async () => {
      const longTitleDto = {
        ...createReviewDto,
        title: 'a'.repeat(101), // More than 100 characters
      };

      await request(app.getHttpServer())
        .post(`/entities/${entityId}/reviews`)
        .send(longTitleDto)
        .expect(400);
    });

    it('should return 400 for invalid UUID format', async () => {
      await request(app.getHttpServer())
        .post('/entities/invalid-uuid/reviews')
        .send(createReviewDto)
        .expect(400);
    });

    it('should require authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [ReviewsController],
        providers: [
          ReviewsService,
          { provide: PrismaService, useValue: mockPrismaService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .post(`/entities/${entityId}/reviews`)
        .send(createReviewDto)
        .expect(403);

      await testApp.close();
    });

    it('should strip non-whitelisted properties', async () => {
      const dtoWithExtraFields = {
        ...createReviewDto,
        status: ReviewStatus.APPROVED, // Should be stripped
        maliciousField: 'should be removed',
      };

      jest.spyOn(reviewsService, 'createReview').mockResolvedValue(mockReview);

      await request(app.getHttpServer())
        .post(`/entities/${entityId}/reviews`)
        .send(dtoWithExtraFields)
        .expect(201);

      expect(reviewsService.createReview).toHaveBeenCalledWith(
        mockUser.id,
        entityId,
        expect.not.objectContaining({
          status: expect.anything(),
          maliciousField: expect.anything(),
        }),
      );
    });
  });

  describe('GET /entities/:entityId/reviews', () => {
    const entityId = 'entity-123';
    const mockPaginatedResult = {
      data: [mockReviewWithUser],
      total: 1,
      page: 1,
      limit: 10,
    };

    it('should return approved reviews for entity', async () => {
      jest.spyOn(reviewsService, 'getApprovedReviewsForEntity').mockResolvedValue(mockPaginatedResult as any);

      const response = await request(app.getHttpServer())
        .get(`/entities/${entityId}/reviews`)
        .expect(200);

      expect(response.body).toHaveProperty('data');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('limit');
      expect(response.body.data).toHaveLength(1);
      expect(reviewsService.getApprovedReviewsForEntity).toHaveBeenCalledWith(entityId, expect.any(Object));
    });

    it('should handle query parameters correctly', async () => {
      jest.spyOn(reviewsService, 'getApprovedReviewsForEntity').mockResolvedValue(mockPaginatedResult as any);

      await request(app.getHttpServer())
        .get(`/entities/${entityId}/reviews`)
        .query({
          page: 2,
          limit: 5,
          sortBy: 'rating',
          sortOrder: 'desc',
        })
        .expect(200);

      expect(reviewsService.getApprovedReviewsForEntity).toHaveBeenCalledWith(
        entityId,
        expect.objectContaining({
          page: 2,
          limit: 5,
          sortBy: 'rating',
          sortOrder: 'desc',
        }),
      );
    });

    it('should return 400 for invalid UUID format', async () => {
      await request(app.getHttpServer())
        .get('/entities/invalid-uuid/reviews')
        .expect(400);
    });

    it('should validate query parameter types', async () => {
      await request(app.getHttpServer())
        .get(`/entities/${entityId}/reviews`)
        .query({
          page: 'invalid',
          limit: 'invalid',
        })
        .expect(400);
    });

    it('should handle empty results', async () => {
      const emptyResult = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
      };
      jest.spyOn(reviewsService, 'getApprovedReviewsForEntity').mockResolvedValue(emptyResult as any);

      const response = await request(app.getHttpServer())
        .get(`/entities/${entityId}/reviews`)
        .expect(200);

      expect(response.body.data).toHaveLength(0);
      expect(response.body.total).toBe(0);
    });
  });

  describe('PUT /reviews/:reviewId', () => {
    const reviewId = 'review-123';
    const updateReviewDto: UpdateReviewDto = {
      rating: 4,
      title: 'Updated title',
      reviewText: 'Updated review text with more details.',
    };

    it('should update a review successfully', async () => {
      const updatedReview = { ...mockReview, ...updateReviewDto };
      jest.spyOn(reviewsService, 'updateUserReview').mockResolvedValue(updatedReview);

      const response = await request(app.getHttpServer())
        .put(`/reviews/${reviewId}`)
        .send(updateReviewDto)
        .expect(200);

      expect(response.body.rating).toBe(updateReviewDto.rating);
      expect(response.body.title).toBe(updateReviewDto.title);
      expect(response.body.reviewText).toBe(updateReviewDto.reviewText);
      expect(reviewsService.updateUserReview).toHaveBeenCalledWith(reviewId, mockUser.id, updateReviewDto);
    });

    it('should allow partial updates', async () => {
      const partialUpdateDto = {
        rating: 3,
      };
      const updatedReview = { ...mockReview, rating: 3 };
      jest.spyOn(reviewsService, 'updateUserReview').mockResolvedValue(updatedReview);

      await request(app.getHttpServer())
        .put(`/reviews/${reviewId}`)
        .send(partialUpdateDto)
        .expect(200);

      expect(reviewsService.updateUserReview).toHaveBeenCalledWith(reviewId, mockUser.id, partialUpdateDto);
    });

    it('should validate rating range if provided', async () => {
      const invalidRatingDto = {
        rating: 0, // Invalid rating
      };

      await request(app.getHttpServer())
        .put(`/reviews/${reviewId}`)
        .send(invalidRatingDto)
        .expect(400);
    });

    it('should validate review text length if provided', async () => {
      const shortTextDto = {
        reviewText: 'Too short', // Less than 10 characters
      };

      await request(app.getHttpServer())
        .put(`/reviews/${reviewId}`)
        .send(shortTextDto)
        .expect(400);
    });

    it('should return 400 for invalid UUID format', async () => {
      await request(app.getHttpServer())
        .put('/reviews/invalid-uuid')
        .send(updateReviewDto)
        .expect(400);
    });

    it('should require authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [ReviewsController],
        providers: [
          ReviewsService,
          { provide: PrismaService, useValue: mockPrismaService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .put(`/reviews/${reviewId}`)
        .send(updateReviewDto)
        .expect(403);

      await testApp.close();
    });

    it('should strip non-whitelisted properties', async () => {
      const dtoWithExtraFields = {
        ...updateReviewDto,
        status: ReviewStatus.APPROVED, // Should be stripped
        userId: 'different-user', // Should be stripped
        maliciousField: 'should be removed',
      };

      const updatedReview = { ...mockReview, ...updateReviewDto };
      jest.spyOn(reviewsService, 'updateUserReview').mockResolvedValue(updatedReview);

      await request(app.getHttpServer())
        .put(`/reviews/${reviewId}`)
        .send(dtoWithExtraFields)
        .expect(200);

      expect(reviewsService.updateUserReview).toHaveBeenCalledWith(
        reviewId,
        mockUser.id,
        expect.not.objectContaining({
          status: expect.anything(),
          userId: expect.anything(),
          maliciousField: expect.anything(),
        }),
      );
    });
  });

  describe('DELETE /reviews/:reviewId', () => {
    const reviewId = 'review-123';

    it('should delete a review successfully', async () => {
      jest.spyOn(reviewsService, 'deleteUserReview').mockResolvedValue(undefined);

      await request(app.getHttpServer())
        .delete(`/reviews/${reviewId}`)
        .expect(204);

      expect(reviewsService.deleteUserReview).toHaveBeenCalledWith(reviewId, mockUser.id);
    });

    it('should return 400 for invalid UUID format', async () => {
      await request(app.getHttpServer())
        .delete('/reviews/invalid-uuid')
        .expect(400);
    });

    it('should require authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [ReviewsController],
        providers: [
          ReviewsService,
          { provide: PrismaService, useValue: mockPrismaService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .delete(`/reviews/${reviewId}`)
        .expect(403);

      await testApp.close();
    });

    it('should handle service errors gracefully', async () => {
      jest.spyOn(reviewsService, 'deleteUserReview').mockRejectedValue(
        new Error('Review not found or forbidden'),
      );

      await request(app.getHttpServer())
        .delete(`/reviews/${reviewId}`)
        .expect(500);
    });
  });
});
