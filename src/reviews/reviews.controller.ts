import {
  Controller,
  Post,
  Get,
  Param,
  Body,
  Query,
  UseGuards,
  ParseUUI<PERSON>ipe,
  Put,
  Delete,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ReviewsService } from './reviews.service';
import { CreateReviewDto } from './dto/create-review.dto';
import { ListReviewsDto } from './dto/list-reviews.dto';
import { UpdateReviewDto } from './dto/update-review.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetUser } from '../auth/decorators/get-user.decorator';
import {
  User as UserModel,
} from '../../generated/prisma';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';

@ApiTags('Reviews (User)')
@Controller()
export class ReviewsController {
  constructor(private readonly reviewsService: ReviewsService) {}

  @Post('entities/:entityId/reviews')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Submit a new review for an entity' })
  @ApiParam({ name: 'entityId', type: 'string', format: 'uuid', description: 'The ID of the entity to review' })
  @ApiResponse({ status: 201, description: 'Review submitted successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid input.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Entity not found.' })
  @ApiResponse({ status: 409, description: 'User has already reviewed this entity.' })
  async submitReview(
    @Param('entityId', ParseUUIDPipe) entityId: string,
    @GetUser() user: UserModel,
    @Body() createReviewDto: CreateReviewDto,
  ) {
    return this.reviewsService.createReview(user.id, entityId, createReviewDto);
  }

  @Get('entities/:entityId/reviews')
  @ApiOperation({ summary: 'Get approved reviews for an entity' })
  @ApiParam({ name: 'entityId', type: 'string', format: 'uuid', description: 'The ID of the entity' })
  @ApiResponse({ status: 200, description: 'List of approved reviews.' })
  @ApiResponse({ status: 404, description: 'Entity not found.' })
  async getApprovedReviewsForEntity(
    @Param('entityId', ParseUUIDPipe) entityId: string,
    @Query() listReviewsDto: ListReviewsDto,
  ) {
    return this.reviewsService.getApprovedReviewsForEntity(entityId, listReviewsDto);
  }

  @Put('reviews/:reviewId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update an existing review by its ID' })
  @ApiParam({ name: 'reviewId', type: 'string', format: 'uuid', description: 'The ID of the review to update' })
  @ApiResponse({ status: 200, description: 'Review updated successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid input.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. User cannot update this review.' })
  @ApiResponse({ status: 404, description: 'Review not found.' })
  async updateUserReview(
    @Param('reviewId', ParseUUIDPipe) reviewId: string,
    @GetUser() user: UserModel,
    @Body() updateReviewDto: UpdateReviewDto,
  ) {
    return this.reviewsService.updateUserReview(reviewId, user.id, updateReviewDto);
  }

  @Delete('reviews/:reviewId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a review by its ID' })
  @ApiParam({ name: 'reviewId', type: 'string', format: 'uuid', description: 'The ID of the review to delete' })
  @ApiResponse({ status: 204, description: 'Review deleted successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. User cannot delete this review.' })
  @ApiResponse({ status: 404, description: 'Review not found (or already deleted).' })
  async deleteUserReview(
    @Param('reviewId', ParseUUIDPipe) reviewId: string,
    @GetUser() user: UserModel,
  ) {
    return this.reviewsService.deleteUserReview(reviewId, user.id);
  }
} 