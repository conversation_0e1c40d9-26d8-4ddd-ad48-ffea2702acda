import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, ConflictException, ForbiddenException, InternalServerErrorException } from '@nestjs/common';
import { ReviewsService } from './reviews.service';
import { PrismaService } from '../prisma/prisma.service';
import { mockPrismaService, resetPrismaMocks, mockPrismaErrors } from '../test/mocks/prisma.service.mock';
import { CreateReviewDto } from './dto/create-review.dto';
import { UpdateReviewDto } from './dto/update-review.dto';
import { ListReviewsDto } from './dto/list-reviews.dto';
import { Review, ReviewStatus, User as UserModel, UserRole, UserStatus, Entity, Prisma } from '@generated-prisma';

describe('ReviewsService', () => {
  let service: ReviewsService;
  let prisma: typeof mockPrismaService;

  const mockUser: UserModel = {
    id: 'user-123',
    authUserId: 'auth-123',
    email: '<EMAIL>',
    username: 'testuser',
    displayName: 'Test User',
    profilePictureUrl: null,
    role: UserRole.USER,
    status: UserStatus.ACTIVE,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    lastLogin: new Date('2023-01-01'),
    isActive: true,
    bio: null,
    location: null,
    website: null,
    socialLinks: null,
    preferences: null,
    emailVerified: true,
    emailVerifiedAt: new Date('2023-01-01'),
    twoFactorEnabled: false,
    lastPasswordChange: new Date('2023-01-01'),
    loginAttempts: 0,
    lockedUntil: null,
    passwordResetToken: null,
    passwordResetExpires: null,
    emailVerificationToken: null,
    emailVerificationExpires: null,
  };

  const mockAdminUser: UserModel = {
    ...mockUser,
    id: 'admin-123',
    role: UserRole.ADMIN,
  };

  const mockEntity: Entity = {
    id: 'entity-123',
    name: 'Test Entity',
    websiteUrl: 'https://example.com',
    entityTypeId: 'type-123',
    shortDescription: 'A test entity',
    description: 'A longer description',
    logoUrl: 'https://example.com/logo.png',
    documentationUrl: 'https://example.com/docs',
    contactUrl: 'https://example.com/contact',
    privacyPolicyUrl: 'https://example.com/privacy',
    foundedYear: 2023,
    status: 'APPROVED' as any,
    socialLinks: { twitter: 'https://twitter.com/test' },
    submitterId: 'user-123',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    metaTitle: null,
    metaDescription: null,
    employeeCountRange: null,
    fundingStage: null,
    locationSummary: null,
    refLink: null,
    affiliateStatus: null,
    scrapedReviewSentimentLabel: null,
    scrapedReviewSentimentScore: null,
    scrapedReviewCount: null,
    averageRating: null,
    totalReviews: 0,
    lastReviewedAt: null,
    featuredScore: null,
    trendingScore: null,
    qualityScore: null,
    popularityScore: null,
    lastScrapedAt: null,
    scrapingEnabled: false,
    adminNotes: null,
    rejectionReason: null,
    approvedAt: null,
    approvedBy: null,
    lastModifiedBy: null,
    slug: 'test-entity',
    searchVector: null,
  };

  const mockReview: Review = {
    id: 'review-123',
    userId: 'user-123',
    entityId: 'entity-123',
    rating: 5,
    title: 'Great tool!',
    reviewText: 'This is an excellent tool for AI development.',
    status: ReviewStatus.PENDING,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    helpfulCount: 0,
    reportedCount: 0,
    moderatorNotes: null,
    moderatedAt: null,
    moderatedBy: null,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReviewsService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<ReviewsService>(ReviewsService);
    prisma = module.get(PrismaService);
    
    // Reset all mocks before each test
    resetPrismaMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createReview', () => {
    const createReviewDto: CreateReviewDto = {
      rating: 5,
      title: 'Great tool!',
      reviewText: 'This is an excellent tool for AI development.',
    };

    it('should create a review successfully', async () => {
      // Mock entity exists
      prisma.entity.findUnique.mockResolvedValue(mockEntity);
      // Mock no existing review
      prisma.review.findUnique.mockResolvedValue(null);
      // Mock review creation
      prisma.review.create.mockResolvedValue(mockReview);

      const result = await service.createReview(mockUser.id, mockEntity.id, createReviewDto);

      expect(prisma.entity.findUnique).toHaveBeenCalledWith({
        where: { id: mockEntity.id },
        select: { id: true },
      });
      expect(prisma.review.findUnique).toHaveBeenCalledWith({
        where: {
          userId_entityId: {
            userId: mockUser.id,
            entityId: mockEntity.id,
          },
        },
      });
      expect(prisma.review.create).toHaveBeenCalledWith({
        data: {
          userId: mockUser.id,
          entityId: mockEntity.id,
          rating: createReviewDto.rating,
          title: createReviewDto.title,
          reviewText: createReviewDto.reviewText,
          status: ReviewStatus.PENDING,
        },
      });
      expect(result).toEqual(mockReview);
    });

    it('should throw NotFoundException when entity does not exist', async () => {
      prisma.entity.findUnique.mockResolvedValue(null);

      await expect(
        service.createReview(mockUser.id, 'non-existent-entity', createReviewDto),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw ConflictException when user has already reviewed the entity', async () => {
      prisma.entity.findUnique.mockResolvedValue(mockEntity);
      prisma.review.findUnique.mockResolvedValue(mockReview);

      await expect(
        service.createReview(mockUser.id, mockEntity.id, createReviewDto),
      ).rejects.toThrow(ConflictException);
    });

    it('should throw InternalServerErrorException on database error', async () => {
      prisma.entity.findUnique.mockResolvedValue(mockEntity);
      prisma.review.findUnique.mockResolvedValue(null);
      prisma.review.create.mockRejectedValue(new Error('Database error'));

      await expect(
        service.createReview(mockUser.id, mockEntity.id, createReviewDto),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('updateUserReview', () => {
    const updateReviewDto: UpdateReviewDto = {
      rating: 4,
      title: 'Updated title',
      reviewText: 'Updated review text',
    };

    it('should update a review successfully', async () => {
      const updatedReview = { ...mockReview, ...updateReviewDto };
      
      prisma.review.findUnique.mockResolvedValue(mockReview);
      prisma.review.update.mockResolvedValue(updatedReview);

      const result = await service.updateUserReview(mockReview.id, mockUser.id, updateReviewDto);

      expect(prisma.review.update).toHaveBeenCalledWith({
        where: { id: mockReview.id },
        data: {
          ...updateReviewDto,
          updatedAt: expect.any(Date),
        },
      });
      expect(result).toEqual(updatedReview);
    });

    it('should throw NotFoundException when review does not exist', async () => {
      prisma.review.findUnique.mockResolvedValue(null);

      await expect(
        service.updateUserReview('non-existent-review', mockUser.id, updateReviewDto),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw ForbiddenException when user is not the review author', async () => {
      const otherUserReview = { ...mockReview, userId: 'other-user-123' };
      prisma.review.findUnique.mockResolvedValue(otherUserReview);

      await expect(
        service.updateUserReview(mockReview.id, mockUser.id, updateReviewDto),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should throw ForbiddenException when review is not pending', async () => {
      const approvedReview = { ...mockReview, status: ReviewStatus.APPROVED };
      prisma.review.findUnique.mockResolvedValue(approvedReview);

      await expect(
        service.updateUserReview(mockReview.id, mockUser.id, updateReviewDto),
      ).rejects.toThrow(ForbiddenException);
    });
  });

  describe('deleteUserReview', () => {
    it('should delete a review successfully', async () => {
      prisma.review.findUnique.mockResolvedValue(mockReview);
      prisma.review.delete.mockResolvedValue(mockReview);

      await service.deleteUserReview(mockReview.id, mockUser.id);

      expect(prisma.review.delete).toHaveBeenCalledWith({
        where: { id: mockReview.id },
      });
    });

    it('should return silently when review does not exist (idempotent)', async () => {
      prisma.review.findUnique.mockResolvedValue(null);

      await expect(service.deleteUserReview('non-existent-review', mockUser.id)).resolves.toBeUndefined();
    });

    it('should throw ForbiddenException when user is not the review author', async () => {
      const otherUserReview = { ...mockReview, userId: 'other-user-123' };
      prisma.review.findUnique.mockResolvedValue(otherUserReview);

      await expect(
        service.deleteUserReview(mockReview.id, mockUser.id),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should handle P2025 error gracefully', async () => {
      prisma.review.findUnique.mockResolvedValue(mockReview);
      prisma.review.delete.mockRejectedValue(mockPrismaErrors.P2025);

      await expect(service.deleteUserReview(mockReview.id, mockUser.id)).resolves.toBeUndefined();
    });

    it('should throw InternalServerErrorException on other database errors', async () => {
      prisma.review.findUnique.mockResolvedValue(mockReview);
      prisma.review.delete.mockRejectedValue(new Error('Database error'));

      await expect(
        service.deleteUserReview(mockReview.id, mockUser.id),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('getApprovedReviewsForEntity', () => {
    const listReviewsDto: ListReviewsDto = {
      page: 1,
      limit: 10,
      sortBy: 'createdAt',
      sortOrder: Prisma.SortOrder.desc,
    };

    const mockReviewWithUser = {
      ...mockReview,
      status: ReviewStatus.APPROVED,
      user: {
        id: mockUser.id,
        username: mockUser.username,
        profilePictureUrl: mockUser.profilePictureUrl,
      },
    };

    it('should return paginated approved reviews for entity', async () => {
      const mockReviews = [mockReviewWithUser];
      const totalCount = 1;

      prisma.entity.findUnique.mockResolvedValue(mockEntity);
      prisma.$transaction.mockResolvedValue([mockReviews, totalCount]);

      const result = await service.getApprovedReviewsForEntity(mockEntity.id, listReviewsDto);

      expect(result).toEqual({
        data: mockReviews,
        total: totalCount,
        page: 1,
        limit: 10,
      });

      expect(prisma.$transaction).toHaveBeenCalledWith([
        expect.objectContaining({
          where: {
            entityId: mockEntity.id,
            status: ReviewStatus.APPROVED,
          },
          include: {
            user: {
              select: {
                id: true,
                username: true,
                profilePictureUrl: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip: 0,
          take: 10,
        }),
        expect.objectContaining({
          where: {
            entityId: mockEntity.id,
            status: ReviewStatus.APPROVED,
          },
        }),
      ]);
    });

    it('should throw NotFoundException when entity does not exist', async () => {
      prisma.entity.findUnique.mockResolvedValue(null);

      await expect(
        service.getApprovedReviewsForEntity('non-existent-entity', listReviewsDto),
      ).rejects.toThrow(NotFoundException);
    });

    it('should handle empty results', async () => {
      prisma.entity.findUnique.mockResolvedValue(mockEntity);
      prisma.$transaction.mockResolvedValue([[], 0]);

      const result = await service.getApprovedReviewsForEntity(mockEntity.id, listReviewsDto);

      expect(result).toEqual({
        data: [],
        total: 0,
        page: 1,
        limit: 10,
      });
    });

    it('should throw InternalServerErrorException on database error', async () => {
      prisma.entity.findUnique.mockResolvedValue(mockEntity);
      prisma.$transaction.mockRejectedValue(new Error('Database error'));

      await expect(
        service.getApprovedReviewsForEntity(mockEntity.id, listReviewsDto),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });

  describe('updateReviewStatus', () => {
    it('should update review status successfully', async () => {
      const updatedReview = {
        ...mockReview,
        status: ReviewStatus.APPROVED,
        user: mockUser,
        entity: mockEntity,
      };

      prisma.review.findUnique.mockResolvedValue(mockReview);
      prisma.review.update.mockResolvedValue(updatedReview);

      const result = await service.updateReviewStatus(mockReview.id, ReviewStatus.APPROVED, mockAdminUser);

      expect(prisma.review.update).toHaveBeenCalledWith({
        where: { id: mockReview.id },
        data: {
          status: ReviewStatus.APPROVED,
          updatedAt: expect.any(Date),
        },
        include: {
          user: true,
          entity: true,
        },
      });
      expect(result).toEqual(updatedReview);
    });

    it('should throw NotFoundException when review does not exist', async () => {
      prisma.review.findUnique.mockResolvedValue(null);

      await expect(
        service.updateReviewStatus('non-existent-review', ReviewStatus.APPROVED, mockAdminUser),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('adminDeleteReview', () => {
    it('should delete review successfully as admin', async () => {
      prisma.review.findUnique.mockResolvedValue(mockReview);
      prisma.review.delete.mockResolvedValue(mockReview);

      await service.adminDeleteReview(mockReview.id, mockAdminUser);

      expect(prisma.review.delete).toHaveBeenCalledWith({
        where: { id: mockReview.id },
      });
    });

    it('should throw NotFoundException when review does not exist', async () => {
      prisma.review.findUnique.mockResolvedValue(null);

      await expect(
        service.adminDeleteReview('non-existent-review', mockAdminUser),
      ).rejects.toThrow(NotFoundException);
    });

    it('should handle P2025 error and throw NotFoundException', async () => {
      prisma.review.findUnique.mockResolvedValue(mockReview);
      prisma.review.delete.mockRejectedValue(mockPrismaErrors.P2025);

      await expect(
        service.adminDeleteReview(mockReview.id, mockAdminUser),
      ).rejects.toThrow(NotFoundException);
    });

    it('should throw InternalServerErrorException on other database errors', async () => {
      prisma.review.findUnique.mockResolvedValue(mockReview);
      prisma.review.delete.mockRejectedValue(new Error('Database error'));

      await expect(
        service.adminDeleteReview(mockReview.id, mockAdminUser),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });
});
