import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, MaxLength, MinLength, Matches } from 'class-validator';

export class CreateTagDto {
  @ApiProperty({
    description: 'The name of the tag.',
    example: 'Machine Learning',
    minLength: 2,
    maxLength: 50,
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  name: string;

  @ApiPropertyOptional({
    description: 'The URL-friendly slug for the tag. Should be unique. If not provided, it will be generated from the name.',
    example: 'machine-learning',
    pattern: '^[a-z0-9]+(?:-[a-z0-9]+)*$',
    maxLength: 75,
  })
  @IsOptional()
  @IsString()
  @Matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, {
    message: 'Slug must be lowercase alphanumeric with hyphens as separators.',
  })
  @MaxLength(75)
  slug?: string;

  @ApiPropertyOptional({
    description: 'A brief description of the tag.',
    example: 'Tags related to machine learning algorithms, libraries, and techniques.',
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string;
} 