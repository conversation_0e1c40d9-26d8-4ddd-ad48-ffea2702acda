import { ApiProperty } from '@nestjs/swagger';

export class TagResponseDto {
  @ApiProperty({ example: 'c1b2a3d4-e5f6-7890-1234-567890abcdef' })
  id: string;

  @ApiProperty({ example: 'Artificial Intelligence' })
  name: string;

  @ApiProperty({ example: 'artificial-intelligence' })
  slug: string;

  // Add other fields if needed, e.g., description, iconUrl, etc.
  // For now, keeping it minimal as per request (id, name, slug)
} 