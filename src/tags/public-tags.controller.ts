import { Controller, Get } from '@nestjs/common';
import { TagsService } from './tags.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { TagResponseDto } from './dto/tag-response.dto';
import { Tag as TagModel } from '../../generated/prisma'; // Prisma model

@ApiTags('Public - Tags')
@Controller('tags')
export class PublicTagsController {
  constructor(private readonly tagsService: TagsService) {}

  private mapToResponseDto(tag: TagModel): TagResponseDto {
    return {
      id: tag.id,
      name: tag.name,
      slug: tag.slug,
      // Map other fields if TagResponseDto includes them and TagModel provides them
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get all public tags' })
  @ApiResponse({ status: 200, description: 'List of all public tags.', type: [TagResponseDto] })
  async findAll(): Promise<TagResponseDto[]> {
    const tags = await this.tagsService.findAllPublic();
    return tags.map(tag => this.mapToResponseDto(tag));
  }
} 