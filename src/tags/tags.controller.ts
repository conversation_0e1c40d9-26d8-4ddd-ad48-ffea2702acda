import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, ParseIntPipe, ParseUUIDPipe, NotFoundException } from '@nestjs/common';
import { TagsService } from './tags.service';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { AdminGuard } from '../auth/guards/admin.guard';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';
import { Tag as TagModel } from '../../generated/prisma';
import { TagResponseDto } from './dto/tag-response.dto';
import { AdminTagListResponseDto } from '../admin/tags/dto/admin-tag-list-response.dto';

@ApiTags('Admin - Tags')
@ApiBearerAuth()
@UseGuards(AdminGuard)
@Controller('admin/tags')
export class TagsController {
  constructor(private readonly tagsService: TagsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new tag' })
  @ApiBody({ type: CreateTagDto })
  @ApiResponse({ status: 201, description: 'The tag has been successfully created.', type: TagResponseDto })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 409, description: 'Conflict. Tag with this name or slug already exists.' })
  async create(@Body() createTagDto: CreateTagDto): Promise<TagResponseDto> {
    const tag = await this.tagsService.create(createTagDto);
    return this.mapToTagResponseDto(tag);
  }

  @Get()
  @ApiOperation({ summary: 'Get all tags with pagination' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number for pagination', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiResponse({ status: 200, description: 'List of all tags.', type: AdminTagListResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  async findAll(
    @Query('page', new ParseIntPipe({ optional: true })) page?: number,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
  ): Promise<AdminTagListResponseDto> {
    const { data, count, totalPages, currentPage } = await this.tagsService.findAll(page, limit);
    return {
      data: data.map(this.mapToTagResponseDto),
      total: count,
      page: currentPage,
      limit: limit || 10,
      totalPages,
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a tag by ID' })
  @ApiParam({ name: 'id', type: String, description: 'Tag ID (UUID)', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'The found tag.', type: TagResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Tag not found.' })
  async findOne(@Param('id', ParseUUIDPipe) id: string): Promise<TagResponseDto> {
    const tag = await this.tagsService.findOne(id);
    if (!tag) throw new NotFoundException(`Tag with ID ${id} not found.`);
    return this.mapToTagResponseDto(tag);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a tag by ID' })
  @ApiParam({ name: 'id', type: String, description: 'Tag ID (UUID)', format: 'uuid' })
  @ApiBody({ type: UpdateTagDto })
  @ApiResponse({ status: 200, description: 'The tag has been successfully updated.', type: TagResponseDto })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Tag not found.' })
  @ApiResponse({ status: 409, description: 'Conflict. Tag with this name or slug already exists.' })
  async update(@Param('id', ParseUUIDPipe) id: string, @Body() updateTagDto: UpdateTagDto): Promise<TagResponseDto> {
    const tag = await this.tagsService.update(id, updateTagDto);
    if (!tag) throw new NotFoundException(`Tag with ID ${id} not found or update failed.`);
    return this.mapToTagResponseDto(tag);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a tag by ID' })
  @ApiParam({ name: 'id', type: String, description: 'Tag ID (UUID)', format: 'uuid' })
  @ApiResponse({ status: 200, description: 'The tag has been successfully deleted.', type: TagResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Tag not found.' })
  async remove(@Param('id', ParseUUIDPipe) id: string): Promise<TagResponseDto> {
    const tag = await this.tagsService.remove(id);
    if (!tag) throw new NotFoundException(`Tag with ID ${id} not found or delete failed.`);
    return this.mapToTagResponseDto(tag);
  }

  private mapToTagResponseDto(tag: TagModel): TagResponseDto {
    return {
      id: tag.id,
      name: tag.name,
      slug: tag.slug,
    };
  }
} 