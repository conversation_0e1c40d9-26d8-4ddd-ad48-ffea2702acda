import { Module } from '@nestjs/common';
import { TagsService } from './tags.service';
import { TagsController } from './tags.controller';
import { PublicTagsController } from './public-tags.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [PrismaModule, AuthModule],
  controllers: [TagsController, PublicTagsController],
  providers: [TagsService],
  exports: [TagsService],
})
export class TagsModule {} 