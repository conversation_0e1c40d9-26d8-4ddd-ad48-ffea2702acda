import { Injectable, NotFoundException, ConflictException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { Tag, Prisma } from 'generated/prisma';
import { generateSlug } from '../utils/slug.utils';
import { Logger } from '@nestjs/common';

@Injectable()
export class TagsService {
  private readonly logger = new Logger(TagsService.name);

  constructor(private readonly prisma: PrismaService) {}

  async create(createTagDto: CreateTagDto): Promise<Tag> {
    const slug = createTagDto.slug || generateSlug(createTagDto.name);

    try {
      const tag = await this.prisma.tag.create({
        data: {
          name: createTagDto.name,
          slug: slug,
          description: createTagDto.description,
        },
      });
      return tag;
    } catch (error) {
      this.logger.error(
        `Caught error during TagsService.create. Error type: ${error?.constructor?.name}, Name: ${error?.name}, Message: ${error?.message}`,
        // error // Log the full error object for deep inspection if needed during debugging
      );
      if (error && typeof error === 'object') {
        // Check for PrismaClientKnownRequestError (has a 'code' property starting with 'P')
        if ('code' in error && typeof error.code === 'string' && error.code.startsWith('P')) {
          this.logger.warn(
            `Re-throwing PrismaClientKnownRequestError from TagsService (Code: ${error.code}) for global filter. Target: ${error.meta?.target}`,
          );
          throw error; // Re-throw
        } 
        // Check for PrismaClientValidationError (has a 'name' property)
        else if (error.name === 'PrismaClientValidationError') { 
          this.logger.warn(
            `Re-throwing PrismaClientValidationError from TagsService for global filter.`,
          );
          throw error; // Re-throw
        }
      }
      // Fallback for other errors
      this.logger.error(
        `Unexpected error in TagsService.create for tag '${createTagDto.name}'. Not identified as Prisma error for re-throw. Original error: ${error?.message}`,
        error?.stack,
      );
      throw new InternalServerErrorException('Could not create tag due to an unexpected server issue.');
    }
  }

  async findAll(page: number = 1, limit: number = 10): Promise<{ data: Tag[], count: number, totalPages: number, currentPage: number }> {
    const skip = (page - 1) * limit;
    try {
      const [tags, totalCount] = await this.prisma.$transaction([
        this.prisma.tag.findMany({
          skip,
          take: limit,
          orderBy: {
            name: 'asc', // Default sort by name for tags
          },
        }),
        this.prisma.tag.count(),
      ]);
      return {
        data: tags,
        count: totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
      };
    } catch (error) {
      console.error('Error fetching all tags:', error);
      throw new InternalServerErrorException('Could not fetch tags.');
    }
  }

  async findAllPublic(): Promise<Tag[]> {
    try {
      return await this.prisma.tag.findMany({
        orderBy: {
          name: 'asc',
        },
      });
    } catch (error) {
      this.logger.error('Error fetching all public tags:', error.stack);
      throw new InternalServerErrorException('Could not fetch public tags.');
    }
  }

  async findOne(id: string): Promise<Tag> {
    const tag = await this.prisma.tag.findUnique({
      where: { id },
    });
    if (!tag) {
      throw new NotFoundException(`Tag with ID "${id}" not found`);
    }
    return tag;
  }

  async update(id: string, updateTagDto: UpdateTagDto): Promise<Tag> {
    await this.findOne(id); // Ensures tag exists

    const dataToUpdate: Prisma.TagUpdateInput = {};
    if (updateTagDto.name !== undefined) dataToUpdate.name = updateTagDto.name;
    if (updateTagDto.description !== undefined) dataToUpdate.description = updateTagDto.description;

    if (updateTagDto.slug) {
      dataToUpdate.slug = generateSlug(updateTagDto.slug);
    } else if (updateTagDto.name) {
      dataToUpdate.slug = generateSlug(updateTagDto.name);
    }

    try {
      return await this.prisma.tag.update({
        where: { id },
        data: dataToUpdate,
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new ConflictException(`Tag with this name or slug already exists.`);
        }
        if (error.code === 'P2025') {
          throw new NotFoundException(`Tag with ID "${id}" not found to update.`);
        }
      }
      console.error(`Error updating tag ${id}:`, error);
      throw new InternalServerErrorException('Could not update tag.');
    }
  }

  async remove(id: string): Promise<Tag> {
    await this.findOne(id); // Ensures tag exists

    try {
      // Optional: Check if tag is associated with many entities before deletion
      // const associatedEntitiesCount = await this.prisma.entityTag.count({ where: { tagId: id } });
      // if (associatedEntitiesCount > 0) {
      //   throw new ConflictException(`Tag with ID "${id}" is associated with entities and cannot be deleted. Consider removing associations first.`);
      // }

      return await this.prisma.tag.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2025') {
          throw new NotFoundException(`Tag with ID "${id}" not found to delete.`);
        }
      }
      console.error(`Error removing tag ${id}:`, error);
      throw new InternalServerErrorException('Could not delete tag.');
    }
  }
} 