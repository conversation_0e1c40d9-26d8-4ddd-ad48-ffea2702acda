import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { PrismaService } from '../prisma/prisma.service';
import { EntitiesService } from '../entities/entities.service';
import { Logger } from '@nestjs/common';
import { Prisma } from 'generated/prisma';

const logger = new Logger('BackfillTask');

async function bootstrap() {
  logger.log('--- Starting Backfill Task ---');
  const app = await NestFactory.createApplicationContext(AppModule, {
    logger: ['error', 'warn', 'log'],
  });

  const prisma = app.get(PrismaService);
  const entitiesService = app.get(EntitiesService);

  logger.log('Successfully bootstrapped application context and got services.');

  type EntityToProcess = { id: string; name: string };
  const entitiesToProcess = await prisma.$queryRaw<EntityToProcess[]>`
    SELECT id, name FROM "public"."entities"
    WHERE "ftsDocument" IS NULL OR "vector_embedding" IS NULL
  `;

  logger.log(`Found ${entitiesToProcess.length} entities to backfill.`);

  if (entitiesToProcess.length === 0) {
    logger.log('No entities to backfill. Exiting.');
    await app.close();
    return;
  }

  for (const entity of entitiesToProcess) {
    logger.log(`Processing entity: ${entity.name} (ID: ${entity.id})`);
    try {
      await prisma.$transaction(async (tx) => {
        // 1. Generate the comprehensive text for FTS
        const ftsText = await entitiesService.generateFtsTextForEntity(entity.id, tx);
        logger.log(`- Generated FTS text for ${entity.name}.`);

        // 2. Update the ftsDocument for the entity
        // We use a raw query because Prisma doesn't have a typed API for tsvector functions
        await tx.$executeRaw`
          UPDATE "public"."entities"
          SET "ftsDocument" = to_tsvector('english', ${ftsText})
          WHERE id = ${entity.id}::uuid;
        `;
        logger.log(`- Updated ftsDocument for ${entity.name}.`);

        // 3. Generate and save the vector embedding
        // This method now also uses the generateFtsTextForEntity internally
        await entitiesService.generateAndSaveEmbedding(entity.id, tx);
        logger.log(`- Generated and saved embedding for ${entity.name}.`);
      });
    } catch (error) {
      logger.error(`Failed to process entity ${entity.id}:`, error.stack);
    }
  }

  logger.log('--- Backfill Task Completed ---');
  await app.close();
  process.exit(0);
}

bootstrap().catch(err => {
  logger.error('An unhandled error occurred during the backfill task:', err);
  process.exit(1);
}); 