import { Controller, Get, Post, Patch, Delete, Param, Body, HttpCode, HttpStatus, Query } from '@nestjs/common';
import { TestService } from './test.service';

@Controller('test') // Base path for all routes in this controller
export class TestController {
  constructor(private readonly testService: TestService) {}

  @Post('entity-type/crud')
  @HttpCode(HttpStatus.OK) // Use POST but return OK for test sequence
  async testEntityTypeCrud() {
    console.log('--- Running EntityType CRUD Test ---');
    const result = await this.testService.testCreateReadEntityType();
    // For a real test suite, you'd assert results here.
    // For manual testing, we rely on console logs and manual DB checks.
    console.log('--- CRUD Test Step 1 (Create/Read) Complete --- ');
    // Now test Update using the created slug
    const createdSlug = result.createdType.slug;
    console.log(`--- CRUD Test Step 2 (Update ${createdSlug}) ---`);
    const updated = await this.testService.testUpdateEntityType(createdSlug);
    console.log('--- CRUD Test Step 2 (Update) Complete --- ');
    // Now test Delete
    console.log(`--- CRUD Test Step 3 (Delete ${createdSlug}) ---`);
    const deletionResult = await this.testService.testDeleteEntityType(createdSlug);
    console.log('--- CRUD Test Step 3 (Delete) Complete --- ');
    console.log('--- EntityType CRUD Test Finished ---');
    return { createReadResult: result, updateResult: updated, deleteResult: deletionResult };
  }

  @Post('entity/relation')
  async testEntityRelation() {
     console.log('--- Running Entity Relation Test ---');
     const result = await this.testService.testCreateEntityWithRelation();
     // Remember to manually delete the created entity and potentially the type
     console.log('--- Entity Relation Test Finished --- Please clean up DB manually --- ');
     return result;
  }

  @Post('entity/m2m-relation')
  async testEntityTagM2MRelation() {
     console.log('--- Running Entity-Tag M2M Relation Test ---');
     const result = await this.testService.testEntityTagRelation();
     // Service method now includes cleanup for M2M
     console.log('--- Entity-Tag M2M Relation Test Finished (DB cleanup attempted) --- ');
     return result;
  }

  @Post('constraints/enum')
  async testEnumConstraints() {
      console.log('--- Running ENUM Constraint Test ---');
      const result = await this.testService.testEnums();
      console.log('--- ENUM Constraint Test Finished --- ');
      return result;
  }

  @Post('constraints/unique')
  async testUniqueConstraints() {
      console.log('--- Running Unique Constraint Test ---');
      const result = await this.testService.testUniqueConstraints();
      console.log('--- Unique Constraint Test Finished --- ');
      return result;
  }

  @Post('constraints/optional-default-timestamp')
  async testOptionalDefaultsAndTimestamps() {
      console.log('--- Running Optional/Default/Timestamp Test ---');
      const result = await this.testService.testOptionalDefaultsUpdatedAt();
      console.log('--- Optional/Default/Timestamp Test Finished --- ');
      return result;
  }

  // Example of how you might add specific cleanup endpoints if needed
  // @Delete('cleanup/entity-type/:slug')
  // async cleanupEntityType(@Param('slug') slug: string) {
  //   console.log(`--- Manual Cleanup Request: Delete EntityType ${slug} ---`);
  //   return this.testService.testDeleteEntityType(slug);
  // }
} 