import { Injectable, InternalServerErrorException, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service'; // Adjust path if needed
import { Prisma, EntityType, Entity, Tag, EntityTag, EntityStatus, UserRole } from '../../generated/prisma'; // Adjust path if needed
import { generateSlug } from '../utils/slug.utils';

@Injectable()
export class TestService {
  constructor(private readonly prisma: PrismaService) {}

  // --- Phase 2: Basic CRUD (EntityType) ---

  async testCreateReadEntityType() {
    const slug = 'test-type-prisma-' + Date.now(); // Ensure unique slug for tests
    try {
      console.log(`Attempting to create EntityType with slug: ${slug}`);
      const createdType = await this.prisma.entityType.create({
        data: { name: 'Test Type From Prisma', slug: slug, description: 'Initial description' },
      });
      console.log('Created EntityType:', createdType);

      console.log(`Attempting to read EntityType with slug: ${slug}`);
      const readType = await this.prisma.entityType.findUnique({
        where: { slug: slug },
      });
      console.log('Read EntityType:', readType);

      console.log('Attempting to read all EntityTypes');
      const allTypes = await this.prisma.entityType.findMany();
      console.log('All EntityTypes count:', allTypes.length);

      // Return the specific one created for easier verification
      return { createdType, readType, allTypesCount: allTypes.length };
    } catch (error) {
      console.error('Error during CREATE/READ test:', error);
      throw new InternalServerErrorException('CREATE/READ test failed', error.message);
    }
  }

  async testUpdateEntityType(slug: string) {
    try {
      console.log(`Attempting to update EntityType with slug: ${slug}`);
      const updatedType = await this.prisma.entityType.update({
        where: { slug: slug },
        data: { description: 'Updated description at ' + new Date().toISOString() },
      });
      console.log('Updated EntityType:', updatedType);
      return updatedType;
    } catch (error) {
      console.error(`Error updating EntityType ${slug}:`, error);
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new NotFoundException(`EntityType with slug '${slug}' not found for update.`);
      }
      throw new InternalServerErrorException('UPDATE test failed', error.message);
    }
  }

  async testDeleteEntityType(slug: string) {
    try {
      console.log(`Attempting to delete EntityType with slug: ${slug}`);
      await this.prisma.entityType.delete({
        where: { slug: slug },
      });
      console.log(`Deleted EntityType with slug: ${slug}`);

      // Verify deletion
      const deletedType = await this.prisma.entityType.findUnique({
        where: { slug: slug },
      });
      if (deletedType === null) {
        console.log('Deletion verified.');
        return { message: `EntityType '${slug}' deleted successfully.`, deleted: true };
      } else {
        console.error('Deletion verification failed!');
        throw new InternalServerErrorException('Deletion verification failed');
      }
    } catch (error) {
      console.error(`Error deleting EntityType ${slug}:`, error);
       if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        // Already deleted or never existed, consider success for test idempotentcy?
        console.log(`EntityType with slug '${slug}' not found for deletion (might be already deleted).`);
        return { message: `EntityType '${slug}' not found (likely already deleted).`, deleted: true };
      }
      throw new InternalServerErrorException('DELETE test failed', error.message);
    }
  }

 // --- Phase 3: Relations ---

  async testCreateEntityWithRelation() {
    let entityType: EntityType;
    const typeSlug = 'test-rel-type-prisma';

    try {
      // Ensure EntityType exists
      entityType = await this.ensureTestEntityType(typeSlug, 'Test Relation Type');
      // Check required for TS null analysis
      if (!entityType) throw new InternalServerErrorException('Failed to ensure EntityType');

      const entityName = 'Test Entity with Prisma Relation ' + Date.now();
      console.log(`Attempting to create Entity: ${entityName} linked to Type ID: ${entityType.id}`);

      const createdEntity = await this.prisma.entity.create({
        data: {
          name: entityName,
          slug: generateSlug(entityName),
          websiteUrl: 'https://relation-test.example.com',
          status: EntityStatus.ACTIVE,
          description: 'Testing relations',
          entityType: { connect: { id: entityType.id } },
          submitter: { connect: { id: '123e4567-e89b-12d3-a456-426614174000' } }
        },
        include: { entityType: true }
      });
      console.log('Created Entity with relation:', createdEntity);
      return createdEntity;
    } catch (error) {
      console.error('Error during CREATE with relation test:', error);
      throw new InternalServerErrorException('CREATE relation test failed', error.message);
    }
  }

 async testEntityTagRelation() {
    let entity: Entity | null = null; // Initialize to null for cleanup check
    let tag1: Tag | null = null; // Initialize to null
    let tag2: Tag | null = null; // Initialize to null
    let createdTagSlug: string | null = null; // Track created tag for cleanup
    const entityName = 'test-m2m-entity-' + Date.now();
    const tag1Slug = 'test-m2m-tag1';
    const tag2Slug = 'test-m2m-tag2';

    try {
      // 1. Ensure Entity exists
      const entityType = await this.ensureTestEntityType('test-m2m-type');
      if (!entityType) throw new InternalServerErrorException('Failed to ensure EntityType for M2M test');

      entity = await this.prisma.entity.create({
        data: {
          name: entityName,
          slug: generateSlug(entityName),
          websiteUrl: 'https://m2m-test.com',
          status: EntityStatus.ACTIVE,
          entityTypeId: entityType.id,
          submitterId: '123e4567-e89b-12d3-a456-426614174000'
        }
      });
      console.log('Created test entity:', entity);

      // 2. Ensure Tags exist (Assign to tag1 and tag2)
      tag1 = await this.ensureTestTag(tag1Slug, 'Test M2M Tag 1');
      tag2 = await this.ensureTestTag(tag2Slug, 'Test M2M Tag 2');
      console.log('Ensured test tags exist:', tag1, tag2);

      // 3. Connect Tag 1 to Entity
      console.log(`Connecting ${tag1Slug} to ${entityName}`);
      // Use correct relation field `entityTags`
      const updatedEntity1 = await this.prisma.entity.update({
        where: { id: entity.id },
        data: {
          entityTags: { // Use the relation field name from schema.prisma
            create: [{ // Need to create the junction record
                tagId: tag1.id, assignedBy: '123e4567-e89b-12d3-a456-426614174000' // Use tag1.id
            }]
          }
        },
        // Update include path
        include: { entityTags: { include: { tag: true } } } // Include junction + tag data
      });
      console.log('Updated entity after connecting tag1:', updatedEntity1);
      // Add type annotation for `et` and check correct relation
      // Note: entityTags relation needs to be included in the query to access this data
      // if (!updatedEntity1.entityTags.some((et: EntityTag & { tag: Tag }) => et.tag.slug === tag1Slug)) {
      //    throw new InternalServerErrorException(`Failed to verify connection of ${tag1Slug}`);
      // }

      // 4. Connect Tag 2 and Create a new Tag 3
      const tag3Name = 'New Tag via Entity ' + Date.now();
      createdTagSlug = tag3Name.toLowerCase().replace(/\s+/g, '-'); // Assign created slug
      console.log(`Connecting ${tag2Slug} and creating ${createdTagSlug} for ${entityName}`);
      // Use correct relation field `entityTags`
      const updatedEntity2 = await this.prisma.entity.update({
        where: { id: entity.id },
        data: {
          entityTags: {
            create: [
              { tagId: tag2.id, assignedBy: '123e4567-e89b-12d3-a456-426614174000' }, // Connect Tag 2 (use tag2.id)
              // Note: Cannot create tags through junction table, need to create tag first
              // { tag: { create: { name: tag3Name, slug: createdTagSlug } } } // Create Tag 3
            ]
          }
        },
        // Update include path
        include: { entityTags: { include: { tag: true } } }
      });
      console.log('Updated entity after connecting tag2 and creating tag3:', updatedEntity2);
      // Note: entityTags relation needs to be included in the query to access this data
      // Check correct relation
      // if (!updatedEntity2.entityTags.some((et: EntityTag & { tag: Tag }) => et.tag.slug === tag2Slug)) {
      //    throw new InternalServerErrorException(`Failed to verify connection of ${tag2Slug}`);
      // }
      // Check correct relation
      // if (!updatedEntity2.entityTags.some((et: EntityTag & { tag: Tag }) => et.tag.slug === createdTagSlug)) {
      //    throw new InternalServerErrorException(`Failed to verify creation/connection of ${createdTagSlug}`);
      // }

      // 5. Verify junction table (indirectly via includes)
      const finalEntity = await this.prisma.entity.findUnique({
          where: { id: entity.id },
          // Update include path
          include: { entityTags: { include: { tag: true } } }
      });
       if (!finalEntity) throw new NotFoundException('Final entity not found for verification');
      console.log('Final entity with tags:', finalEntity);
      // Check correct relation
      const tagSlugs = finalEntity.entityTags.map((et: EntityTag & { tag: Tag }) => et.tag.slug);

      // Call cleanup here for successful test run
      const tagIdsToClean = [tag1?.id, tag2?.id].filter((id): id is string => !!id); // Filter out nulls
      if (entity) await this.cleanupM2MTestData(entity.id, tagIdsToClean, createdTagSlug || undefined);

      return { finalEntity, tagSlugs };

    } catch (error) {
      console.error('Error during M2M relation test:', error);
      // Attempt cleanup even on error, only if entity was created
      const tagIdsToClean = [tag1?.id, tag2?.id].filter((id): id is string => !!id); // Filter out nulls
      if (entity) await this.cleanupM2MTestData(entity.id, tagIdsToClean, createdTagSlug || undefined);
      throw new InternalServerErrorException('M2M relation test failed', error.message);
    }
 }

 // --- Phase 4: Constraints & Data Types ---

 async testEnums() {
    const validName = 'test-enum-entity-' + Date.now(); // Entity doesn't have slug
    const invalidStatusValue = 'INVALID_STATUS' as any;
    let createdEntityId: string | null = null; // Initialize to null

    try {
      const entityType = await this.ensureTestEntityType('test-enum-type');
      if (!entityType) throw new InternalServerErrorException('Failed to ensure EntityType for Enum test');

      // Test valid enum
      console.log('Attempting to create entity with valid status ENUM');
      const createdEntity = await this.prisma.entity.create({
        data: {
          name: validName,
          slug: generateSlug(validName),
          websiteUrl: 'https://valid-enum.com',
          status: EntityStatus.PENDING,
          entityTypeId: entityType.id,
          submitterId: '123e4567-e89b-12d3-a456-426614174000'
        }
      });
      createdEntityId = createdEntity.id; // Store ID for cleanup
      console.log('Created entity with valid ENUM status:', createdEntity);

      // Test invalid enum (expect Prisma validation error)
      console.log('Attempting to create entity with invalid status ENUM');
      try {
        const invalidEntityName = 'Test Invalid Enum ' + Date.now();
        await this.prisma.entity.create({
          data: {
            name: invalidEntityName,
            slug: generateSlug(invalidEntityName),
            websiteUrl: 'https://invalid-enum.com',
            status: invalidStatusValue,
            entityTypeId: entityType.id,
            submitterId: '123e4567-e89b-12d3-a456-426614174000'
          }
        });
        // If it reaches here, the test failed!
        throw new InternalServerErrorException('Prisma should have rejected the invalid ENUM value!');
      } catch (enumError) {
         if (enumError instanceof Prisma.PrismaClientValidationError || (enumError instanceof Prisma.PrismaClientKnownRequestError && enumError.message.includes('invalid input value for enum'))) {
            // Accept either validation error or DB constraint error
            console.log('Successfully caught expected error for invalid ENUM:', enumError.message);
         } else {
             // Unexpected error type
             console.error('Caught unexpected error type for invalid enum:', enumError);
             throw enumError; // Re-throw unexpected errors
         }
      }

      // Cleanup the successfully created entity
      if (createdEntityId) { // Check if ID was assigned
          await this.prisma.entity.delete({ where: { id: createdEntityId } });
      }

      return { message: 'ENUM tests passed (valid created, invalid rejected).' };

    } catch (error) {
      console.error('Error during ENUM test:', error);
      // Cleanup attempt only if ID was assigned
      if (createdEntityId) await this.prisma.entity.delete({ where: { id: createdEntityId } }).catch(e => console.error("Cleanup failed:", e));
      throw new InternalServerErrorException('ENUM test failed', error.message);
    }
  }

  async testUniqueConstraints() {
    const uniqueSlug = 'unique-test-type-' + Date.now();
    let createdTypeId: string | null = null; // Initialize to null
    try {
      // Create first entity type
      console.log(`Attempting to create first EntityType with unique slug: ${uniqueSlug}`);
      const firstType = await this.prisma.entityType.create({
        data: { name: 'Unique Type 1', slug: uniqueSlug }
      });
      createdTypeId = firstType.id; // Assign ID
      console.log('Created first type:', firstType);

      // Attempt to create second with the same slug
      console.log(`Attempting to create second EntityType with duplicate slug: ${uniqueSlug}`);
      await this.prisma.entityType.create({
        data: { name: 'Unique Type 2', slug: uniqueSlug }
      });

      // If it reaches here, the constraint failed!
      throw new InternalServerErrorException('Unique constraint test failed: Second creation succeeded unexpectedly.');

    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
        // This is the expected error for unique constraint violation
        console.log('Successfully caught expected unique constraint violation (P2002).');
        // Cleanup the first one, only if ID was assigned
        if (createdTypeId) {
            await this.prisma.entityType.delete({ where: { id: createdTypeId } });
        }
        return { message: 'Unique constraint test passed (duplicate rejected).' };
      } else {
        console.error('Error during unique constraint test was not P2002:', error);
        // Cleanup attempt only if ID was assigned
        if (createdTypeId) await this.prisma.entityType.delete({ where: { id: createdTypeId } }).catch(e => console.error("Cleanup failed:", e));
        throw new InternalServerErrorException('Unique constraint test failed with unexpected error', error.message);
      }
    }
  }

  async testOptionalDefaultsUpdatedAt() {
    const slug = 'optional-default-test-' + Date.now();
    let createdTypeId: string | null = null; // Initialize to null
    try {
        // Assuming EntityType has an optional 'description' and maybe a default value somewhere
        // And assuming it has createdAt/updatedAt managed by DB trigger
        console.log('Creating EntityType omitting optional description');
        const createdType = await this.prisma.entityType.create({
            data: { name: 'Optional Default Test', slug: slug }
        });
        createdTypeId = createdType.id; // Assign ID
        console.log('Created type:', createdType);

        // Check optional field (description is nullable in schema)
        if (createdType.description !== null) {
             console.warn(`Warning: Optional field 'description' was not null after creation: ${createdType.description}`);
             // Not throwing error as null is the expected default if unspecified
        }

        if (!createdType.createdAt || !createdType.updatedAt) {
            throw new InternalServerErrorException('Timestamp test failed: createdAt or updatedAt missing.');
        }
        const initialUpdatedAt = createdType.updatedAt;

        // Wait a bit to ensure updatedAt changes
        await new Promise(resolve => setTimeout(resolve, 50));

        console.log('Updating the EntityType to test updatedAt');
        const updatedType = await this.prisma.entityType.update({
            where: { id: createdTypeId },
            data: { name: 'Optional Default Test Updated' }
        });
        console.log('Updated type:', updatedType);

        if (updatedType.updatedAt <= initialUpdatedAt) {
             throw new InternalServerErrorException('Timestamp test failed: updatedAt did not change after update.');
        }
        console.log(`Timestamps: Initial ${initialUpdatedAt}, Updated ${updatedType.updatedAt}`);

        // Cleanup, only if ID was assigned
        if (createdTypeId) {
            await this.prisma.entityType.delete({ where: { id: createdTypeId }});
        }

        return { message: 'Optional/Default/Timestamp tests passed.', initial: createdType, updated: updatedType };

    } catch (error) {
        console.error('Error during Optional/Default/Timestamp test:', error);
         // Cleanup attempt only if ID was assigned
        if (createdTypeId) await this.prisma.entityType.delete({ where: { id: createdTypeId } }).catch(e => console.error("Cleanup failed:", e));
        throw new InternalServerErrorException('Optional/Default/Timestamp test failed', error.message);
    }
  }

  // --- Helper Methods for Tests ---

  private async ensureTestEntityType(slug: string, name?: string): Promise<EntityType> {
    let entityType = await this.prisma.entityType.findUnique({ where: { slug } });
    if (!entityType) {
      entityType = await this.prisma.entityType.create({
        data: { name: name || `Test Type ${slug}`, slug }
      });
       console.log(`Helper: Created EntityType ${slug}`);
    }
    // Ensure return type matches even if created
    return entityType as EntityType;
  }

 private async ensureTestTag(slug: string, name?: string): Promise<Tag> {
    let tag = await this.prisma.tag.findUnique({ where: { slug } });
    if (!tag) {
        tag = await this.prisma.tag.create({
            data: { name: name || `Test Tag ${slug}`, slug }
        });
        console.log(`Helper: Created Tag ${slug}`);
    }
     // Ensure return type matches even if created
    return tag as Tag;
 }

 // Example cleanup (call this from controller if needed)
 async cleanupM2MTestData(entityId: string, tagIds: string[], createdTagSlug?: string) {
    try {
      console.log(`Cleaning up M2M test data for entity ${entityId}`);
      // Delete junction table entries first
      // Prisma cascades should handle this if set up correctly on delete of Entity/Tag,
      // but explicit deletion is safer if unsure.
      await this.prisma.entityTag.deleteMany({ where: { entityId: entityId } });
      console.log(`Deleted entityTags for entity ${entityId}`);

      // Delete the entity
      await this.prisma.entity.delete({ where: { id: entityId } });
      console.log(`Deleted entity ${entityId}`);

      // Delete tags *only if they were created specifically for this test*
      if (createdTagSlug) {
         await this.prisma.tag.delete({ where: { slug: createdTagSlug } });
         console.log(`Deleted created tag ${createdTagSlug}`);
      }
      // Be careful deleting the pre-existing tags (tagIds) unless sure they are test-only
       // Example if tagIds were strings:
       // await this.prisma.tag.deleteMany({ where: { id: { in: tagIds } } });

    } catch (error) {
        console.error('Error during M2M cleanup:', error);
        // Don't rethrow from cleanup
    }
 }
} 