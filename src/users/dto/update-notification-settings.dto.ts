import { IsBoolean, IsOptional } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateNotificationSettingsDto {
  @ApiPropertyOptional({
    description: 'Receive general email newsletters.',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  emailNewsletter?: boolean;

  @ApiPropertyOptional({
    description: 'Receive email when a new entity is added to a category you follow.',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  emailNewEntityInFollowedCategory?: boolean;

  @ApiPropertyOptional({
    description: 'Receive email when a new entity is added with a tag you follow.',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  emailNewEntityInFollowedTag?: boolean;

  @ApiPropertyOptional({
    description: 'Receive email when a new review is posted on an entity you saved/bookmarked.',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  emailNewReviewOnSavedEntity?: boolean;

  @ApiPropertyOptional({
    description: 'Receive email about updates or news related to an entity you saved/bookmarked.',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  emailUpdatesOnSavedEntity?: boolean;
} 