import { IsString, IsUrl, IsOptional, MaxLength, IsObject, ValidateNested, IsEnum, Matches, MinLength } from 'class-validator';
import { Type } from 'class-transformer'; // Needed for ValidateNested if used for complex objects
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

// Import TechnicalLevel from the generated Prisma client types
import { TechnicalLevel } from '../../../generated/prisma';

export class UpdateProfileDto {
  @ApiPropertyOptional({
    description: 'User\'s display name.',
    example: '<PERSON>',
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  display_name?: string;

  @ApiPropertyOptional({
    description: 'User\'s unique username. Must be 3-30 characters, alphanumeric and underscores only.',
    example: 'john_doe_123',
    minLength: 3,
    maxLength: 30,
    pattern: '^[a-zA-Z0-9_]+$',
  })
  @IsOptional()
  @IsString()
  @MinLength(3, { message: 'Username must be at least 3 characters long' })
  @MaxLength(30, { message: 'Username cannot be longer than 30 characters' })
  @Matches(/^[a-zA-Z0-9_]+$/, {
    message: 'Username can only contain letters, numbers, and underscores'
  })
  username?: string;

  @ApiPropertyOptional({
    description: 'URL to the user\'s profile picture.',
    example: 'https://example.com/profile.jpg',
  })
  @IsOptional()
  @IsUrl({}, { message: 'Profile picture must be a valid URL.' })
  profile_picture_url?: string;

  @ApiPropertyOptional({
    description: 'Short biography of the user.',
    example: 'Loves coding and AI.',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  bio?: string;

  @ApiPropertyOptional({
    description: 'Object containing social media links (e.g., twitter, linkedin).',
    example: { twitter: 'https://twitter.com/johndoe', linkedin: 'https://linkedin.com/in/johndoe' },
    type: 'object',
    additionalProperties: { type: 'string', format: 'url' },
  })
  @IsOptional()
  @IsObject()
  // For more specific validation of keys/values within the object:
  // Consider creating a specific SocialLinksDto and using @ValidateNested()
  // @Type(() => SocialLinksDto) // Requires Type from class-transformer
  social_links?: Record<string, string>; 

  @ApiPropertyOptional({
    description: 'User\'s self-assessed technical level.',
    enum: TechnicalLevel,
    example: TechnicalLevel.INTERMEDIATE,
  })
  @IsOptional()
  @IsEnum(TechnicalLevel, { message: 'Invalid technical level provided.' })
  technical_level?: TechnicalLevel;
} 