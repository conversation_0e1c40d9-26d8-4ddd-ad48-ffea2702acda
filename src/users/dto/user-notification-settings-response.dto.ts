import { ApiProperty } from '@nestjs/swagger';

export class UserNotificationSettingsResponseDto {
  @ApiProperty({ description: 'Unique identifier for the notification settings record.', example: 'clq2q5q7p0001c8e4b9g7h1i3' })
  id: string;

  @ApiProperty({ description: 'User ID associated with these settings.', example: 'clq2q5q7p0000c8e4a9g7h1i2' })
  userId: string;

  @ApiProperty({ description: 'Enable email notifications for new comments on user\'s submitted entities.', example: true, default: true })
  newCommentOnEntity: boolean;

  @ApiProperty({ description: 'Enable email notifications for replies to user\'s comments.', example: true, default: true })
  replyToComment: boolean;

  @ApiProperty({ description: 'Enable email notifications when a submitted entity status changes (e.g., approved, rejected).', example: true, default: true })
  entityStatusChange: boolean;

  @ApiProperty({ description: 'Enable email notifications for new reviews on user\'s submitted entities.', example: true, default: true })
  newReviewOnEntity: boolean;

  @ApiProperty({ description: 'Enable email notifications for general platform announcements.', example: false, default: false })
  platformAnnouncements: boolean;

  @ApiProperty({ description: 'Enable email notifications for a weekly/monthly digest.', example: false, default: false })
  digestSubscription: boolean;

  // Timestamps are good practice, though not strictly in the current Prisma model for UserNotificationSettings
  @ApiProperty({ description: 'Timestamp of when these settings were created.', example: '2023-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to these settings.', example: '2023-01-10T10:00:00.000Z' })
  updatedAt: Date;
} 