import { ApiProperty } from '@nestjs/swagger';
import { UserRole, UserStatus } from '../../../generated/prisma'; // Adjust path as needed

export class UserProfileResponseDto {
  @ApiProperty({ description: 'User ID (from public.users table)', example: 'clq2q5q7p0000c8e4a9g7h1i2' })
  id: string;

  @ApiProperty({ description: 'Auth User ID (from auth.users table)', example: 'a1b2c3d4-e5f6-7890-1234-567890abcdef' })
  authUserId: string;

  @ApiProperty({ description: 'User\'s email address', example: '<EMAIL>' })
  email: string;

  @ApiProperty({ description: 'Unique username', example: 'john_doe_123', required: false, nullable: true })
  username?: string | null;

  @ApiProperty({ description: 'User\'s display name', example: '<PERSON>', required: false, nullable: true })
  displayName?: string | null;

  @ApiProperty({ description: 'URL of the user\'s profile picture', example: 'https://example.com/profile.jpg', required: false, nullable: true })
  profilePictureUrl?: string | null;

  @ApiProperty({ description: 'User\'s biography or short description', example: 'Loves coding and coffee.', required: false, nullable: true })
  bio?: string | null;

  @ApiProperty({ enum: UserStatus, description: 'Current status of the user account', example: UserStatus.ACTIVE })
  status: UserStatus;

  @ApiProperty({ enum: UserRole, description: 'Role of the user within the system', example: UserRole.USER })
  role: UserRole;

  @ApiProperty({ description: 'Timestamp of when the user profile was created', example: '2023-01-01T00:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Timestamp of the last update to the user profile', example: '2023-01-10T10:00:00.000Z' })
  updatedAt: Date;
  
  @ApiProperty({ description: 'Timestamp of the last login from auth.users', example: '2023-01-10T09:00:00.000Z', required: false, nullable: true })
  lastLoginAt?: Date | null; 
} 