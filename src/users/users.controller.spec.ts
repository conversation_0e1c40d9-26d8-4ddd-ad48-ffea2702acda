import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { UserController } from './users.controller';
import { UserService } from './users.service';
import { PrismaService } from '../prisma/prisma.service';
import { SupabaseService } from '../supabase/supabase.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { mockPrismaService } from '../test/mocks/prisma.service.mock';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { UpdateNotificationSettingsDto } from './dto/update-notification-settings.dto';
import { User as UserModel, UserRole, UserStatus, UserNotificationSettings, TechnicalLevel } from '@generated-prisma';

describe('UserController (Integration)', () => {
  let app: INestApplication;
  let userService: UserService;

  const mockUser: UserModel = {
    id: 'user-123',
    authUserId: 'auth-123',
    email: '<EMAIL>',
    username: 'testuser',
    displayName: 'Test User',
    profilePictureUrl: null,
    role: UserRole.USER,
    status: UserStatus.ACTIVE,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    lastLogin: new Date('2023-01-01'),
    isActive: true,
    bio: null,
    location: null,
    website: null,
    socialLinks: null,
    preferences: null,
    emailVerified: true,
    emailVerifiedAt: new Date('2023-01-01'),
    twoFactorEnabled: false,
    lastPasswordChange: new Date('2023-01-01'),
    loginAttempts: 0,
    lockedUntil: null,
    passwordResetToken: null,
    passwordResetExpires: null,
    emailVerificationToken: null,
    emailVerificationExpires: null,
  };

  const mockNotificationSettings: UserNotificationSettings = {
    id: 'settings-123',
    userId: 'user-123',
    newCommentOnEntity: true,
    replyToComment: true,
    entityStatusUpdate: true,
    weeklyDigest: false,
    marketingEmails: false,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  const mockSupabaseService = {
    updateUserMetadata: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserController],
      providers: [
        UserService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: SupabaseService,
          useValue: mockSupabaseService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn((context) => {
          const request = context.switchToHttp().getRequest();
          request.user = mockUser;
          return true;
        }),
      })
      .compile();

    app = module.createNestApplication();
    
    // Apply global validation pipe like in main.ts
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }));

    userService = module.get<UserService>(UserService);
    
    await app.init();
  });

  afterEach(async () => {
    await app.close();
    jest.clearAllMocks();
  });

  describe('GET /users/me', () => {
    it('should return current user profile', async () => {
      jest.spyOn(userService, 'findProfileById').mockResolvedValue(mockUser);

      const response = await request(app.getHttpServer())
        .get('/users/me')
        .expect(200);

      expect(response.body).toEqual({
        id: mockUser.id,
        authUserId: mockUser.authUserId,
        email: mockUser.email,
        username: mockUser.username,
        displayName: mockUser.displayName,
        profilePictureUrl: mockUser.profilePictureUrl,
        bio: mockUser.bio,
        status: mockUser.status,
        role: mockUser.role,
        createdAt: mockUser.createdAt.toISOString(),
        updatedAt: mockUser.updatedAt.toISOString(),
        lastLoginAt: mockUser.lastLogin?.toISOString(),
      });
      expect(userService.findProfileById).toHaveBeenCalledWith(mockUser.id);
    });

    it('should return 404 when user profile not found', async () => {
      jest.spyOn(userService, 'findProfileById').mockResolvedValue(null);

      await request(app.getHttpServer())
        .get('/users/me')
        .expect(404);
    });

    it('should require authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [UserController],
        providers: [
          UserService,
          { provide: PrismaService, useValue: mockPrismaService },
          { provide: SupabaseService, useValue: mockSupabaseService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .get('/users/me')
        .expect(403);

      await testApp.close();
    });
  });

  describe('PUT /users/me', () => {
    const updateProfileDto: UpdateProfileDto = {
      display_name: 'Updated Name',
      username: 'updated_user',
      bio: 'Updated bio',
      technical_level: TechnicalLevel.ADVANCED,
    };

    it('should update user profile successfully', async () => {
      const updatedUser = { ...mockUser, ...updateProfileDto };
      jest.spyOn(userService, 'updateProfile').mockResolvedValue(updatedUser);

      const response = await request(app.getHttpServer())
        .put('/users/me')
        .send(updateProfileDto)
        .expect(200);

      expect(response.body.displayName).toBe(updateProfileDto.display_name);
      expect(response.body.username).toBe(updateProfileDto.username);
      expect(response.body.bio).toBe(updateProfileDto.bio);
      expect(userService.updateProfile).toHaveBeenCalledWith(mockUser.id, updateProfileDto);
    });

    it('should validate input data', async () => {
      const invalidDto = {
        display_name: '', // Empty display name
        username: 'ab', // Too short username
        profile_picture_url: 'invalid-url', // Invalid URL
      };

      await request(app.getHttpServer())
        .put('/users/me')
        .send(invalidDto)
        .expect(400);
    });

    it('should strip non-whitelisted properties', async () => {
      const dtoWithExtraFields = {
        ...updateProfileDto,
        role: UserRole.ADMIN, // Should be stripped
        status: UserStatus.SUSPENDED, // Should be stripped
        maliciousField: 'should be removed',
      };

      const updatedUser = { ...mockUser, ...updateProfileDto };
      jest.spyOn(userService, 'updateProfile').mockResolvedValue(updatedUser);

      await request(app.getHttpServer())
        .put('/users/me')
        .send(dtoWithExtraFields)
        .expect(200);

      // Verify that only whitelisted fields were passed to the service
      expect(userService.updateProfile).toHaveBeenCalledWith(
        mockUser.id,
        expect.not.objectContaining({
          role: expect.anything(),
          status: expect.anything(),
          maliciousField: expect.anything(),
        }),
      );
    });

    it('should require authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [UserController],
        providers: [
          UserService,
          { provide: PrismaService, useValue: mockPrismaService },
          { provide: SupabaseService, useValue: mockSupabaseService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .put('/users/me')
        .send(updateProfileDto)
        .expect(403);

      await testApp.close();
    });

    it('should validate username format', async () => {
      const invalidUsernameDto = {
        username: 'invalid username!', // Contains spaces and special characters
      };

      await request(app.getHttpServer())
        .put('/users/me')
        .send(invalidUsernameDto)
        .expect(400);
    });

    it('should validate technical level enum', async () => {
      const invalidTechnicalLevelDto = {
        technical_level: 'INVALID_LEVEL',
      };

      await request(app.getHttpServer())
        .put('/users/me')
        .send(invalidTechnicalLevelDto)
        .expect(400);
    });
  });

  describe('GET /users/me/preferences', () => {
    it('should return user notification preferences', async () => {
      jest.spyOn(userService, 'findNotificationSettingsByUserId').mockResolvedValue(mockNotificationSettings);

      const response = await request(app.getHttpServer())
        .get('/users/me/preferences')
        .expect(200);

      expect(response.body).toHaveProperty('id');
      expect(response.body).toHaveProperty('userId');
      expect(response.body).toHaveProperty('newCommentOnEntity');
      expect(response.body).toHaveProperty('replyToComment');
      expect(userService.findNotificationSettingsByUserId).toHaveBeenCalledWith(mockUser.id);
    });

    it('should return 404 when notification settings not found', async () => {
      jest.spyOn(userService, 'findNotificationSettingsByUserId').mockResolvedValue(null);

      await request(app.getHttpServer())
        .get('/users/me/preferences')
        .expect(404);
    });

    it('should require authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [UserController],
        providers: [
          UserService,
          { provide: PrismaService, useValue: mockPrismaService },
          { provide: SupabaseService, useValue: mockSupabaseService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .get('/users/me/preferences')
        .expect(403);

      await testApp.close();
    });
  });

  describe('PUT /users/me/preferences', () => {
    const updateNotificationDto: UpdateNotificationSettingsDto = {
      newCommentOnEntity: false,
      replyToComment: true,
      entityStatusUpdate: false,
      weeklyDigest: true,
      marketingEmails: false,
    };

    it('should update notification preferences successfully', async () => {
      const updatedSettings = { ...mockNotificationSettings, ...updateNotificationDto };
      jest.spyOn(userService, 'updateNotificationSettings').mockResolvedValue(updatedSettings);

      const response = await request(app.getHttpServer())
        .put('/users/me/preferences')
        .send(updateNotificationDto)
        .expect(200);

      expect(response.body.newCommentOnEntity).toBe(updateNotificationDto.newCommentOnEntity);
      expect(response.body.weeklyDigest).toBe(updateNotificationDto.weeklyDigest);
      expect(userService.updateNotificationSettings).toHaveBeenCalledWith(mockUser.id, updateNotificationDto);
    });

    it('should validate boolean fields', async () => {
      const invalidDto = {
        newCommentOnEntity: 'not-a-boolean',
        replyToComment: 'also-not-boolean',
      };

      await request(app.getHttpServer())
        .put('/users/me/preferences')
        .send(invalidDto)
        .expect(400);
    });

    it('should allow partial updates', async () => {
      const partialDto = {
        weeklyDigest: false,
      };

      const updatedSettings = { ...mockNotificationSettings, weeklyDigest: false };
      jest.spyOn(userService, 'updateNotificationSettings').mockResolvedValue(updatedSettings);

      await request(app.getHttpServer())
        .put('/users/me/preferences')
        .send(partialDto)
        .expect(200);

      expect(userService.updateNotificationSettings).toHaveBeenCalledWith(mockUser.id, partialDto);
    });

    it('should strip non-whitelisted properties', async () => {
      const dtoWithExtraFields = {
        ...updateNotificationDto,
        maliciousField: 'should be removed',
        userId: 'should-not-be-changeable',
      };

      const updatedSettings = { ...mockNotificationSettings, ...updateNotificationDto };
      jest.spyOn(userService, 'updateNotificationSettings').mockResolvedValue(updatedSettings);

      await request(app.getHttpServer())
        .put('/users/me/preferences')
        .send(dtoWithExtraFields)
        .expect(200);

      expect(userService.updateNotificationSettings).toHaveBeenCalledWith(
        mockUser.id,
        expect.not.objectContaining({
          maliciousField: expect.anything(),
          userId: expect.anything(),
        }),
      );
    });

    it('should require authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [UserController],
        providers: [
          UserService,
          { provide: PrismaService, useValue: mockPrismaService },
          { provide: SupabaseService, useValue: mockSupabaseService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .put('/users/me/preferences')
        .send(updateNotificationDto)
        .expect(403);

      await testApp.close();
    });
  });

  describe('DELETE /users/me', () => {
    it('should soft delete user account successfully', async () => {
      jest.spyOn(userService, 'softDeleteUser').mockResolvedValue(undefined);

      await request(app.getHttpServer())
        .delete('/users/me')
        .expect(200);

      expect(userService.softDeleteUser).toHaveBeenCalledWith(mockUser.id, mockUser.authUserId);
    });

    it('should require authentication', async () => {
      const module = await Test.createTestingModule({
        controllers: [UserController],
        providers: [
          UserService,
          { provide: PrismaService, useValue: mockPrismaService },
          { provide: SupabaseService, useValue: mockSupabaseService },
        ],
      })
        .overrideGuard(JwtAuthGuard)
        .useValue({ canActivate: () => false })
        .compile();

      const testApp = module.createNestApplication();
      await testApp.init();

      await request(testApp.getHttpServer())
        .delete('/users/me')
        .expect(403);

      await testApp.close();
    });

    it('should handle service errors gracefully', async () => {
      jest.spyOn(userService, 'softDeleteUser').mockRejectedValue(new Error('Database error'));

      await request(app.getHttpServer())
        .delete('/users/me')
        .expect(500);
    });
  });
});
