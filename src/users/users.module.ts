import { Module } from '@nestjs/common';
import { UserController } from './users.controller';
import { UserService } from './users.service';
// Assuming PrismaModule exists and exports PrismaService
import { PrismaModule } from '../prisma/prisma.module'; 
import { SupabaseModule } from '../supabase/supabase.module'; // Import SupabaseModule
// import { AuthModule } from '../auth/auth.module'; // Might need if interacting with Auth services

@Module({
  imports: [
    PrismaModule, // Import PrismaModule to make PrismaService available
    SupabaseModule, // Add SupabaseModule to imports
    // forwardRef(() => AuthModule), // Example if AuthModule needed UserModule or vice-versa (circular dependency)
  ],
  controllers: [UserController],
  providers: [UserService],
  exports: [UserService] // Export service if needed by other modules
})
export class UsersModule {}
