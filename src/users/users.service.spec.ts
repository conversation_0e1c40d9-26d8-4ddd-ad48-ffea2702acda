import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, ForbiddenException, BadRequestException, InternalServerErrorException, ConflictException } from '@nestjs/common';
import { UserService } from './users.service';
import { PrismaService } from '../prisma/prisma.service';
import { SupabaseService } from '../supabase/supabase.service';
import { mockPrismaService, resetPrismaMocks, mockPrismaErrors } from '../test/mocks/prisma.service.mock';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { UpdateNotificationSettingsDto } from './dto/update-notification-settings.dto';
import { User, UserRole, UserStatus, UserNotificationSettings, TechnicalLevel, Prisma } from '@generated-prisma';

describe('UserService', () => {
  let service: UserService;
  let prisma: typeof mockPrismaService;
  let supabaseService: jest.Mocked<SupabaseService>;

  const mockUser: User = {
    id: 'user-123',
    authUserId: 'auth-123',
    email: '<EMAIL>',
    username: 'testuser',
    displayName: 'Test User',
    profilePictureUrl: null,
    role: UserRole.USER,
    status: UserStatus.ACTIVE,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    lastLogin: new Date('2023-01-01'),
    isActive: true,
    bio: null,
    location: null,
    website: null,
    socialLinks: null,
    preferences: null,
    emailVerified: true,
    emailVerifiedAt: new Date('2023-01-01'),
    twoFactorEnabled: false,
    lastPasswordChange: new Date('2023-01-01'),
    loginAttempts: 0,
    lockedUntil: null,
    passwordResetToken: null,
    passwordResetExpires: null,
    emailVerificationToken: null,
    emailVerificationExpires: null,
  };

  const mockAdminUser: User = {
    ...mockUser,
    id: 'admin-123',
    role: UserRole.ADMIN,
  };

  const mockNotificationSettings: UserNotificationSettings = {
    id: 'settings-123',
    userId: 'user-123',
    newCommentOnEntity: true,
    replyToComment: true,
    entityStatusUpdate: true,
    weeklyDigest: false,
    marketingEmails: false,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
  };

  const mockSupabaseService = {
    updateUserMetadata: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: SupabaseService,
          useValue: mockSupabaseService,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    prisma = module.get(PrismaService);
    supabaseService = module.get(SupabaseService);
    
    // Reset all mocks before each test
    resetPrismaMocks();
    jest.clearAllMocks();
  });

  describe('findProfileById', () => {
    it('should return a user profile by id', async () => {
      prisma.user.findUnique.mockResolvedValue(mockUser);

      const result = await service.findProfileById(mockUser.id);

      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { id: mockUser.id },
      });
      expect(result).toEqual(mockUser);
    });

    it('should return null when user not found', async () => {
      prisma.user.findUnique.mockResolvedValue(null);

      const result = await service.findProfileById('non-existent-id');

      expect(result).toBeNull();
    });
  });

  describe('updateProfile', () => {
    const updateProfileDto: UpdateProfileDto = {
      display_name: 'Updated Name',
      username: 'updated_user',
      bio: 'Updated bio',
      technical_level: TechnicalLevel.ADVANCED,
    };

    it('should update user profile successfully', async () => {
      const updatedUser = { ...mockUser, ...updateProfileDto };
      
      supabaseService.updateUserMetadata.mockResolvedValue(undefined);
      prisma.user.update.mockResolvedValue(updatedUser);

      const result = await service.updateProfile(mockUser.id, updateProfileDto);

      expect(supabaseService.updateUserMetadata).toHaveBeenCalledWith(
        mockUser.authUserId,
        expect.objectContaining({
          display_name: updateProfileDto.display_name,
        }),
      );
      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: mockUser.id },
        data: expect.objectContaining({
          displayName: updateProfileDto.display_name,
          username: updateProfileDto.username,
          bio: updateProfileDto.bio,
        }),
      });
      expect(result).toEqual(updatedUser);
    });

    it('should throw NotFoundException when user not found', async () => {
      supabaseService.updateUserMetadata.mockResolvedValue(undefined);
      prisma.user.update.mockRejectedValue(mockPrismaErrors.P2025);

      await expect(service.updateProfile('non-existent-id', updateProfileDto)).rejects.toThrow(
        NotFoundException,
      );
    });

    it('should handle username conflicts', async () => {
      supabaseService.updateUserMetadata.mockResolvedValue(undefined);
      prisma.user.update.mockRejectedValue(mockPrismaErrors.P2002);

      await expect(service.updateProfile(mockUser.id, updateProfileDto)).rejects.toThrow();
    });
  });

  describe('findNotificationSettingsByUserId', () => {
    it('should return notification settings for user', async () => {
      prisma.userNotificationSettings.findUnique.mockResolvedValue(mockNotificationSettings);

      const result = await service.findNotificationSettingsByUserId(mockUser.id);

      expect(prisma.userNotificationSettings.findUnique).toHaveBeenCalledWith({
        where: { userId: mockUser.id },
      });
      expect(result).toEqual(mockNotificationSettings);
    });

    it('should return null when settings not found', async () => {
      prisma.userNotificationSettings.findUnique.mockResolvedValue(null);

      const result = await service.findNotificationSettingsByUserId('non-existent-id');

      expect(result).toBeNull();
    });
  });

  describe('updateNotificationSettings', () => {
    const updateDto: UpdateNotificationSettingsDto = {
      newCommentOnEntity: false,
      weeklyDigest: true,
    };

    it('should update notification settings successfully', async () => {
      const updatedSettings = { ...mockNotificationSettings, ...updateDto };
      
      prisma.userNotificationSettings.upsert.mockResolvedValue(updatedSettings);

      const result = await service.updateNotificationSettings(mockUser.id, updateDto);

      expect(prisma.userNotificationSettings.upsert).toHaveBeenCalledWith({
        where: { userId: mockUser.id },
        update: updateDto,
        create: {
          userId: mockUser.id,
          ...updateDto,
        },
      });
      expect(result).toEqual(updatedSettings);
    });
  });

  describe('softDeleteUser', () => {
    it('should soft delete user successfully', async () => {
      const anonymizedUser = {
        ...mockUser,
        status: UserStatus.DELETED,
        username: expect.stringMatching(/^deleted_user_\d+$/),
        email: expect.stringMatching(/^deleted_user_\d+@example\.com$/),
        displayName: 'Deleted User',
        profilePictureUrl: null,
        bio: null,
        socialLinks: Prisma.JsonNull,
      };

      prisma.user.update.mockResolvedValue(anonymizedUser);

      await service.softDeleteUser(mockUser.id, mockUser.authUserId);

      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: mockUser.id },
        data: expect.objectContaining({
          status: UserStatus.DELETED,
          username: expect.stringMatching(/^deleted_user_\d+$/),
          email: expect.stringMatching(/^deleted_user_\d+@example\.com$/),
          displayName: 'Deleted User',
          profilePictureUrl: null,
          bio: null,
          socialLinks: Prisma.JsonNull,
        }),
      });
    });

    it('should throw InternalServerErrorException on database error', async () => {
      prisma.user.update.mockRejectedValue(new Error('Database error'));

      await expect(service.softDeleteUser(mockUser.id, mockUser.authUserId)).rejects.toThrow(
        InternalServerErrorException,
      );
    });
  });

  describe('findAllUsers', () => {
    const mockUsers = [
      {
        id: mockUser.id,
        username: mockUser.username,
        displayName: mockUser.displayName,
        email: mockUser.email,
        role: mockUser.role,
        status: mockUser.status,
        createdAt: mockUser.createdAt,
        updatedAt: mockUser.updatedAt,
        lastLogin: mockUser.lastLogin,
      },
      {
        id: mockAdminUser.id,
        username: mockAdminUser.username,
        displayName: mockAdminUser.displayName,
        email: mockAdminUser.email,
        role: mockAdminUser.role,
        status: mockAdminUser.status,
        createdAt: mockAdminUser.createdAt,
        updatedAt: mockAdminUser.updatedAt,
        lastLogin: mockAdminUser.lastLogin,
      },
    ];

    it('should return paginated users with default options', async () => {
      prisma.user.findMany.mockResolvedValue(mockUsers);
      prisma.user.count.mockResolvedValue(2);

      const result = await service.findAllUsers({});

      expect(result).toEqual({
        users: mockUsers,
        total: 2,
        page: 1,
        limit: 10,
      });

      expect(prisma.user.findMany).toHaveBeenCalledWith({
        where: {},
        skip: 0,
        take: 10,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          username: true,
          displayName: true,
          email: true,
          role: true,
          status: true,
          createdAt: true,
          updatedAt: true,
          lastLogin: true,
        },
      });
    });

    it('should filter users by status', async () => {
      const activeUsers = [mockUsers[0]];
      prisma.user.findMany.mockResolvedValue(activeUsers);
      prisma.user.count.mockResolvedValue(1);

      const result = await service.findAllUsers({
        filterByStatus: UserStatus.ACTIVE,
      });

      expect(result.users).toEqual(activeUsers);
      expect(prisma.user.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { status: UserStatus.ACTIVE },
        }),
      );
    });

    it('should sort users by specified field and order', async () => {
      prisma.user.findMany.mockResolvedValue(mockUsers);
      prisma.user.count.mockResolvedValue(2);

      await service.findAllUsers({
        sortBy: 'username',
        sortOrder: 'asc',
      });

      expect(prisma.user.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBy: { username: 'asc' },
        }),
      );
    });

    it('should handle pagination correctly', async () => {
      prisma.user.findMany.mockResolvedValue([mockUsers[1]]);
      prisma.user.count.mockResolvedValue(2);

      const result = await service.findAllUsers({
        page: 2,
        limit: 1,
      });

      expect(result).toEqual({
        users: [mockUsers[1]],
        total: 2,
        page: 2,
        limit: 1,
      });

      expect(prisma.user.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skip: 1,
          take: 1,
        }),
      );
    });
  });

  describe('updateUserStatus', () => {
    it('should update user status successfully', async () => {
      const targetUser = { ...mockUser, id: 'target-user-123' };
      const updatedUser = { ...targetUser, status: UserStatus.SUSPENDED };

      prisma.user.findUnique.mockResolvedValue(targetUser);
      prisma.user.update.mockResolvedValue(updatedUser);

      const result = await service.updateUserStatus(mockAdminUser, targetUser.id, UserStatus.SUSPENDED);

      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: targetUser.id },
        data: { status: UserStatus.SUSPENDED },
      });
      expect(result).toEqual(updatedUser);
    });

    it('should throw ForbiddenException when admin tries to update own status', async () => {
      await expect(
        service.updateUserStatus(mockAdminUser, mockAdminUser.id, UserStatus.SUSPENDED),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should throw BadRequestException when trying to set status to DELETED', async () => {
      const targetUser = { ...mockUser, id: 'target-user-123' };

      await expect(
        service.updateUserStatus(mockAdminUser, targetUser.id, UserStatus.DELETED),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw NotFoundException when target user not found', async () => {
      prisma.user.findUnique.mockResolvedValue(null);

      await expect(
        service.updateUserStatus(mockAdminUser, 'non-existent-id', UserStatus.SUSPENDED),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('updateUserRole', () => {
    it('should update user role successfully', async () => {
      const targetUser = { ...mockUser, id: 'target-user-123' };
      const updatedUser = { ...targetUser, role: UserRole.MODERATOR };

      prisma.user.findUnique.mockResolvedValue(targetUser);
      prisma.user.update.mockResolvedValue(updatedUser);

      const result = await service.updateUserRole(mockAdminUser, targetUser.id, UserRole.MODERATOR);

      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: targetUser.id },
        data: { role: UserRole.MODERATOR },
      });
      expect(result).toEqual(updatedUser);
    });

    it('should throw ForbiddenException when admin tries to update own role', async () => {
      await expect(
        service.updateUserRole(mockAdminUser, mockAdminUser.id, UserRole.USER),
      ).rejects.toThrow(ForbiddenException);
    });

    it('should throw NotFoundException when target user not found', async () => {
      prisma.user.findUnique.mockResolvedValue(null);

      await expect(
        service.updateUserRole(mockAdminUser, 'non-existent-id', UserRole.MODERATOR),
      ).rejects.toThrow(NotFoundException);
    });

    it('should prevent removing the last admin role', async () => {
      const adminUser = { ...mockUser, role: UserRole.ADMIN };

      prisma.user.findUnique.mockResolvedValue(adminUser);
      prisma.user.count.mockResolvedValue(1); // Only one admin

      await expect(
        service.updateUserRole(mockAdminUser, adminUser.id, UserRole.USER),
      ).rejects.toThrow(ForbiddenException);

      expect(prisma.user.count).toHaveBeenCalledWith({
        where: { role: UserRole.ADMIN },
      });
    });

    it('should allow removing admin role when multiple admins exist', async () => {
      const adminUser = { ...mockUser, role: UserRole.ADMIN };
      const updatedUser = { ...adminUser, role: UserRole.USER };

      prisma.user.findUnique.mockResolvedValue(adminUser);
      prisma.user.count.mockResolvedValue(2); // Multiple admins
      prisma.user.update.mockResolvedValue(updatedUser);

      const result = await service.updateUserRole(mockAdminUser, adminUser.id, UserRole.USER);

      expect(result).toEqual(updatedUser);
    });

    it('should throw InternalServerErrorException on database error', async () => {
      const targetUser = { ...mockUser, id: 'target-user-123' };

      prisma.user.findUnique.mockResolvedValue(targetUser);
      prisma.user.update.mockRejectedValue(new Error('Database error'));

      await expect(
        service.updateUserRole(mockAdminUser, targetUser.id, UserRole.MODERATOR),
      ).rejects.toThrow(InternalServerErrorException);
    });
  });
});
