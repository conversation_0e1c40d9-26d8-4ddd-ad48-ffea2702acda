import { Prisma } from 'generated/prisma';

/**
 * Creates the Prisma input structure for connecting to or creating categories based on a list of IDs.
 * WARNING: The 'create' part `create: { id }` assumes that Categories can be created with only an ID.
 * This will fail if the Category model has other required fields (e.g., 'name') that do not have default values.
 */
export const mapCategoriesToConnectOrCreate = (
  categoryIds?: string[],
): any => {
  if (!categoryIds || categoryIds.length === 0) {
    return undefined;
  }
  return {
    connectOrCreate: categoryIds.map((id) => ({
      where: { id },
      // WARNING: This create operation assumes Category can be created with only an ID.
      // This will likely fail if other fields like 'name' are required for Category.
      create: { id },
    })),
  };
};

/**
 * Creates the Prisma input structure for connecting to or creating tags based on a list of IDs.
 * WARNING: The 'create' part `create: { id }` assumes that Tags can be created with only an ID.
 * This will fail if the Tag model has other required fields (e.g., 'name') that do not have default values.
 */
export const mapTagsToConnectOrCreate = (
  tagIds?: string[],
): any => {
  if (!tagIds || tagIds.length === 0) {
    return undefined;
  }
  return {
    connectOrCreate: tagIds.map((id) => ({
      where: { id },
      // WARNING: This create operation assumes Tag can be created with only an ID.
      // This will likely fail if other fields like 'name' are required for Tag.
      create: { id },
    })),
  };
}; 