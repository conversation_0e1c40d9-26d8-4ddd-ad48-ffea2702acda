

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pg_trgm" WITH SCHEMA "public";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "vector" WITH SCHEMA "public";






CREATE TYPE "public"."action_type" AS ENUM (
    'view_entity',
    'view_category',
    'view_tag',
    'view_profile',
    'save_entity',
    'unsave_entity',
    'create_review',
    'update_review',
    'delete_review',
    'vote_review_helpful',
    'vote_review_unhelpful',
    'follow_category',
    'unfollow_category',
    'follow_tag',
    'unfollow_tag',
    'search',
    'login',
    'logout',
    'submit_entity',
    'request_tool'
);


ALTER TYPE "public"."action_type" OWNER TO "postgres";


CREATE TYPE "public"."affiliate_status" AS ENUM (
    'none',
    'pending_application',
    'approved',
    'rejected',
    'needs_check'
);


ALTER TYPE "public"."affiliate_status" OWNER TO "postgres";


CREATE TYPE "public"."badge_scope" AS ENUM (
    'user',
    'entity',
    'both'
);


ALTER TYPE "public"."badge_scope" OWNER TO "postgres";


CREATE TYPE "public"."entity_status" AS ENUM (
    'pending',
    'active',
    'archived',
    'rejected',
    'needs_review'
);


ALTER TYPE "public"."entity_status" OWNER TO "postgres";


CREATE TYPE "public"."learning_curve" AS ENUM (
    'gentle',
    'moderate',
    'steep'
);


ALTER TYPE "public"."learning_curve" OWNER TO "postgres";


CREATE TYPE "public"."price_range" AS ENUM (
    'free',
    '$',
    '$$',
    '$$$',
    '$$$$'
);


ALTER TYPE "public"."price_range" OWNER TO "postgres";


CREATE TYPE "public"."pricing_model" AS ENUM (
    'free',
    'freemium',
    'paid_subscription',
    'paid_one_time',
    'usage_based',
    'contact_sales',
    'open_source'
);


ALTER TYPE "public"."pricing_model" OWNER TO "postgres";


CREATE TYPE "public"."review_status" AS ENUM (
    'pending',
    'approved',
    'rejected',
    'edited'
);


ALTER TYPE "public"."review_status" OWNER TO "postgres";


CREATE TYPE "public"."technical_level" AS ENUM (
    'beginner',
    'intermediate',
    'advanced',
    'variable'
);


ALTER TYPE "public"."technical_level" OWNER TO "postgres";


CREATE TYPE "public"."user_role" AS ENUM (
    'user',
    'admin',
    'moderator'
);


ALTER TYPE "public"."user_role" OWNER TO "postgres";


CREATE TYPE "public"."user_status" AS ENUM (
    'active',
    'pending_verification',
    'suspended',
    'deleted'
);


ALTER TYPE "public"."user_status" OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."check_user_role"("required_role" "text") RETURNS boolean
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  profile_role public.user_role; -- Use the actual enum type
BEGIN
  -- Get the role from the user's profile based on their auth ID
  SELECT role INTO profile_role
  FROM public.users
  WHERE auth_user_id = auth.uid(); -- auth.uid() gets the ID of the currently authenticated user

  -- Check if a role was found and if it matches the required role
  IF FOUND AND profile_role::text = required_role THEN
    RETURN true;
  ELSE
    RETURN false;
  END IF;
END;
$$;


ALTER FUNCTION "public"."check_user_role"("required_role" "text") OWNER TO "postgres";


COMMENT ON FUNCTION "public"."check_user_role"("required_role" "text") IS 'Checks if the current user has the specified role by querying the public.users table. Requires SECURITY DEFINER.';



CREATE OR REPLACE FUNCTION "public"."current_user_profile_id"() RETURNS "uuid"
    LANGUAGE "sql" SECURITY DEFINER
    AS $$
  SELECT id FROM public.users WHERE auth_user_id = auth.uid();
$$;


ALTER FUNCTION "public"."current_user_profile_id"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."current_user_profile_id"() IS 'Returns the public.users.id for the currently authenticated user (auth.uid()). Requires SECURITY DEFINER.';



CREATE OR REPLACE FUNCTION "public"."handle_new_user"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    SET "search_path" TO 'public'
    AS $$
BEGIN
  INSERT INTO public.users (auth_user_id, email, role, status, display_name) -- Add other default fields as needed
  VALUES (
    NEW.id, 
    NEW.email, 
    'user'::user_role, -- Default role
    'active'::user_status, -- Default status
    COALESCE(NEW.raw_user_meta_data ->> 'display_name', NEW.email) -- Use display_name from meta or email
  );
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."handle_new_user"() OWNER TO "postgres";


CREATE OR REPLACE FUNCTION "public"."trigger_set_timestamp"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;


ALTER FUNCTION "public"."trigger_set_timestamp"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."trigger_set_timestamp"() IS 'Automatically sets updated_at timestamp on table update.';



CREATE OR REPLACE FUNCTION "public"."update_entity_rating_stats"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  target_entity_id uuid;
  new_count int;
  new_avg float;
BEGIN
  IF (TG_OP = 'DELETE') THEN
    target_entity_id := OLD.entity_id;
    -- Ensure we recalculate even if the deleted review wasn't 'approved'
    -- in case it affects an average of zero.
  ELSIF (TG_OP = 'INSERT') THEN
     target_entity_id := NEW.entity_id;
     -- Only proceed if the new review is approved
     IF NEW.status <> 'approved'::review_status THEN RETURN NEW; END IF;
  ELSE -- UPDATE
     target_entity_id := NEW.entity_id;
     -- Only proceed if status changed to/from approved OR if rating changed on an approved review
     IF (OLD.status = NEW.status AND OLD.rating = NEW.rating) THEN RETURN NEW; END IF;
     IF (OLD.status <> 'approved'::review_status AND NEW.status <> 'approved'::review_status) THEN RETURN NEW; END IF;
  END IF;


  -- Recalculate stats only based on 'approved' reviews for the specific entity
  SELECT
    COUNT(*),
    COALESCE(AVG(rating), 0.0)
  INTO
    new_count,
    new_avg
  FROM public.reviews
  WHERE entity_id = target_entity_id AND status = 'approved'::review_status;

  -- Update the parent entity
  UPDATE public.entities
  SET
    review_count = new_count,
    avg_rating = new_avg,
    updated_at = now() -- Also update entity timestamp
  WHERE id = target_entity_id;

  IF (TG_OP = 'DELETE') THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$;


ALTER FUNCTION "public"."update_entity_rating_stats"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."update_entity_rating_stats"() IS 'Updates review_count and avg_rating on entities table when an approved review is added/deleted or status/rating changes.';



CREATE OR REPLACE FUNCTION "public"."update_entity_save_count"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  target_entity_id uuid;
  new_count int;
BEGIN
  IF (TG_OP = 'DELETE') THEN
    target_entity_id := OLD.entity_id;
  ELSE
    target_entity_id := NEW.entity_id;
  END IF;

  -- Recalculate count for the specific entity
  SELECT COUNT(*) INTO new_count
  FROM public.user_saved_entities
  WHERE entity_id = target_entity_id;

  -- Update the parent entity
  UPDATE public.entities
  SET save_count = new_count,
      updated_at = now() -- Also update entity timestamp
  WHERE id = target_entity_id;

  IF (TG_OP = 'DELETE') THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$;


ALTER FUNCTION "public"."update_entity_save_count"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."update_entity_save_count"() IS 'Updates the save_count on the entities table when a user saves or unsaves an entity.';



CREATE OR REPLACE FUNCTION "public"."update_review_helpfulness"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  new_score int;
  target_review_id uuid;
BEGIN
  IF (TG_OP = 'DELETE') THEN
    target_review_id := OLD.review_id;
  ELSE
    target_review_id := NEW.review_id;
  END IF;

  -- Recalculate score for the affected review
  SELECT COALESCE(SUM(vote), 0) INTO new_score
  FROM public.review_votes
  WHERE review_id = target_review_id;

  -- Update the parent review
  UPDATE public.reviews
  SET helpfulness_score = new_score
  WHERE id = target_review_id;

  IF (TG_OP = 'DELETE') THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$;


ALTER FUNCTION "public"."update_review_helpfulness"() OWNER TO "postgres";


COMMENT ON FUNCTION "public"."update_review_helpfulness"() IS 'Updates the helpfulness_score on the reviews table when a vote is added, changed, or removed.';


SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."badge_types" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "slug" "text" NOT NULL,
    "description" "text" NOT NULL,
    "icon_url" "text" NOT NULL,
    "criteria_details" "text",
    "scope" "public"."badge_scope" NOT NULL,
    "is_auto_granted" boolean DEFAULT false NOT NULL,
    "is_manual_granted" boolean DEFAULT true NOT NULL,
    "display_order" integer DEFAULT 0 NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "badge_types_description_not_empty" CHECK (("description" <> ''::"text")),
    CONSTRAINT "badge_types_grantable" CHECK ((("is_auto_granted" = true) OR ("is_manual_granted" = true))),
    CONSTRAINT "badge_types_icon_url_not_empty" CHECK (("icon_url" <> ''::"text")),
    CONSTRAINT "badge_types_name_not_empty" CHECK (("name" <> ''::"text")),
    CONSTRAINT "badge_types_slug_format" CHECK (("slug" ~ '^[a-z0-9]+(?:-[a-z0-9]+)*$'::"text")),
    CONSTRAINT "badge_types_slug_not_empty" CHECK (("slug" <> ''::"text"))
);


ALTER TABLE "public"."badge_types" OWNER TO "postgres";


COMMENT ON TABLE "public"."badge_types" IS 'Defines the different types of badges available on the platform.';



CREATE TABLE IF NOT EXISTS "public"."categories" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "slug" "text" NOT NULL,
    "parent_category_id" "uuid",
    "icon_url" "text",
    "display_order" integer DEFAULT 0 NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "categories_name_not_empty" CHECK (("name" <> ''::"text")),
    CONSTRAINT "categories_slug_format" CHECK (("slug" ~ '^[a-z0-9]+(?:-[a-z0-9]+)*$'::"text")),
    CONSTRAINT "categories_slug_not_empty" CHECK (("slug" <> ''::"text"))
);


ALTER TABLE "public"."categories" OWNER TO "postgres";


COMMENT ON TABLE "public"."categories" IS 'Hierarchical categories for organizing entities. Slugs must be unique.';



CREATE TABLE IF NOT EXISTS "public"."entities" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "entity_type_id" "uuid" NOT NULL,
    "name" "text" NOT NULL,
    "short_description" "text",
    "description" "text",
    "logo_url" "text",
    "website_url" "text" NOT NULL,
    "documentation_url" "text",
    "contact_url" "text",
    "privacy_policy_url" "text",
    "founded_year" integer,
    "social_links" "jsonb",
    "status" "public"."entity_status" DEFAULT 'pending'::"public"."entity_status" NOT NULL,
    "verified" boolean DEFAULT false NOT NULL,
    "featured" boolean DEFAULT false NOT NULL,
    "sponsored" boolean DEFAULT false NOT NULL,
    "platform_vetted" boolean DEFAULT false NOT NULL,
    "avg_rating" double precision DEFAULT 0 NOT NULL,
    "review_count" integer DEFAULT 0 NOT NULL,
    "save_count" integer DEFAULT 0 NOT NULL,
    "click_count" integer DEFAULT 0 NOT NULL,
    "review_summary_ai" "text",
    "verification_token" "uuid",
    "verification_email_sent" timestamp with time zone,
    "verified_at" timestamp with time zone,
    "verified_by" "uuid",
    "affiliate_status" "public"."affiliate_status" DEFAULT 'none'::"public"."affiliate_status" NOT NULL,
    "ref_link" "text",
    "user_submitted" boolean DEFAULT false NOT NULL,
    "submitter_user_id" "uuid",
    "source_url" "text",
    "admin_notes" "text",
    "last_scraped_update" timestamp with time zone,
    "last_manual_update" timestamp with time zone,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "vector_embedding" "public"."vector"(1536),
    CONSTRAINT "check_founded_year" CHECK ((("founded_year" IS NULL) OR (("founded_year" > 1900) AND (("founded_year")::double precision <= "date_part"('year'::"text", "now"())))))
);


ALTER TABLE "public"."entities" OWNER TO "postgres";


COMMENT ON TABLE "public"."entities" IS 'Core table storing common information for all listed entities.';



COMMENT ON COLUMN "public"."entities"."avg_rating" IS 'Denormalized average rating. Requires trigger/logic based on reviews.';



COMMENT ON COLUMN "public"."entities"."review_count" IS 'Denormalized review count. Requires trigger/logic based on reviews.';



COMMENT ON COLUMN "public"."entities"."save_count" IS 'Denormalized save count. Requires trigger/logic based on user_saved_entities.';



COMMENT ON COLUMN "public"."entities"."click_count" IS 'Denormalized click count. Requires app logic to update.';



COMMENT ON COLUMN "public"."entities"."vector_embedding" IS 'Vector embedding for semantic search. Dimensions depend on the model used.';



CREATE TABLE IF NOT EXISTS "public"."entity_badges" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "entity_id" "uuid" NOT NULL,
    "badge_type_id" "uuid" NOT NULL,
    "granted_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "granted_by_user_id" "uuid",
    "notes" "text"
);


ALTER TABLE "public"."entity_badges" OWNER TO "postgres";


COMMENT ON TABLE "public"."entity_badges" IS 'Assigns specific badges (badge_types) to entities.';



CREATE TABLE IF NOT EXISTS "public"."entity_categories" (
    "entity_id" "uuid" NOT NULL,
    "category_id" "uuid" NOT NULL
);


ALTER TABLE "public"."entity_categories" OWNER TO "postgres";


COMMENT ON TABLE "public"."entity_categories" IS 'Associates entities with relevant categories.';



CREATE TABLE IF NOT EXISTS "public"."entity_details_agency" (
    "entity_id" "uuid" NOT NULL,
    "services_offered" "jsonb",
    "industry_focus" "jsonb",
    "target_client_size" "jsonb",
    "target_audience" "jsonb",
    "location_summary" "text",
    "portfolio_url" "text",
    "pricing_info" "text"
);


ALTER TABLE "public"."entity_details_agency" OWNER TO "postgres";


COMMENT ON TABLE "public"."entity_details_agency" IS 'Stores details specific to entities of type Agency/Integrator.';



CREATE TABLE IF NOT EXISTS "public"."entity_details_community" (
    "entity_id" "uuid" NOT NULL,
    "platform" "text",
    "member_count" integer DEFAULT 0,
    "focus_topics" "jsonb",
    "rules_url" "text",
    "invite_url" "text",
    "main_channel_url" "text"
);


ALTER TABLE "public"."entity_details_community" OWNER TO "postgres";


COMMENT ON TABLE "public"."entity_details_community" IS 'Stores details specific to entities of type Community.';



CREATE TABLE IF NOT EXISTS "public"."entity_details_content_creator" (
    "entity_id" "uuid" NOT NULL,
    "creator_name" "text",
    "primary_platform" "text",
    "focus_areas" "jsonb",
    "follower_count" integer DEFAULT 0,
    "example_content_url" "text"
);


ALTER TABLE "public"."entity_details_content_creator" OWNER TO "postgres";


COMMENT ON TABLE "public"."entity_details_content_creator" IS 'Stores details specific to entities of type Content Creator.';



CREATE TABLE IF NOT EXISTS "public"."entity_details_course" (
    "entity_id" "uuid" NOT NULL,
    "instructor_name" "text",
    "duration_text" "text",
    "skill_level" "public"."technical_level",
    "prerequisites" "text",
    "syllabus_url" "text",
    "enrollment_count" integer DEFAULT 0,
    "certificate_available" boolean DEFAULT false
);


ALTER TABLE "public"."entity_details_course" OWNER TO "postgres";


COMMENT ON TABLE "public"."entity_details_course" IS 'Stores details specific to entities of type Course.';



CREATE TABLE IF NOT EXISTS "public"."entity_details_newsletter" (
    "entity_id" "uuid" NOT NULL,
    "frequency" "text",
    "main_topics" "jsonb",
    "archive_url" "text",
    "subscribe_url" "text",
    "author_name" "text",
    "subscriber_count" integer DEFAULT 0
);


ALTER TABLE "public"."entity_details_newsletter" OWNER TO "postgres";


COMMENT ON TABLE "public"."entity_details_newsletter" IS 'Stores details specific to entities of type Newsletter.';



CREATE TABLE IF NOT EXISTS "public"."entity_details_tool" (
    "entity_id" "uuid" NOT NULL,
    "technical_level" "public"."technical_level",
    "learning_curve" "public"."learning_curve",
    "target_audience" "jsonb",
    "has_api" boolean DEFAULT false NOT NULL,
    "api_documentation_url" "text",
    "api_sandbox_url" "text",
    "key_features" "jsonb",
    "use_cases" "jsonb",
    "pricing_model" "public"."pricing_model",
    "price_range" "public"."price_range",
    "pricing_details" "text",
    "pricing_url" "text",
    "has_free_tier" boolean DEFAULT false NOT NULL,
    "platforms" "jsonb",
    "integrations" "jsonb",
    "supported_languages" "jsonb",
    "current_version" "text",
    "last_version_update_date" "date",
    "changelog_url" "text"
);


ALTER TABLE "public"."entity_details_tool" OWNER TO "postgres";


COMMENT ON TABLE "public"."entity_details_tool" IS 'Stores details specific to entities of type AI Tool.';



CREATE TABLE IF NOT EXISTS "public"."entity_tags" (
    "entity_id" "uuid" NOT NULL,
    "tag_id" "uuid" NOT NULL
);


ALTER TABLE "public"."entity_tags" OWNER TO "postgres";


COMMENT ON TABLE "public"."entity_tags" IS 'Associates entities with relevant tags.';



CREATE TABLE IF NOT EXISTS "public"."entity_types" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "slug" "text" NOT NULL,
    "icon_url" "text",
    "display_order" integer DEFAULT 0 NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "entity_types_name_not_empty" CHECK (("name" <> ''::"text")),
    CONSTRAINT "entity_types_slug_format" CHECK (("slug" ~ '^[a-z0-9]+(?:-[a-z0-9]+)*$'::"text")),
    CONSTRAINT "entity_types_slug_not_empty" CHECK (("slug" <> ''::"text"))
);


ALTER TABLE "public"."entity_types" OWNER TO "postgres";


COMMENT ON TABLE "public"."entity_types" IS 'Defines the distinct types of entities listed (e.g., AI Tool, Agency).';



CREATE TABLE IF NOT EXISTS "public"."review_votes" (
    "review_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "vote" smallint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "review_votes_vote_check" CHECK (("vote" = ANY (ARRAY[1, '-1'::integer])))
);


ALTER TABLE "public"."review_votes" OWNER TO "postgres";


COMMENT ON TABLE "public"."review_votes" IS 'Tracks user votes on review helpfulness. Requires trigger/logic to update reviews.helpfulness_score.';



CREATE TABLE IF NOT EXISTS "public"."reviews" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "entity_id" "uuid" NOT NULL,
    "user_id" "uuid" NOT NULL,
    "rating" integer NOT NULL,
    "title" "text",
    "review_text" "text",
    "status" "public"."review_status" DEFAULT 'pending'::"public"."review_status" NOT NULL,
    "helpfulness_score" integer DEFAULT 0 NOT NULL,
    "moderator_user_id" "uuid",
    "moderated_at" timestamp with time zone,
    "moderation_notes" "text",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "reviews_rating_check" CHECK ((("rating" >= 1) AND ("rating" <= 5)))
);


ALTER TABLE "public"."reviews" OWNER TO "postgres";


COMMENT ON TABLE "public"."reviews" IS 'Stores user reviews for entities.';



COMMENT ON COLUMN "public"."reviews"."helpfulness_score" IS 'Denormalized score indicating review helpfulness. Requires trigger/logic based on review_votes.';



CREATE TABLE IF NOT EXISTS "public"."tags" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "name" "text" NOT NULL,
    "description" "text",
    "slug" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    CONSTRAINT "tags_name_not_empty" CHECK (("name" <> ''::"text")),
    CONSTRAINT "tags_slug_format" CHECK (("slug" ~ '^[a-z0-9]+(?:-[a-z0-9]+)*$'::"text")),
    CONSTRAINT "tags_slug_not_empty" CHECK (("slug" <> ''::"text"))
);


ALTER TABLE "public"."tags" OWNER TO "postgres";


COMMENT ON TABLE "public"."tags" IS 'Stores reusable tags for classifying entities (e.g., image-generation, no-code).';



CREATE TABLE IF NOT EXISTS "public"."user_activity_logs" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "action_type" "public"."action_type" NOT NULL,
    "entity_id" "uuid",
    "category_id" "uuid",
    "tag_id" "uuid",
    "review_id" "uuid",
    "target_user_id" "uuid",
    "timestamp" timestamp with time zone DEFAULT "now"() NOT NULL,
    "details" "jsonb",
    CONSTRAINT "check_activity_target_basic" CHECK (("num_nonnulls"("entity_id", "category_id", "tag_id", "review_id", "target_user_id") >= 0))
);


ALTER TABLE "public"."user_activity_logs" OWNER TO "postgres";


COMMENT ON TABLE "public"."user_activity_logs" IS 'Logs user actions performed on the platform for analytics and history.';



COMMENT ON COLUMN "public"."user_activity_logs"."details" IS 'JSONB field for storing action-specific context (e.g., search terms, vote value).';



CREATE TABLE IF NOT EXISTS "public"."user_badges" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "user_id" "uuid" NOT NULL,
    "badge_type_id" "uuid" NOT NULL,
    "granted_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "granted_by_user_id" "uuid",
    "notes" "text"
);


ALTER TABLE "public"."user_badges" OWNER TO "postgres";


COMMENT ON TABLE "public"."user_badges" IS 'Assigns specific badges (badge_types) to users.';



CREATE TABLE IF NOT EXISTS "public"."user_followed_categories" (
    "user_id" "uuid" NOT NULL,
    "category_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."user_followed_categories" OWNER TO "postgres";


COMMENT ON TABLE "public"."user_followed_categories" IS 'Tracks which categories users are following.';



CREATE TABLE IF NOT EXISTS "public"."user_followed_tags" (
    "user_id" "uuid" NOT NULL,
    "tag_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."user_followed_tags" OWNER TO "postgres";


COMMENT ON TABLE "public"."user_followed_tags" IS 'Tracks which tags users are following.';



CREATE TABLE IF NOT EXISTS "public"."user_notification_settings" (
    "user_id" "uuid" NOT NULL,
    "email_newsletter" boolean DEFAULT true NOT NULL,
    "email_new_entity_in_followed_category" boolean DEFAULT true NOT NULL,
    "email_new_entity_in_followed_tag" boolean DEFAULT false NOT NULL,
    "email_new_review_on_saved_entity" boolean DEFAULT true NOT NULL,
    "email_updates_on_saved_entity" boolean DEFAULT false NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."user_notification_settings" OWNER TO "postgres";


COMMENT ON TABLE "public"."user_notification_settings" IS 'Stores user preferences for various types of notifications.';



CREATE TABLE IF NOT EXISTS "public"."user_saved_entities" (
    "user_id" "uuid" NOT NULL,
    "entity_id" "uuid" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."user_saved_entities" OWNER TO "postgres";


COMMENT ON TABLE "public"."user_saved_entities" IS 'Tracks which entities users have saved/bookmarked.';



CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "auth_user_id" "uuid" NOT NULL,
    "username" "text",
    "display_name" "text",
    "email" "text" NOT NULL,
    "role" "public"."user_role" DEFAULT 'user'::"public"."user_role" NOT NULL,
    "status" "public"."user_status" DEFAULT 'active'::"public"."user_status" NOT NULL,
    "technical_level" "public"."technical_level",
    "profile_picture_url" "text",
    "bio" "text",
    "social_links" "jsonb",
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "last_login" timestamp with time zone,
    CONSTRAINT "users_email_not_empty" CHECK (("email" <> ''::"text")),
    CONSTRAINT "users_username_format" CHECK ((("username" IS NULL) OR ("username" ~ '^[a-zA-Z0-9_]{3,20}$'::"text"))),
    CONSTRAINT "users_username_not_empty" CHECK ((("username" IS NULL) OR ("username" <> ''::"text")))
);


ALTER TABLE "public"."users" OWNER TO "postgres";


COMMENT ON TABLE "public"."users" IS 'Stores user profile information, linked to Supabase auth.users table. Email requires sync mechanism.';



ALTER TABLE ONLY "public"."badge_types"
    ADD CONSTRAINT "badge_types_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."badge_types"
    ADD CONSTRAINT "badge_types_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."badge_types"
    ADD CONSTRAINT "badge_types_slug_key" UNIQUE ("slug");



ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_slug_key" UNIQUE ("slug");



ALTER TABLE ONLY "public"."entities"
    ADD CONSTRAINT "entities_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."entities"
    ADD CONSTRAINT "entities_verification_token_key" UNIQUE ("verification_token");



ALTER TABLE ONLY "public"."entity_badges"
    ADD CONSTRAINT "entity_badges_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."entity_badges"
    ADD CONSTRAINT "entity_badges_unique" UNIQUE ("entity_id", "badge_type_id");



ALTER TABLE ONLY "public"."entity_categories"
    ADD CONSTRAINT "entity_categories_pkey" PRIMARY KEY ("entity_id", "category_id");



ALTER TABLE ONLY "public"."entity_details_agency"
    ADD CONSTRAINT "entity_details_agency_pkey" PRIMARY KEY ("entity_id");



ALTER TABLE ONLY "public"."entity_details_community"
    ADD CONSTRAINT "entity_details_community_pkey" PRIMARY KEY ("entity_id");



ALTER TABLE ONLY "public"."entity_details_content_creator"
    ADD CONSTRAINT "entity_details_content_creator_pkey" PRIMARY KEY ("entity_id");



ALTER TABLE ONLY "public"."entity_details_course"
    ADD CONSTRAINT "entity_details_course_pkey" PRIMARY KEY ("entity_id");



ALTER TABLE ONLY "public"."entity_details_newsletter"
    ADD CONSTRAINT "entity_details_newsletter_pkey" PRIMARY KEY ("entity_id");



ALTER TABLE ONLY "public"."entity_details_tool"
    ADD CONSTRAINT "entity_details_tool_pkey" PRIMARY KEY ("entity_id");



ALTER TABLE ONLY "public"."entity_tags"
    ADD CONSTRAINT "entity_tags_pkey" PRIMARY KEY ("entity_id", "tag_id");



ALTER TABLE ONLY "public"."entity_types"
    ADD CONSTRAINT "entity_types_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."entity_types"
    ADD CONSTRAINT "entity_types_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."entity_types"
    ADD CONSTRAINT "entity_types_slug_key" UNIQUE ("slug");



ALTER TABLE ONLY "public"."review_votes"
    ADD CONSTRAINT "review_votes_pkey" PRIMARY KEY ("review_id", "user_id");



ALTER TABLE ONLY "public"."reviews"
    ADD CONSTRAINT "reviews_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."reviews"
    ADD CONSTRAINT "reviews_user_entity_unique" UNIQUE ("user_id", "entity_id");



ALTER TABLE ONLY "public"."tags"
    ADD CONSTRAINT "tags_name_key" UNIQUE ("name");



ALTER TABLE ONLY "public"."tags"
    ADD CONSTRAINT "tags_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."tags"
    ADD CONSTRAINT "tags_slug_key" UNIQUE ("slug");



ALTER TABLE ONLY "public"."user_activity_logs"
    ADD CONSTRAINT "user_activity_logs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_badges"
    ADD CONSTRAINT "user_badges_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."user_badges"
    ADD CONSTRAINT "user_badges_unique" UNIQUE ("user_id", "badge_type_id");



ALTER TABLE ONLY "public"."user_followed_categories"
    ADD CONSTRAINT "user_followed_categories_pkey" PRIMARY KEY ("user_id", "category_id");



ALTER TABLE ONLY "public"."user_followed_tags"
    ADD CONSTRAINT "user_followed_tags_pkey" PRIMARY KEY ("user_id", "tag_id");



ALTER TABLE ONLY "public"."user_notification_settings"
    ADD CONSTRAINT "user_notification_settings_pkey" PRIMARY KEY ("user_id");



ALTER TABLE ONLY "public"."user_saved_entities"
    ADD CONSTRAINT "user_saved_entities_pkey" PRIMARY KEY ("user_id", "entity_id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_auth_user_id_key" UNIQUE ("auth_user_id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_email_key" UNIQUE ("email");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_username_key" UNIQUE ("username");



CREATE INDEX "agency_industry_focus_gin_idx" ON "public"."entity_details_agency" USING "gin" ("industry_focus");



COMMENT ON INDEX "public"."agency_industry_focus_gin_idx" IS 'Index for querying agency industry focus';



CREATE INDEX "agency_services_offered_gin_idx" ON "public"."entity_details_agency" USING "gin" ("services_offered");



COMMENT ON INDEX "public"."agency_services_offered_gin_idx" IS 'Index for querying agency services';



CREATE INDEX "agency_target_audience_gin_idx" ON "public"."entity_details_agency" USING "gin" ("target_audience");



COMMENT ON INDEX "public"."agency_target_audience_gin_idx" IS 'Index for querying agency target audience';



CREATE INDEX "agency_target_client_size_gin_idx" ON "public"."entity_details_agency" USING "gin" ("target_client_size");



COMMENT ON INDEX "public"."agency_target_client_size_gin_idx" IS 'Index for querying agency client size';



CREATE INDEX "badge_types_display_order_idx" ON "public"."badge_types" USING "btree" ("display_order");



CREATE INDEX "badge_types_scope_idx" ON "public"."badge_types" USING "btree" ("scope");



CREATE INDEX "badge_types_slug_idx" ON "public"."badge_types" USING "btree" ("slug");



CREATE INDEX "categories_display_order_idx" ON "public"."categories" USING "btree" ("display_order");



CREATE INDEX "categories_name_trgm_idx" ON "public"."categories" USING "gin" ("name" "public"."gin_trgm_ops");



CREATE INDEX "categories_parent_category_id_idx" ON "public"."categories" USING "btree" ("parent_category_id");



CREATE INDEX "categories_slug_idx" ON "public"."categories" USING "btree" ("slug");



CREATE INDEX "community_focus_topics_gin_idx" ON "public"."entity_details_community" USING "gin" ("focus_topics");



COMMENT ON INDEX "public"."community_focus_topics_gin_idx" IS 'Index for querying community focus topics';



CREATE INDEX "community_platform_idx" ON "public"."entity_details_community" USING "btree" ("platform");



CREATE INDEX "creator_focus_areas_gin_idx" ON "public"."entity_details_content_creator" USING "gin" ("focus_areas");



COMMENT ON INDEX "public"."creator_focus_areas_gin_idx" IS 'Index for querying creator focus areas';



CREATE INDEX "creator_primary_platform_idx" ON "public"."entity_details_content_creator" USING "btree" ("primary_platform");



CREATE INDEX "entities_created_at_idx" ON "public"."entities" USING "btree" ("created_at" DESC);



CREATE INDEX "entities_embedding_idx" ON "public"."entities" USING "hnsw" ("vector_embedding" "public"."vector_cosine_ops");



COMMENT ON INDEX "public"."entities_embedding_idx" IS 'HNSW index for vector similarity search on entity embeddings.';



CREATE INDEX "entities_entity_type_id_idx" ON "public"."entities" USING "btree" ("entity_type_id");



CREATE INDEX "entities_featured_idx" ON "public"."entities" USING "btree" ("featured") WHERE ("featured" = true);



CREATE INDEX "entities_name_trgm_idx" ON "public"."entities" USING "gin" ("name" "public"."gin_trgm_ops");



CREATE INDEX "entities_sponsored_idx" ON "public"."entities" USING "btree" ("sponsored") WHERE ("sponsored" = true);



CREATE INDEX "entities_status_idx" ON "public"."entities" USING "btree" ("status");



CREATE INDEX "entities_submitter_user_id_idx" ON "public"."entities" USING "btree" ("submitter_user_id");



CREATE INDEX "entities_verified_idx" ON "public"."entities" USING "btree" ("verified") WHERE ("verified" = true);



CREATE INDEX "entity_badges_badge_type_id_idx" ON "public"."entity_badges" USING "btree" ("badge_type_id");



CREATE INDEX "entity_badges_entity_id_idx" ON "public"."entity_badges" USING "btree" ("entity_id");



CREATE INDEX "entity_categories_category_id_idx" ON "public"."entity_categories" USING "btree" ("category_id");



CREATE INDEX "entity_tags_tag_id_idx" ON "public"."entity_tags" USING "btree" ("tag_id");



CREATE INDEX "entity_types_display_order_idx" ON "public"."entity_types" USING "btree" ("display_order");



CREATE INDEX "entity_types_slug_idx" ON "public"."entity_types" USING "btree" ("slug");



CREATE INDEX "newsletter_main_topics_gin_idx" ON "public"."entity_details_newsletter" USING "gin" ("main_topics");



COMMENT ON INDEX "public"."newsletter_main_topics_gin_idx" IS 'Index for querying newsletter main topics';



CREATE INDEX "review_votes_user_id_idx" ON "public"."review_votes" USING "btree" ("user_id");



CREATE INDEX "reviews_created_at_idx" ON "public"."reviews" USING "btree" ("created_at" DESC);



CREATE INDEX "reviews_entity_id_idx" ON "public"."reviews" USING "btree" ("entity_id");



CREATE INDEX "reviews_status_idx" ON "public"."reviews" USING "btree" ("status");



CREATE INDEX "reviews_user_id_idx" ON "public"."reviews" USING "btree" ("user_id");



CREATE INDEX "tags_name_trgm_idx" ON "public"."tags" USING "gin" ("name" "public"."gin_trgm_ops");



CREATE INDEX "tags_slug_idx" ON "public"."tags" USING "btree" ("slug");



CREATE INDEX "tool_integrations_gin_idx" ON "public"."entity_details_tool" USING "gin" ("integrations");



COMMENT ON INDEX "public"."tool_integrations_gin_idx" IS 'Index for querying tool integrations';



CREATE INDEX "tool_key_features_gin_idx" ON "public"."entity_details_tool" USING "gin" ("key_features");



COMMENT ON INDEX "public"."tool_key_features_gin_idx" IS 'Index for querying tool key features';



CREATE INDEX "tool_platforms_gin_idx" ON "public"."entity_details_tool" USING "gin" ("platforms");



COMMENT ON INDEX "public"."tool_platforms_gin_idx" IS 'Index for querying tool platforms';



CREATE INDEX "tool_pricing_model_idx" ON "public"."entity_details_tool" USING "btree" ("pricing_model");



CREATE INDEX "tool_target_audience_gin_idx" ON "public"."entity_details_tool" USING "gin" ("target_audience");



COMMENT ON INDEX "public"."tool_target_audience_gin_idx" IS 'Index for querying tool target audience';



CREATE INDEX "tool_technical_level_idx" ON "public"."entity_details_tool" USING "btree" ("technical_level");



CREATE INDEX "tool_use_cases_gin_idx" ON "public"."entity_details_tool" USING "gin" ("use_cases");



COMMENT ON INDEX "public"."tool_use_cases_gin_idx" IS 'Index for querying tool use cases';



CREATE INDEX "ual_action_type_idx" ON "public"."user_activity_logs" USING "btree" ("action_type");



CREATE INDEX "ual_category_id_idx" ON "public"."user_activity_logs" USING "btree" ("category_id") WHERE ("category_id" IS NOT NULL);



CREATE INDEX "ual_entity_id_idx" ON "public"."user_activity_logs" USING "btree" ("entity_id") WHERE ("entity_id" IS NOT NULL);



CREATE INDEX "ual_review_id_idx" ON "public"."user_activity_logs" USING "btree" ("review_id") WHERE ("review_id" IS NOT NULL);



CREATE INDEX "ual_tag_id_idx" ON "public"."user_activity_logs" USING "btree" ("tag_id") WHERE ("tag_id" IS NOT NULL);



CREATE INDEX "ual_target_user_id_idx" ON "public"."user_activity_logs" USING "btree" ("target_user_id") WHERE ("target_user_id" IS NOT NULL);



CREATE INDEX "ual_timestamp_idx" ON "public"."user_activity_logs" USING "btree" ("timestamp" DESC);



CREATE INDEX "ual_user_id_timestamp_idx" ON "public"."user_activity_logs" USING "btree" ("user_id", "timestamp" DESC);



CREATE INDEX "user_badges_badge_type_id_idx" ON "public"."user_badges" USING "btree" ("badge_type_id");



CREATE INDEX "user_badges_user_id_idx" ON "public"."user_badges" USING "btree" ("user_id");



CREATE INDEX "user_followed_categories_category_id_idx" ON "public"."user_followed_categories" USING "btree" ("category_id");



CREATE INDEX "user_followed_categories_user_id_idx" ON "public"."user_followed_categories" USING "btree" ("user_id");



CREATE INDEX "user_followed_tags_tag_id_idx" ON "public"."user_followed_tags" USING "btree" ("tag_id");



CREATE INDEX "user_followed_tags_user_id_idx" ON "public"."user_followed_tags" USING "btree" ("user_id");



CREATE INDEX "user_saved_entities_entity_id_idx" ON "public"."user_saved_entities" USING "btree" ("entity_id");



CREATE INDEX "user_saved_entities_user_id_idx" ON "public"."user_saved_entities" USING "btree" ("user_id");



CREATE INDEX "users_email_idx" ON "public"."users" USING "btree" ("email");



CREATE INDEX "users_role_idx" ON "public"."users" USING "btree" ("role");



CREATE INDEX "users_status_idx" ON "public"."users" USING "btree" ("status");



CREATE INDEX "users_username_idx" ON "public"."users" USING "btree" ("username");



CREATE OR REPLACE TRIGGER "set_badge_types_timestamp" BEFORE UPDATE ON "public"."badge_types" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_set_timestamp"();



CREATE OR REPLACE TRIGGER "set_categories_timestamp" BEFORE UPDATE ON "public"."categories" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_set_timestamp"();



CREATE OR REPLACE TRIGGER "set_entities_timestamp" BEFORE UPDATE ON "public"."entities" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_set_timestamp"();



CREATE OR REPLACE TRIGGER "set_entity_types_timestamp" BEFORE UPDATE ON "public"."entity_types" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_set_timestamp"();



CREATE OR REPLACE TRIGGER "set_reviews_timestamp" BEFORE UPDATE ON "public"."reviews" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_set_timestamp"();



CREATE OR REPLACE TRIGGER "set_tags_timestamp" BEFORE UPDATE ON "public"."tags" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_set_timestamp"();



CREATE OR REPLACE TRIGGER "set_user_notification_settings_timestamp" BEFORE UPDATE ON "public"."user_notification_settings" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_set_timestamp"();



CREATE OR REPLACE TRIGGER "set_users_timestamp" BEFORE UPDATE ON "public"."users" FOR EACH ROW EXECUTE FUNCTION "public"."trigger_set_timestamp"();



CREATE OR REPLACE TRIGGER "trigger_update_entity_rating_stats" AFTER INSERT OR DELETE OR UPDATE OF "status", "rating" ON "public"."reviews" FOR EACH ROW EXECUTE FUNCTION "public"."update_entity_rating_stats"();



CREATE OR REPLACE TRIGGER "trigger_update_entity_save_count" AFTER INSERT OR DELETE ON "public"."user_saved_entities" FOR EACH ROW EXECUTE FUNCTION "public"."update_entity_save_count"();



CREATE OR REPLACE TRIGGER "trigger_update_review_helpfulness" AFTER INSERT OR DELETE OR UPDATE OF "vote" ON "public"."review_votes" FOR EACH ROW EXECUTE FUNCTION "public"."update_review_helpfulness"();



ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_parent_category_id_fkey" FOREIGN KEY ("parent_category_id") REFERENCES "public"."categories"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."entities"
    ADD CONSTRAINT "entities_entity_type_id_fkey" FOREIGN KEY ("entity_type_id") REFERENCES "public"."entity_types"("id");



ALTER TABLE ONLY "public"."entities"
    ADD CONSTRAINT "entities_submitter_user_id_fkey" FOREIGN KEY ("submitter_user_id") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."entities"
    ADD CONSTRAINT "entities_verified_by_fkey" FOREIGN KEY ("verified_by") REFERENCES "auth"."users"("id");



ALTER TABLE ONLY "public"."entity_badges"
    ADD CONSTRAINT "entity_badges_badge_type_id_fkey" FOREIGN KEY ("badge_type_id") REFERENCES "public"."badge_types"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."entity_badges"
    ADD CONSTRAINT "entity_badges_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."entity_badges"
    ADD CONSTRAINT "entity_badges_granted_by_user_id_fkey" FOREIGN KEY ("granted_by_user_id") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."entity_categories"
    ADD CONSTRAINT "entity_categories_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."entity_categories"
    ADD CONSTRAINT "entity_categories_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."entity_details_agency"
    ADD CONSTRAINT "entity_details_agency_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."entity_details_community"
    ADD CONSTRAINT "entity_details_community_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."entity_details_content_creator"
    ADD CONSTRAINT "entity_details_content_creator_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."entity_details_course"
    ADD CONSTRAINT "entity_details_course_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."entity_details_newsletter"
    ADD CONSTRAINT "entity_details_newsletter_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."entity_details_tool"
    ADD CONSTRAINT "entity_details_tool_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."entity_tags"
    ADD CONSTRAINT "entity_tags_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."entity_tags"
    ADD CONSTRAINT "entity_tags_tag_id_fkey" FOREIGN KEY ("tag_id") REFERENCES "public"."tags"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."review_votes"
    ADD CONSTRAINT "review_votes_review_id_fkey" FOREIGN KEY ("review_id") REFERENCES "public"."reviews"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."review_votes"
    ADD CONSTRAINT "review_votes_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."reviews"
    ADD CONSTRAINT "reviews_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."reviews"
    ADD CONSTRAINT "reviews_moderator_user_id_fkey" FOREIGN KEY ("moderator_user_id") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."reviews"
    ADD CONSTRAINT "reviews_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_activity_logs"
    ADD CONSTRAINT "user_activity_logs_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."user_activity_logs"
    ADD CONSTRAINT "user_activity_logs_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."user_activity_logs"
    ADD CONSTRAINT "user_activity_logs_review_id_fkey" FOREIGN KEY ("review_id") REFERENCES "public"."reviews"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."user_activity_logs"
    ADD CONSTRAINT "user_activity_logs_tag_id_fkey" FOREIGN KEY ("tag_id") REFERENCES "public"."tags"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."user_activity_logs"
    ADD CONSTRAINT "user_activity_logs_target_user_id_fkey" FOREIGN KEY ("target_user_id") REFERENCES "public"."users"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."user_activity_logs"
    ADD CONSTRAINT "user_activity_logs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_badges"
    ADD CONSTRAINT "user_badges_badge_type_id_fkey" FOREIGN KEY ("badge_type_id") REFERENCES "public"."badge_types"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_badges"
    ADD CONSTRAINT "user_badges_granted_by_user_id_fkey" FOREIGN KEY ("granted_by_user_id") REFERENCES "public"."users"("id");



ALTER TABLE ONLY "public"."user_badges"
    ADD CONSTRAINT "user_badges_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_followed_categories"
    ADD CONSTRAINT "user_followed_categories_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_followed_categories"
    ADD CONSTRAINT "user_followed_categories_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_followed_tags"
    ADD CONSTRAINT "user_followed_tags_tag_id_fkey" FOREIGN KEY ("tag_id") REFERENCES "public"."tags"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_followed_tags"
    ADD CONSTRAINT "user_followed_tags_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_notification_settings"
    ADD CONSTRAINT "user_notification_settings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_saved_entities"
    ADD CONSTRAINT "user_saved_entities_entity_id_fkey" FOREIGN KEY ("entity_id") REFERENCES "public"."entities"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."user_saved_entities"
    ADD CONSTRAINT "user_saved_entities_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_auth_user_id_fkey" FOREIGN KEY ("auth_user_id") REFERENCES "auth"."users"("id") ON DELETE CASCADE;



CREATE POLICY "Admin access activity_logs" ON "public"."user_activity_logs" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access agency_details" ON "public"."entity_details_agency" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access badge_types" ON "public"."badge_types" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access categories" ON "public"."categories" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access community_details" ON "public"."entity_details_community" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access course_details" ON "public"."entity_details_course" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access creator_details" ON "public"."entity_details_content_creator" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access entities" ON "public"."entities" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access entity_badges" ON "public"."entity_badges" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access entity_types" ON "public"."entity_types" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access followed_categories" ON "public"."user_followed_categories" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access followed_tags" ON "public"."user_followed_tags" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access newsletter_details" ON "public"."entity_details_newsletter" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access notification_settings" ON "public"."user_notification_settings" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access review_votes" ON "public"."review_votes" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access saved_entities" ON "public"."user_saved_entities" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access tags" ON "public"."tags" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access tool_details" ON "public"."entity_details_tool" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access user_badges" ON "public"."user_badges" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin access users" ON "public"."users" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin can delete entities" ON "public"."entities" FOR DELETE USING ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin can update entities" ON "public"."entities" FOR UPDATE USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin manage entity categories" ON "public"."entity_categories" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Admin manage entity tags" ON "public"."entity_tags" USING ("public"."check_user_role"('admin'::"text")) WITH CHECK ("public"."check_user_role"('admin'::"text"));



CREATE POLICY "Allow read access to entity categories based on entity visibili" ON "public"."entity_categories" FOR SELECT USING (((EXISTS ( SELECT 1
   FROM "public"."entities"
  WHERE (("entities"."id" = "entity_categories"."entity_id") AND ("entities"."status" = 'active'::"public"."entity_status")))) OR (("auth"."role"() = 'authenticated'::"text") AND (EXISTS ( SELECT 1
   FROM "public"."entities"
  WHERE (("entities"."id" = "entity_categories"."entity_id") AND ("entities"."submitter_user_id" = "public"."current_user_profile_id"()) AND ("entities"."status" <> 'active'::"public"."entity_status")))))));



CREATE POLICY "Allow read access to entity tags based on entity visibility" ON "public"."entity_tags" FOR SELECT USING (((EXISTS ( SELECT 1
   FROM "public"."entities"
  WHERE (("entities"."id" = "entity_tags"."entity_id") AND ("entities"."status" = 'active'::"public"."entity_status")))) OR (("auth"."role"() = 'authenticated'::"text") AND (EXISTS ( SELECT 1
   FROM "public"."entities"
  WHERE (("entities"."id" = "entity_tags"."entity_id") AND ("entities"."submitter_user_id" = "public"."current_user_profile_id"()) AND ("entities"."status" <> 'active'::"public"."entity_status")))))));



CREATE POLICY "Disallow direct log modification" ON "public"."user_activity_logs" TO "authenticated" USING (false);



CREATE POLICY "Moderators manage reviews" ON "public"."reviews" USING (("public"."check_user_role"('admin'::"text") OR "public"."check_user_role"('moderator'::"text"))) WITH CHECK (("public"."check_user_role"('admin'::"text") OR "public"."check_user_role"('moderator'::"text")));



CREATE POLICY "Public can view active entities" ON "public"."entities" FOR SELECT USING (("status" = 'active'::"public"."entity_status"));



CREATE POLICY "Public read active agency details" ON "public"."entity_details_agency" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."entities"
  WHERE (("entities"."id" = "entity_details_agency"."entity_id") AND ("entities"."status" = 'active'::"public"."entity_status")))));



CREATE POLICY "Public read active community details" ON "public"."entity_details_community" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."entities"
  WHERE (("entities"."id" = "entity_details_community"."entity_id") AND ("entities"."status" = 'active'::"public"."entity_status")))));



CREATE POLICY "Public read active course details" ON "public"."entity_details_course" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."entities"
  WHERE (("entities"."id" = "entity_details_course"."entity_id") AND ("entities"."status" = 'active'::"public"."entity_status")))));



CREATE POLICY "Public read active creator details" ON "public"."entity_details_content_creator" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."entities"
  WHERE (("entities"."id" = "entity_details_content_creator"."entity_id") AND ("entities"."status" = 'active'::"public"."entity_status")))));



CREATE POLICY "Public read active newsletter details" ON "public"."entity_details_newsletter" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."entities"
  WHERE (("entities"."id" = "entity_details_newsletter"."entity_id") AND ("entities"."status" = 'active'::"public"."entity_status")))));



CREATE POLICY "Public read active tool details" ON "public"."entity_details_tool" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."entities"
  WHERE (("entities"."id" = "entity_details_tool"."entity_id") AND ("entities"."status" = 'active'::"public"."entity_status")))));



CREATE POLICY "Public read approved reviews" ON "public"."reviews" FOR SELECT USING (("status" = 'approved'::"public"."review_status"));



CREATE POLICY "Public read badge_types" ON "public"."badge_types" FOR SELECT USING (true);



CREATE POLICY "Public read categories" ON "public"."categories" FOR SELECT USING (true);



CREATE POLICY "Public read entity badges" ON "public"."entity_badges" FOR SELECT USING ((EXISTS ( SELECT 1
   FROM "public"."entities"
  WHERE (("entities"."id" = "entity_badges"."entity_id") AND ("entities"."status" = 'active'::"public"."entity_status")))));



CREATE POLICY "Public read entity_types" ON "public"."entity_types" FOR SELECT USING (true);



CREATE POLICY "Public read tags" ON "public"."tags" FOR SELECT USING (true);



CREATE POLICY "Public read user badges" ON "public"."user_badges" FOR SELECT USING (true);



CREATE POLICY "Submitters can update their own pending entities" ON "public"."entities" FOR UPDATE TO "authenticated" USING ((("submitter_user_id" = "public"."current_user_profile_id"()) AND ("status" = 'pending'::"public"."entity_status"))) WITH CHECK (("submitter_user_id" = "public"."current_user_profile_id"()));



CREATE POLICY "Users can insert entities" ON "public"."entities" FOR INSERT TO "authenticated" WITH CHECK (("submitter_user_id" = "public"."current_user_profile_id"()));



CREATE POLICY "Users can view their own non-active entities" ON "public"."entities" FOR SELECT TO "authenticated" USING ((("submitter_user_id" = "public"."current_user_profile_id"()) AND ("status" <> 'active'::"public"."entity_status")));



CREATE POLICY "Users delete own PENDING reviews" ON "public"."reviews" FOR DELETE TO "authenticated" USING ((("user_id" = "public"."current_user_profile_id"()) AND ("status" = 'pending'::"public"."review_status")));



CREATE POLICY "Users insert reviews" ON "public"."reviews" FOR INSERT TO "authenticated" WITH CHECK (("user_id" = "public"."current_user_profile_id"()));



CREATE POLICY "Users manage own followed categories" ON "public"."user_followed_categories" TO "authenticated" USING (("user_id" = "public"."current_user_profile_id"())) WITH CHECK (("user_id" = "public"."current_user_profile_id"()));



CREATE POLICY "Users manage own followed tags" ON "public"."user_followed_tags" TO "authenticated" USING (("user_id" = "public"."current_user_profile_id"())) WITH CHECK (("user_id" = "public"."current_user_profile_id"()));



CREATE POLICY "Users manage own review votes" ON "public"."review_votes" TO "authenticated" USING (("user_id" = "public"."current_user_profile_id"())) WITH CHECK (("user_id" = "public"."current_user_profile_id"()));



CREATE POLICY "Users manage own saved entities" ON "public"."user_saved_entities" TO "authenticated" USING (("user_id" = "public"."current_user_profile_id"())) WITH CHECK (("user_id" = "public"."current_user_profile_id"()));



CREATE POLICY "Users update own PENDING reviews" ON "public"."reviews" FOR UPDATE TO "authenticated" USING ((("user_id" = "public"."current_user_profile_id"()) AND ("status" = 'pending'::"public"."review_status"))) WITH CHECK (("user_id" = "public"."current_user_profile_id"()));



CREATE POLICY "Users update own notification settings" ON "public"."user_notification_settings" FOR UPDATE TO "authenticated" USING (("user_id" = "public"."current_user_profile_id"())) WITH CHECK (("user_id" = "public"."current_user_profile_id"()));



CREATE POLICY "Users update own profile" ON "public"."users" FOR UPDATE TO "authenticated" USING (("auth_user_id" = "auth"."uid"())) WITH CHECK (("auth_user_id" = "auth"."uid"()));



CREATE POLICY "Users view own activity" ON "public"."user_activity_logs" FOR SELECT TO "authenticated" USING (("user_id" = "public"."current_user_profile_id"()));



CREATE POLICY "Users view own notification settings" ON "public"."user_notification_settings" FOR SELECT TO "authenticated" USING (("user_id" = "public"."current_user_profile_id"()));



CREATE POLICY "Users view own profile" ON "public"."users" FOR SELECT TO "authenticated" USING (("auth_user_id" = "auth"."uid"()));



ALTER TABLE "public"."badge_types" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."categories" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."entities" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."entity_badges" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."entity_categories" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."entity_details_agency" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."entity_details_community" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."entity_details_content_creator" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."entity_details_course" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."entity_details_newsletter" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."entity_details_tool" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."entity_tags" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."entity_types" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."review_votes" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."reviews" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."tags" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_activity_logs" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_badges" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_followed_categories" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_followed_tags" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_notification_settings" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."user_saved_entities" ENABLE ROW LEVEL SECURITY;


ALTER TABLE "public"."users" ENABLE ROW LEVEL SECURITY;




ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_in"("cstring") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_in"("cstring") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_in"("cstring") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_in"("cstring") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_out"("public"."gtrgm") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_out"("public"."gtrgm") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_out"("public"."gtrgm") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_out"("public"."gtrgm") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_in"("cstring", "oid", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_in"("cstring", "oid", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_in"("cstring", "oid", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_in"("cstring", "oid", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_out"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_out"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_out"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_out"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_recv"("internal", "oid", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_recv"("internal", "oid", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_recv"("internal", "oid", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_recv"("internal", "oid", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_send"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_send"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_send"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_send"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_typmod_in"("cstring"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_typmod_in"("cstring"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_typmod_in"("cstring"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_typmod_in"("cstring"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_in"("cstring", "oid", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_in"("cstring", "oid", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_in"("cstring", "oid", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_in"("cstring", "oid", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_out"("public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_out"("public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_out"("public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_out"("public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_recv"("internal", "oid", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_recv"("internal", "oid", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_recv"("internal", "oid", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_recv"("internal", "oid", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_send"("public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_send"("public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_send"("public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_send"("public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_typmod_in"("cstring"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_typmod_in"("cstring"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_typmod_in"("cstring"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_typmod_in"("cstring"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_in"("cstring", "oid", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_in"("cstring", "oid", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_in"("cstring", "oid", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_in"("cstring", "oid", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_out"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_out"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_out"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_out"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_recv"("internal", "oid", integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_recv"("internal", "oid", integer) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_recv"("internal", "oid", integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_recv"("internal", "oid", integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_send"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_send"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_send"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_send"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_typmod_in"("cstring"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_typmod_in"("cstring"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_typmod_in"("cstring"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_typmod_in"("cstring"[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_halfvec"(real[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(real[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(real[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(real[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(real[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(real[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(real[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(real[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_vector"(real[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_vector"(real[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_vector"(real[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_vector"(real[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_halfvec"(double precision[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(double precision[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(double precision[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(double precision[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(double precision[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(double precision[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(double precision[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(double precision[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_vector"(double precision[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_vector"(double precision[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_vector"(double precision[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_vector"(double precision[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_halfvec"(integer[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(integer[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(integer[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(integer[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(integer[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(integer[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(integer[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(integer[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_vector"(integer[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_vector"(integer[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_vector"(integer[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_vector"(integer[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_halfvec"(numeric[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(numeric[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(numeric[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_halfvec"(numeric[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(numeric[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(numeric[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(numeric[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_sparsevec"(numeric[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."array_to_vector"(numeric[], integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."array_to_vector"(numeric[], integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."array_to_vector"(numeric[], integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."array_to_vector"(numeric[], integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_to_float4"("public"."halfvec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_to_float4"("public"."halfvec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_to_float4"("public"."halfvec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_to_float4"("public"."halfvec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec"("public"."halfvec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec"("public"."halfvec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec"("public"."halfvec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec"("public"."halfvec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_to_sparsevec"("public"."halfvec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_to_sparsevec"("public"."halfvec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_to_sparsevec"("public"."halfvec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_to_sparsevec"("public"."halfvec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_to_vector"("public"."halfvec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_to_vector"("public"."halfvec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_to_vector"("public"."halfvec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_to_vector"("public"."halfvec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_to_halfvec"("public"."sparsevec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_to_halfvec"("public"."sparsevec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_to_halfvec"("public"."sparsevec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_to_halfvec"("public"."sparsevec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec"("public"."sparsevec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec"("public"."sparsevec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec"("public"."sparsevec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec"("public"."sparsevec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_to_vector"("public"."sparsevec", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_to_vector"("public"."sparsevec", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_to_vector"("public"."sparsevec", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_to_vector"("public"."sparsevec", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_to_float4"("public"."vector", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_to_float4"("public"."vector", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_to_float4"("public"."vector", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_to_float4"("public"."vector", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_to_halfvec"("public"."vector", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_to_halfvec"("public"."vector", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_to_halfvec"("public"."vector", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_to_halfvec"("public"."vector", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_to_sparsevec"("public"."vector", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_to_sparsevec"("public"."vector", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_to_sparsevec"("public"."vector", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_to_sparsevec"("public"."vector", integer, boolean) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector"("public"."vector", integer, boolean) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector"("public"."vector", integer, boolean) TO "anon";
GRANT ALL ON FUNCTION "public"."vector"("public"."vector", integer, boolean) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector"("public"."vector", integer, boolean) TO "service_role";











































































































































































GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."binary_quantize"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."check_user_role"("required_role" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."check_user_role"("required_role" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."check_user_role"("required_role" "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."cosine_distance"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."current_user_profile_id"() TO "anon";
GRANT ALL ON FUNCTION "public"."current_user_profile_id"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."current_user_profile_id"() TO "service_role";



GRANT ALL ON FUNCTION "public"."gin_extract_query_trgm"("text", "internal", smallint, "internal", "internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gin_extract_query_trgm"("text", "internal", smallint, "internal", "internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gin_extract_query_trgm"("text", "internal", smallint, "internal", "internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gin_extract_query_trgm"("text", "internal", smallint, "internal", "internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gin_extract_value_trgm"("text", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gin_extract_value_trgm"("text", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gin_extract_value_trgm"("text", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gin_extract_value_trgm"("text", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gin_trgm_consistent"("internal", smallint, "text", integer, "internal", "internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gin_trgm_consistent"("internal", smallint, "text", integer, "internal", "internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gin_trgm_consistent"("internal", smallint, "text", integer, "internal", "internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gin_trgm_consistent"("internal", smallint, "text", integer, "internal", "internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gin_trgm_triconsistent"("internal", smallint, "text", integer, "internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gin_trgm_triconsistent"("internal", smallint, "text", integer, "internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gin_trgm_triconsistent"("internal", smallint, "text", integer, "internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gin_trgm_triconsistent"("internal", smallint, "text", integer, "internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_compress"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_compress"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_compress"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_compress"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_consistent"("internal", "text", smallint, "oid", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_consistent"("internal", "text", smallint, "oid", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_consistent"("internal", "text", smallint, "oid", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_consistent"("internal", "text", smallint, "oid", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_decompress"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_decompress"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_decompress"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_decompress"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_distance"("internal", "text", smallint, "oid", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_distance"("internal", "text", smallint, "oid", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_distance"("internal", "text", smallint, "oid", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_distance"("internal", "text", smallint, "oid", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_options"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_options"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_options"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_options"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_penalty"("internal", "internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_penalty"("internal", "internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_penalty"("internal", "internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_penalty"("internal", "internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_picksplit"("internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_picksplit"("internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_picksplit"("internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_picksplit"("internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_same"("public"."gtrgm", "public"."gtrgm", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_same"("public"."gtrgm", "public"."gtrgm", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_same"("public"."gtrgm", "public"."gtrgm", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_same"("public"."gtrgm", "public"."gtrgm", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."gtrgm_union"("internal", "internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."gtrgm_union"("internal", "internal") TO "anon";
GRANT ALL ON FUNCTION "public"."gtrgm_union"("internal", "internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."gtrgm_union"("internal", "internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_accum"(double precision[], "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_accum"(double precision[], "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_accum"(double precision[], "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_accum"(double precision[], "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_add"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_add"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_add"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_add"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_avg"(double precision[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_avg"(double precision[]) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_avg"(double precision[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_avg"(double precision[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_cmp"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_cmp"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_cmp"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_cmp"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_combine"(double precision[], double precision[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_combine"(double precision[], double precision[]) TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_combine"(double precision[], double precision[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_combine"(double precision[], double precision[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_concat"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_concat"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_concat"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_concat"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_eq"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_eq"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_eq"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_eq"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_ge"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_ge"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_ge"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_ge"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_gt"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_gt"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_gt"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_gt"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_l2_squared_distance"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_l2_squared_distance"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_l2_squared_distance"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_l2_squared_distance"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_le"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_le"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_le"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_le"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_lt"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_lt"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_lt"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_lt"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_mul"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_mul"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_mul"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_mul"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_ne"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_ne"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_ne"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_ne"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_negative_inner_product"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_negative_inner_product"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_negative_inner_product"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_negative_inner_product"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_spherical_distance"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_spherical_distance"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_spherical_distance"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_spherical_distance"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."halfvec_sub"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."halfvec_sub"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."halfvec_sub"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."halfvec_sub"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."hamming_distance"(bit, bit) TO "postgres";
GRANT ALL ON FUNCTION "public"."hamming_distance"(bit, bit) TO "anon";
GRANT ALL ON FUNCTION "public"."hamming_distance"(bit, bit) TO "authenticated";
GRANT ALL ON FUNCTION "public"."hamming_distance"(bit, bit) TO "service_role";



GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "anon";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."handle_new_user"() TO "service_role";



GRANT ALL ON FUNCTION "public"."hnsw_bit_support"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."hnsw_bit_support"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."hnsw_bit_support"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."hnsw_bit_support"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."hnsw_halfvec_support"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."hnsw_halfvec_support"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."hnsw_halfvec_support"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."hnsw_halfvec_support"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."hnsw_sparsevec_support"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."hnsw_sparsevec_support"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."hnsw_sparsevec_support"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."hnsw_sparsevec_support"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."hnswhandler"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."hnswhandler"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."hnswhandler"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."hnswhandler"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."inner_product"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."inner_product"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."inner_product"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."inner_product"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."ivfflat_bit_support"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ivfflat_bit_support"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ivfflat_bit_support"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ivfflat_bit_support"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ivfflat_halfvec_support"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ivfflat_halfvec_support"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ivfflat_halfvec_support"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ivfflat_halfvec_support"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."ivfflathandler"("internal") TO "postgres";
GRANT ALL ON FUNCTION "public"."ivfflathandler"("internal") TO "anon";
GRANT ALL ON FUNCTION "public"."ivfflathandler"("internal") TO "authenticated";
GRANT ALL ON FUNCTION "public"."ivfflathandler"("internal") TO "service_role";



GRANT ALL ON FUNCTION "public"."jaccard_distance"(bit, bit) TO "postgres";
GRANT ALL ON FUNCTION "public"."jaccard_distance"(bit, bit) TO "anon";
GRANT ALL ON FUNCTION "public"."jaccard_distance"(bit, bit) TO "authenticated";
GRANT ALL ON FUNCTION "public"."jaccard_distance"(bit, bit) TO "service_role";



GRANT ALL ON FUNCTION "public"."l1_distance"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l1_distance"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l1_distance"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l1_distance"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_distance"("public"."halfvec", "public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."halfvec", "public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."halfvec", "public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."halfvec", "public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_distance"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_distance"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_distance"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_norm"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_norm"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_norm"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_norm"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_norm"("public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_norm"("public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_norm"("public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_norm"("public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."l2_normalize"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."set_limit"(real) TO "postgres";
GRANT ALL ON FUNCTION "public"."set_limit"(real) TO "anon";
GRANT ALL ON FUNCTION "public"."set_limit"(real) TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_limit"(real) TO "service_role";



GRANT ALL ON FUNCTION "public"."show_limit"() TO "postgres";
GRANT ALL ON FUNCTION "public"."show_limit"() TO "anon";
GRANT ALL ON FUNCTION "public"."show_limit"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."show_limit"() TO "service_role";



GRANT ALL ON FUNCTION "public"."show_trgm"("text") TO "postgres";
GRANT ALL ON FUNCTION "public"."show_trgm"("text") TO "anon";
GRANT ALL ON FUNCTION "public"."show_trgm"("text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."show_trgm"("text") TO "service_role";



GRANT ALL ON FUNCTION "public"."similarity"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."similarity"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."similarity"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."similarity"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."similarity_dist"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."similarity_dist"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."similarity_dist"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."similarity_dist"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."similarity_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."similarity_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."similarity_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."similarity_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_cmp"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_cmp"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_cmp"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_cmp"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_eq"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_eq"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_eq"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_eq"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_ge"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_ge"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_ge"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_ge"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_gt"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_gt"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_gt"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_gt"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_l2_squared_distance"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_l2_squared_distance"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_l2_squared_distance"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_l2_squared_distance"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_le"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_le"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_le"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_le"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_lt"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_lt"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_lt"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_lt"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_ne"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_ne"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_ne"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_ne"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sparsevec_negative_inner_product"("public"."sparsevec", "public"."sparsevec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sparsevec_negative_inner_product"("public"."sparsevec", "public"."sparsevec") TO "anon";
GRANT ALL ON FUNCTION "public"."sparsevec_negative_inner_product"("public"."sparsevec", "public"."sparsevec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sparsevec_negative_inner_product"("public"."sparsevec", "public"."sparsevec") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity_commutator_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_commutator_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_commutator_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_commutator_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_commutator_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_commutator_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_commutator_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_commutator_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_dist_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."strict_word_similarity_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."strict_word_similarity_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."subvector"("public"."halfvec", integer, integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."subvector"("public"."halfvec", integer, integer) TO "anon";
GRANT ALL ON FUNCTION "public"."subvector"("public"."halfvec", integer, integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."subvector"("public"."halfvec", integer, integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."subvector"("public"."vector", integer, integer) TO "postgres";
GRANT ALL ON FUNCTION "public"."subvector"("public"."vector", integer, integer) TO "anon";
GRANT ALL ON FUNCTION "public"."subvector"("public"."vector", integer, integer) TO "authenticated";
GRANT ALL ON FUNCTION "public"."subvector"("public"."vector", integer, integer) TO "service_role";



GRANT ALL ON FUNCTION "public"."trigger_set_timestamp"() TO "anon";
GRANT ALL ON FUNCTION "public"."trigger_set_timestamp"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."trigger_set_timestamp"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_entity_rating_stats"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_entity_rating_stats"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_entity_rating_stats"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_entity_save_count"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_entity_save_count"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_entity_save_count"() TO "service_role";



GRANT ALL ON FUNCTION "public"."update_review_helpfulness"() TO "anon";
GRANT ALL ON FUNCTION "public"."update_review_helpfulness"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_review_helpfulness"() TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_accum"(double precision[], "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_accum"(double precision[], "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_accum"(double precision[], "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_accum"(double precision[], "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_add"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_add"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_add"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_add"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_avg"(double precision[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_avg"(double precision[]) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_avg"(double precision[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_avg"(double precision[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_cmp"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_cmp"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_cmp"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_cmp"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_combine"(double precision[], double precision[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_combine"(double precision[], double precision[]) TO "anon";
GRANT ALL ON FUNCTION "public"."vector_combine"(double precision[], double precision[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_combine"(double precision[], double precision[]) TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_concat"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_concat"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_concat"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_concat"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_dims"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_dims"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_dims"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_dims"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_dims"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_dims"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_dims"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_dims"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_eq"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_eq"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_eq"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_eq"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_ge"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_ge"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_ge"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_ge"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_gt"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_gt"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_gt"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_gt"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_l2_squared_distance"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_l2_squared_distance"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_l2_squared_distance"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_l2_squared_distance"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_le"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_le"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_le"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_le"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_lt"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_lt"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_lt"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_lt"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_mul"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_mul"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_mul"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_mul"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_ne"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_ne"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_ne"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_ne"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_negative_inner_product"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_negative_inner_product"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_negative_inner_product"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_negative_inner_product"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_norm"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_norm"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_norm"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_norm"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_spherical_distance"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_spherical_distance"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_spherical_distance"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_spherical_distance"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."vector_sub"("public"."vector", "public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."vector_sub"("public"."vector", "public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."vector_sub"("public"."vector", "public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."vector_sub"("public"."vector", "public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity_commutator_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity_commutator_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity_commutator_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity_commutator_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity_dist_commutator_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_commutator_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_commutator_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_commutator_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity_dist_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity_dist_op"("text", "text") TO "service_role";



GRANT ALL ON FUNCTION "public"."word_similarity_op"("text", "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."word_similarity_op"("text", "text") TO "anon";
GRANT ALL ON FUNCTION "public"."word_similarity_op"("text", "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."word_similarity_op"("text", "text") TO "service_role";












GRANT ALL ON FUNCTION "public"."avg"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."avg"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."avg"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."avg"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."avg"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."avg"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."avg"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."avg"("public"."vector") TO "service_role";



GRANT ALL ON FUNCTION "public"."sum"("public"."halfvec") TO "postgres";
GRANT ALL ON FUNCTION "public"."sum"("public"."halfvec") TO "anon";
GRANT ALL ON FUNCTION "public"."sum"("public"."halfvec") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sum"("public"."halfvec") TO "service_role";



GRANT ALL ON FUNCTION "public"."sum"("public"."vector") TO "postgres";
GRANT ALL ON FUNCTION "public"."sum"("public"."vector") TO "anon";
GRANT ALL ON FUNCTION "public"."sum"("public"."vector") TO "authenticated";
GRANT ALL ON FUNCTION "public"."sum"("public"."vector") TO "service_role";









GRANT ALL ON TABLE "public"."badge_types" TO "anon";
GRANT ALL ON TABLE "public"."badge_types" TO "authenticated";
GRANT ALL ON TABLE "public"."badge_types" TO "service_role";



GRANT ALL ON TABLE "public"."categories" TO "anon";
GRANT ALL ON TABLE "public"."categories" TO "authenticated";
GRANT ALL ON TABLE "public"."categories" TO "service_role";



GRANT ALL ON TABLE "public"."entities" TO "anon";
GRANT ALL ON TABLE "public"."entities" TO "authenticated";
GRANT ALL ON TABLE "public"."entities" TO "service_role";



GRANT ALL ON TABLE "public"."entity_badges" TO "anon";
GRANT ALL ON TABLE "public"."entity_badges" TO "authenticated";
GRANT ALL ON TABLE "public"."entity_badges" TO "service_role";



GRANT ALL ON TABLE "public"."entity_categories" TO "anon";
GRANT ALL ON TABLE "public"."entity_categories" TO "authenticated";
GRANT ALL ON TABLE "public"."entity_categories" TO "service_role";



GRANT ALL ON TABLE "public"."entity_details_agency" TO "anon";
GRANT ALL ON TABLE "public"."entity_details_agency" TO "authenticated";
GRANT ALL ON TABLE "public"."entity_details_agency" TO "service_role";



GRANT ALL ON TABLE "public"."entity_details_community" TO "anon";
GRANT ALL ON TABLE "public"."entity_details_community" TO "authenticated";
GRANT ALL ON TABLE "public"."entity_details_community" TO "service_role";



GRANT ALL ON TABLE "public"."entity_details_content_creator" TO "anon";
GRANT ALL ON TABLE "public"."entity_details_content_creator" TO "authenticated";
GRANT ALL ON TABLE "public"."entity_details_content_creator" TO "service_role";



GRANT ALL ON TABLE "public"."entity_details_course" TO "anon";
GRANT ALL ON TABLE "public"."entity_details_course" TO "authenticated";
GRANT ALL ON TABLE "public"."entity_details_course" TO "service_role";



GRANT ALL ON TABLE "public"."entity_details_newsletter" TO "anon";
GRANT ALL ON TABLE "public"."entity_details_newsletter" TO "authenticated";
GRANT ALL ON TABLE "public"."entity_details_newsletter" TO "service_role";



GRANT ALL ON TABLE "public"."entity_details_tool" TO "anon";
GRANT ALL ON TABLE "public"."entity_details_tool" TO "authenticated";
GRANT ALL ON TABLE "public"."entity_details_tool" TO "service_role";



GRANT ALL ON TABLE "public"."entity_tags" TO "anon";
GRANT ALL ON TABLE "public"."entity_tags" TO "authenticated";
GRANT ALL ON TABLE "public"."entity_tags" TO "service_role";



GRANT ALL ON TABLE "public"."entity_types" TO "anon";
GRANT ALL ON TABLE "public"."entity_types" TO "authenticated";
GRANT ALL ON TABLE "public"."entity_types" TO "service_role";



GRANT ALL ON TABLE "public"."review_votes" TO "anon";
GRANT ALL ON TABLE "public"."review_votes" TO "authenticated";
GRANT ALL ON TABLE "public"."review_votes" TO "service_role";



GRANT ALL ON TABLE "public"."reviews" TO "anon";
GRANT ALL ON TABLE "public"."reviews" TO "authenticated";
GRANT ALL ON TABLE "public"."reviews" TO "service_role";



GRANT ALL ON TABLE "public"."tags" TO "anon";
GRANT ALL ON TABLE "public"."tags" TO "authenticated";
GRANT ALL ON TABLE "public"."tags" TO "service_role";



GRANT ALL ON TABLE "public"."user_activity_logs" TO "anon";
GRANT ALL ON TABLE "public"."user_activity_logs" TO "authenticated";
GRANT ALL ON TABLE "public"."user_activity_logs" TO "service_role";



GRANT ALL ON TABLE "public"."user_badges" TO "anon";
GRANT ALL ON TABLE "public"."user_badges" TO "authenticated";
GRANT ALL ON TABLE "public"."user_badges" TO "service_role";



GRANT ALL ON TABLE "public"."user_followed_categories" TO "anon";
GRANT ALL ON TABLE "public"."user_followed_categories" TO "authenticated";
GRANT ALL ON TABLE "public"."user_followed_categories" TO "service_role";



GRANT ALL ON TABLE "public"."user_followed_tags" TO "anon";
GRANT ALL ON TABLE "public"."user_followed_tags" TO "authenticated";
GRANT ALL ON TABLE "public"."user_followed_tags" TO "service_role";



GRANT ALL ON TABLE "public"."user_notification_settings" TO "anon";
GRANT ALL ON TABLE "public"."user_notification_settings" TO "authenticated";
GRANT ALL ON TABLE "public"."user_notification_settings" TO "service_role";



GRANT ALL ON TABLE "public"."user_saved_entities" TO "anon";
GRANT ALL ON TABLE "public"."user_saved_entities" TO "authenticated";
GRANT ALL ON TABLE "public"."user_saved_entities" TO "service_role";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";









ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
