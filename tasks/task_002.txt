# Task ID: 2
# Title: Implement Core Backend API and Authentication
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Develop the NestJS backend with essential API endpoints and integrate Supabase Auth for user authentication.
# Details:
1. Initialize NestJS project with TypeScript
2. Set up Supabase client integration in the backend
3. Implement authentication middleware using Supabase Auth
4. Develop API endpoints for:
   - User registration, login, and profile management
   - CRUD operations for resources, categories, and tags
   - Search and filtering functionality
   - Review submission and retrieval
   - Resource saving/bookmarking
5. Implement error handling and logging
6. Set up request validation using class-validator or similar

# Test Strategy:
1. Unit tests for all service methods
2. Integration tests for API endpoints
3. Authentication flow testing
4. Error handling verification
5. Load testing for search endpoints
6. Security testing for authentication endpoints

# Subtasks:
## 1. Initialize NestJS Project with Supabase Integration [done]
### Dependencies: None
### Description: Set up the NestJS project structure with TypeScript configuration and integrate the Supabase client for database and authentication access.
### Details:
1. Create a new NestJS project using the CLI: `nest new resource-sharing-backend`
2. Configure TypeScript settings in tsconfig.json
3. Install Supabase client: `npm install @supabase/supabase-js`
4. Create a Supabase module and service for dependency injection
5. Set up environment variables for Supabase URL and API keys
6. Implement a connection test endpoint to verify Supabase connectivity

<info added on 2025-05-12T00:37:14.824Z>
For step 2.1.4, here's how to create a Supabase module and service for dependency injection:

Create a dedicated Supabase module:
```typescript
// src/supabase/supabase.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SupabaseService } from './supabase.service';

@Module({
  imports: [ConfigModule],
  providers: [SupabaseService],
  exports: [SupabaseService],
})
export class SupabaseModule {}
```

Implement the Supabase service:
```typescript
// src/supabase/supabase.service.ts
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, SupabaseClient } from '@supabase/supabase-js';

@Injectable()
export class SupabaseService {
  private supabase: SupabaseClient;

  constructor(private configService: ConfigService) {
    this.supabase = createClient(
      this.configService.get<string>('SUPABASE_URL'),
      this.configService.get<string>('SUPABASE_KEY'),
    );
  }

  getClient(): SupabaseClient {
    return this.supabase;
  }
}
```

Import the SupabaseModule in your AppModule:
```typescript
// src/app.module.ts
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SupabaseModule } from './supabase/supabase.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    SupabaseModule,
  ],
})
export class AppModule {}
```
</info added on 2025-05-12T00:37:14.824Z>

<info added on 2025-05-12T01:47:55.500Z>
<info added on 2025-05-12T00:37:14.824Z>
For step 2.1.6, here's how to implement a connection test endpoint:

```typescript
// src/app.controller.ts
import { Controller, Get } from '@nestjs/common';
import { SupabaseService } from './supabase/supabase.service';

@Controller()
export class AppController {
  constructor(private readonly supabaseService: SupabaseService) {}

  @Get('test-connection')
  async testConnection() {
    try {
      // Attempt to query a simple system table to verify connection
      const { data, error } = await this.supabaseService.getClient()
        .from('_prisma_migrations')
        .select('id')
        .limit(1);
      
      if (error) {
        return {
          status: 'error',
          message: 'Failed to connect to Supabase',
          details: error.message
        };
      }
      
      return {
        status: 'success',
        message: 'Successfully connected to Supabase'
      };
    } catch (error) {
      return {
        status: 'error',
        message: 'Failed to connect to Supabase',
        details: error.message
      };
    }
  }
}
```

For step 2.1.5, create a `.env.example` file to document required environment variables:

```
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_KEY=your-supabase-anon-key

# Add other environment variables as needed
PORT=3000
NODE_ENV=development
```

Don't forget to add `.env` to your `.gitignore` file to prevent committing sensitive credentials:

```
# .gitignore
.env
node_modules/
dist/
```
</info added on 2025-05-12T00:37:14.824Z>
</info added on 2025-05-12T01:47:55.500Z>

## 2. Implement Authentication System with Supabase Auth [done]
### Dependencies: 2.1
### Description: Develop authentication controllers, services, and middleware to handle user registration, login, and session management using Supabase Auth.
### Details:
1. Create an AuthModule with controller and service
2. Implement registration endpoint with email/password validation
3. Implement login endpoint returning JWT tokens
4. Create authentication guard middleware to verify tokens
5. Add password reset and email verification endpoints
6. Implement JWT strategy for NestJS Passport integration
7. Create user profile retrieval endpoint for authenticated users
8. Add logout functionality to invalidate sessions

## 3. Develop User and Profile Management API [done]
### Dependencies: 2.2
### Description: Create endpoints for user profile management, including profile updates, preference settings, and account management.
### Details:
1. Create UserModule with controller and service
2. Define user profile DTO (Data Transfer Object) schemas
3. Implement GET /users/me endpoint for current user profile
4. Add PUT /users/me endpoint for profile updates
5. Implement user preference settings endpoints
6. Add account deletion and deactivation functionality
7. Create admin-only endpoints for user management (if required)
8. Implement validation using class-validator for all DTOs

## 4. Implement Resource Management API [done]
### Dependencies: 2.2
### Description: Develop CRUD endpoints for resources, categories, and tags, including proper validation and error handling.
### Details:
1. Create ResourceModule, CategoryModule, and TagModule
2. Define DTOs for resources, categories, and tags
3. Implement CRUD endpoints for resources:
   - POST /resources (create)
   - GET /resources (list with pagination)
   - GET /resources/:id (get single)
   - PUT /resources/:id (update)
   - DELETE /resources/:id (delete)
4. Implement similar CRUD endpoints for categories and tags
5. Add validation using class-validator
6. Implement proper error handling with HTTP exceptions
7. Add authorization checks to ensure users can only modify their own resources

## 5. Develop Search, Filtering and Bookmarking API [done]
### Dependencies: 2.4
### Description: Implement advanced search functionality with filtering options and resource bookmarking capabilities.
### Details:
1. Enhance GET /resources endpoint with query parameters for:
   - Full-text search
   - Category and tag filtering
   - Date range filtering
   - Sorting options
2. Implement efficient query building with Supabase
3. Create BookmarkModule with controller and service
4. Implement bookmark endpoints:
   - POST /bookmarks (save a resource)
   - GET /bookmarks (list saved resources)
   - DELETE /bookmarks/:id (remove bookmark)
5. Add pagination for search results and bookmarks
6. Implement proper indexing recommendations for Supabase

## 6. Implement Review System and Global Error Handling [done]
### Dependencies: 2.4
### Description: Develop the review submission and retrieval system, and implement global error handling and logging.
### Details:
1. Create ReviewModule with controller and service
2. Implement review endpoints:
   - POST /resources/:id/reviews (submit review)
   - GET /resources/:id/reviews (get reviews for resource)
   - PUT /reviews/:id (update review)
   - DELETE /reviews/:id (delete review)
3. Add validation for review content and ratings
4. Implement global exception filter for consistent error responses
5. Set up logging service using NestJS built-in logger or Winston
6. Configure request validation pipes globally
7. Implement rate limiting for API endpoints
8. Add request logging middleware for debugging and monitoring

